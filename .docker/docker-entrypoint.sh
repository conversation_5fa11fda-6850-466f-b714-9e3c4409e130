#! /bin/sh

#initialize working environment

composer install --no-suggest --no-scripts
COUNTRIES=$(psql -U $MAIN_DB_USERNAME -h $MAIN_DB_HOST -qtAX -d gs_main_v5 -c "SELECT iso_alpha_2_code FROM countries WHERE active;")

role=${CONTAINER_ROLE:-app}

for COUNTRY in $COUNTRIES
do
	php artisan migrate --path=/database/migrations/local/ --database=$COUNTRY --force
done

php artisan passport:keys

if [ ! -d "/var/www/html/app/storage/files/exports" ]
then
    mkdir /var/www/html/app/storage/files/exports
fi
chmod -R 777 /home/<USER>/qnap_storage/
chmod -R 777 /tmp/
# chmod 666 /var/run/docker.sock # Not needed

php artisan route:clear
php artisan config:clear
php artisan cache:clear

# Recreates cached config, routes and views
if [ $APP_ENV == 'production' ] || [ $APP_ENV == 'staging' ]
then
    php artisan config:cache 
    php artisan route:cache 
    php artisan view:cache
fi

for COUNTRY in $COUNTRIES
do
    COUNTRY_SMALL=$(echo "$COUNTRY" | tr '[:upper:]' '[:lower:]')

    php artisan satellite:generate-map-files $COUNTRY && php artisan satellite:generate-static-map-files $COUNTRY
    cp /var/www/html/app/storage/maps/symbolset /home/<USER>/qnap_storage/storage_$COUNTRY_SMALL/storage/maps/
done

if [ "$role" = "app" ]; then
    #start php
    exec php-fpm -F

elif [ "$role" = "queue" ]; then
    echo "Running the queue..."
    COUNTRIES_STR=$(psql -U $MAIN_DB_USERNAME -h $MAIN_DB_HOST -qtAX -d gs_main_v5 -c "SELECT STRING_AGG(iso_alpha_2_code, ',') FROM countries WHERE active;")
    php /var/www/html/app/artisan queue:work --queue=$COUNTRIES_STR --verbose --tries=3 --timeout=90
    
elif [ "$role" = "scheduler" ]; then
    echo "Scheduler role"
    while [ true ]
    do
      php /var/www/html/app/artisan schedule:run --verbose --no-interaction &
      sleep 60
    done
else
    echo "Could not match the container role \"$role\""
    exit 1
fi

