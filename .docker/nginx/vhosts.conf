server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /var/www/html/app/public/;
    index index.php;



    location / {
        try_files $uri /index.php?$args;
    }

    location ~ ^/index\.php(/|$) {
        include cors_support;
        fastcgi_pass php-laravel:9000;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
        internal;
    }

    location ~ \.php$ {
        include cors_support;
        return 404;
    }

    error_log /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
}