FROM php:7.4-fpm-alpine3.12 as backend

ARG MAIN_DB_HOST
ARG MAIN_DB_PORT
ARG MAIN_DB_USERNAME
ARG MAIN_DB_PASSWORD

WORKDIR /var/www/html/app

ARG APP_ENV

RUN apk add --no-cache build-base autoconf libpq unixodbc-dev freetype libpng libjpeg-turbo freetype-dev libpng-dev libjpeg-turbo-dev libzip-dev zip openldap-dev php7-pear libxml2-dev

RUN apk add openrc
RUN apk add --update docker openrc
RUN rc-update add docker boot

RUN set -ex \
    apk --no-cache add postgresql-libs postgresql-dev \
    docker-php-ext-install pdo pgsql pdo_pgsql gd zip \
    docker-php-ext-configure pgsql \
    --with-pgsql \
    apk del postgresql-dev

RUN apk add --no-cache \
    icu-dev \
    git \
    autoconf \
    g++ \
    make \
    cmake \
    openssl-dev \
    postgresql-libs \
    postgresql-dev

RUN docker-php-ext-install pdo pdo_pgsql

RUN docker-php-ext-enable pdo_pgsql

RUN apk add --no-cache --update libmemcached-libs zlib
RUN set -xe && \
    cd /tmp/ && \
    apk add --no-cache --update --virtual .phpize-deps $PHPIZE_DEPS && \
    apk add --no-cache --update --virtual .memcached-deps zlib-dev libmemcached-dev cyrus-sasl-dev && \
    pecl install igbinary && \
    ( \
    pecl install --nobuild memcached && \
    cd "$(pecl config-get temp_dir)/memcached" && \
    phpize && \
    ./configure --enable-memcached-igbinary && \
    make -j$(nproc) && \
    make install && \
    cd /tmp/ \
    ) && \
    docker-php-ext-enable igbinary memcached && \
    rm -rf /tmp/* && \
    apk del .memcached-deps .phpize-deps


RUN apk add \
    --repository http://dl-cdn.alpinelinux.org/alpine/v3.6/main \
    --no-cache \
    rabbitmq-c-dev \
    && pecl install amqp \
    && docker-php-ext-enable amqp

RUN  addgroup -g 1000 -S appgroup \
    && adduser -u 1000 -D -S appuser -G appgroup \
    #change the app folder permissions
    && chown -R appuser:appgroup /var/www/html/app \
    && chmod -R g+rwx /var/www/html/app \
    && docker-php-ext-install zip intl opcache gd

RUN apk add --no-cache --repository=http://dl-cdn.alpinelinux.org/alpine/edge/testing mono

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer --version=1.10.16

COPY ./.docker/php/php.ini /usr/local/etc/php/php.ini
COPY ./.docker/php/www.conf /usr/local/etc/php-fpm.d/www.conf
COPY ./.docker/crontab/crontab /etc/crontabs/root
COPY ./.docker/docker-entrypoint.sh /usr/local/bin
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# [START] Aworkaround for wkhtmltopdf untill https://github.com/wkhtmltopdf/packaging/issues/2 is fixed.
# Install dependencies for wkhtmltopdf
RUN apk --update --no-cache add \
    wkhtmltopdf \
    libgcc \
    libstdc++ \
    musl \
    qt5-qtbase \
    qt5-qtbase-x11 \
    qt5-qtsvg \
    qt5-qtwebkit \
    ttf-freefont \
    ttf-dejavu \
    ttf-droid \
    ttf-liberation \
    ttf-ubuntu-font-family \
    fontconfig

# Add openssl dependencies for wkhtmltopdf
RUN echo 'http://dl-cdn.alpinelinux.org/alpine/v3.8/main' >> /etc/apk/repositories && \
    apk add --no-cache libcrypto1.0 libssl1.0

RUN apk add postgresql-client --no-cache --repository=http://dl-cdn.alpinelinux.org/alpine/v3.8/main

# Copy wkhtmltopdf with patched qt
COPY --from=technofarm/wkhtmltopdf /bin/wkhtmltopdf /usr/bin/wkhtmltopdf

# [END]
EXPOSE 9000
# Allow local connections
RUN echo "$MAIN_DB_HOST:$MAIN_DB_PORT:*:$MAIN_DB_USERNAME:$MAIN_DB_PASSWORD" > ~/.pgpass \
    && chmod 0600 ~/.pgpass 
    # && chown appuser:appgroup ~/.pgpass
#change the user to execute any further composer commands with appuser
USER appuser

COPY --chown=appuser:appgroup ./composer.json composer.json
#COPY --chown=appuser:appgroup ./composer.lock composer.lock
COPY --chown=appuser:appgroup . .

RUN composer install --no-suggest --no-scripts $(if [ "$APP_ENV" == "staging" ] || [ "$APP_ENV" == "production" ]; then echo "--no-dev"; fi)

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]