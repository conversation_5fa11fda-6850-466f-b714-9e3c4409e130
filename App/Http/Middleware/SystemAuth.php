<?php

namespace App\Http\Middleware;

use App\Models\ConfigParamValue;
use App\Models\Country;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Config;
use Closure;
use App\Exceptions\ForbiddenException;
use \Firebase\JWT\JWT;

class SystemAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!$request->bearerToken()) {
            throw new ForbiddenException();
        }

        try {
            $publicKey = file_get_contents(config('globals.JWT_SYSTEM_PUBLIC_KEY'));
            $algorithm = config('globals.JWT_SYSTEM_ALGORITHM');
            $data = JWT::decode($request->bearerToken(), $publicKey, array($algorithm));
            $data = (array) $data;
            $request->merge($data);

        }catch (\Exception $e){
            return response('Unauthorized.', 401);
        }

        if (!isset($data['country'])) {
            return response('Country code is required.', 400);
        }

        $country = Country::where('iso_alpha_2_code', strtoupper($data['country']))->get()->first();

        /** @var ConfigParamValue[] $configParams */
        $country->configParams->loadMissing('configParam');
        $configParams = $country->configParams;
        foreach ($configParams as $each) {
            Config::set($each->configParam->domain. '.' . $each->configParam->name, $each->value);
        }

        App::useStoragePath(Config::get('globals.STORAGE_PATH'));

        Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));

        return $next($request);
    }
}
