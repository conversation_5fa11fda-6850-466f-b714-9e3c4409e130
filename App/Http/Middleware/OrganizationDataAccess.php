<?php

namespace App\Http\Middleware;

use Auth;
use Closure;
use App\Exceptions\ForbiddenException;
use Exception;

class OrganizationDataAccess
{
    
    public function handle($request, Closure $next)
    {
        $organizationId = $request->get('organization_id');
        $user = Auth::user();

        $userOrganizations = $user->organizations->toArray();
        $userOrganizations = array_column($userOrganizations, 'id');

        if(!in_array($organizationId, $userOrganizations)) {
            throw new ForbiddenException('organization_no_data_access');
        }

        return $next($request);
    }
}
