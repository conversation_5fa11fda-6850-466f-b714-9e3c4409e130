<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Http\Middleware;

use App\Models\RequestLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class RequestLogger
{
    private $start;
    private $end;

    public function handle(Request $request, Closure $next)
    {
        $this->start = microtime(true);

        return $next($request);
    }

    public function terminate(Request $request, $response)
    {
        $this->end = microtime(true);

        $this->log($request, $response);
    }

    private function log(Request $request, $response)
    {

        // Log::channel("gelf")->debug("Request Log", [
        //     "service_provider" => Auth::user()->globalUser()->service_provider_id,
        //     "ip" => $request->getClientIp(),
        //     "url" => empty($request->getBaseUrl())?$request->fullUrl():$request->getBaseUrl(),
        //     // "full_url" => ,
        //     "method" => $request->getMethod(),
        //     "status" => $response->getStatusCode(),
        //     "duration" => number_format(($this->end - $this->start)*1000,0),
        //     "user_id" => Auth::user() ? Auth::user()->globalUser()->id : null,
        //     "request" => json_encode($request->all()),
        // ]);

    }
}
