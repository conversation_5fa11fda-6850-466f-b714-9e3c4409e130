<?php

namespace App\Http\Middleware;

use App\Models\ConfigParamValue;
use App\Models\Country;
use Auth;
use Config;
use Closure;
use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class SetOrganizationData
{

    public function handle($request, Closure $next)
    {
        $user = Auth::user();

        if (!$user->lastChosenOrganization && count($user->organizations)) {
            $user->lastChosenOrganization()->associate($user->organizations->first());
            $user->save();
        }

        $country = $user->globalUser()->country()->first();

        /** @var ConfigParamValue[] $configParams */
        $country->configParams->loadMissing('configParam');
        $configParams = $country->configParams;
        foreach ($configParams as $each) {
            Config::set($each->configParam->domain. '.' . $each->configParam->name, $each->value);
        }

        App::useStoragePath(Config::get('globals.STORAGE_PATH'));

        Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));

        return $next($request);
    }
}
