<?php

namespace App\Http\Middleware;

use Auth;
use Closure;
use App\Exceptions\ForbiddenException;
use Exception;

class ReduceGDDEntriesLeft
{
    
    public function handle($request, Closure $next)
    {
        if(!Auth::user()->gdd_entries_left > 0) {
            // 
            throw new ForbiddenException('no_entries_left');
        } else {
            if($request->get('sowing_date') == '2017-10-01' && $request->get('crop') == '-' ) {

                Auth::user()->gdd_entries_left--;
                Auth::user()->save();
            }

        }

        return $next($request);
    }
}
