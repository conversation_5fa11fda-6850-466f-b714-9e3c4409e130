<?php

namespace App\Http\Controllers\CMS;

use Illuminate\Http\Request;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Validator;
use App\Services\Organization\OrganizationService;
use App\Models\Organization;
use App\Exceptions\ValidationException;

class OrganizationController extends BaseController
{
    private $organizationService;

    public function __construct(OrganizationService $organizationService)
    {
        $this->organizationService = $organizationService;
    }

    /**
     * @OA\Get(
     *     path="/cms/organizations/list",
     *     summary="List for Contracts API",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return Organization[]|\Illuminate\Database\Eloquent\Collection
     */
    public function list(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'identityNumber' => 'string',
            'organizationManagerId' => 'integer',
            'serviceManagerId' => 'integer',
            'page' => 'integer',
            'limit' => 'integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $identityNumber = $request->get('identityNumber');
        $organizationManagerId = $request->get('organizationManagerId');
        $serviceManagerId = $request->get('serviceManagerId');
        $page = $request->get('page');
        $limit = $request->get('limit');

        return $this->organizationService->getListForCms($identityNumber, $organizationManagerId, $serviceManagerId, $page, $limit);
    }

    public function search(Request $request)
    {
        $query = $request->get('q');
        $limit = $request->get('limit', 10);

        $result = Organization::search($query, $limit);
        return $result;
    }

    public function getOrganization(int $id, Request $request)
    {
        $oganization = Organization::select()->where('id', $id)->get();

        return $oganization;
    }
}
