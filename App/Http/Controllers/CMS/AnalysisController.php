<?php

namespace App\Http\Controllers\CMS;

use App\Classes\CMS\AnalysisReportService;
use App\Classes\CMS\AnalysisService;
use App\Exceptions\ValidationException;
use App\Services\Common\MailService;
use Auth;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Validator;

class AnalysisController
{
    private $analysisService;
    private $analysisReportService;
    private $mailService;

    public function __construct(AnalysisService $analysisService, MailService $mailService, AnalysisReportService $analysisReportService
    )
    {
        $this->analysisService = $analysisService;
        $this->mailService = $mailService;
        $this->analysisReportService = $analysisReportService;
    }

    /**
     * @param Request $request
     * @param string|null $contractId
     * @return mixed
     * @throws ValidationException
     */
    public function updateLabElementsResultStates(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'approved' => 'array',
            'approved.*' => 'integer',
            'for_reanalysis' => 'array',
            'for_reanalysis.*' => 'integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $data = $request->all();
        $this->analysisService->updateLabElementsResultStates($data);

        return new JsonResponse(null, 200);
    }

    /**
     * @OA\Get(
     *     path="/cms/analysis/lab-elements/soil-map",
     *     summary="Plots samples",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getSoilMapElements(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
            'filter.meta_groups' => 'sometimes|required|json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->all();
        $result = $this->analysisService->getSoilMapElements($headerParams);

        return new JsonResponse($result, 200);
    }

    /**
     * @return JsonResponse
     */
    public function getMetaElementsGroups()
    {
        $metaElementsGroupsList = $this->analysisService->getMetaElementsGroupsList();
        return new JsonResponse($metaElementsGroupsList, 200);
    }

    /**
     * @return JsonResponse
     */
    public function getLabElementsResults(Request $request)
    {
        $params = $request->all();
        $labElementsResults = $this->analysisService->getLabElementsResults($params);
        return new JsonResponse($labElementsResults, 200);
    }

    /**
     * @OA\GET(
     *     path="/cms/barcodes",
     *     summary="Get barcodes",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getBarcodes(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'barcode_number' => 'sometimes|required|string',
            'customer_identification' => 'sometimes|required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $queryParams = $request->all();

        if (!isset($queryParams['customer_identification'])) {
            $queryParams['customer_identification'] =
                Auth::user()->lastChosenOrganization->identity_number;
        }

        $result = $this->analysisService->getBarcodes($queryParams);
        return new JsonResponse($result, 200);
    }

    /**
     * @OA\GET(
     *     path="/cms/lab_numbers",
     *     summary="Get lab numbers",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getLabNumbers(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'lab_number' => 'sometimes|required|string',
            'customer_identification' => 'sometimes|required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $queryParams = $request->all();

        if (!isset($queryParams['customer_identification'])) {
            $queryParams['customer_identification'] =
                Auth::user()->lastChosenOrganization->identity_number;
        }

        $result = $this->analysisService->getLabNumbers($queryParams);
        return new JsonResponse($result, 200);
    }

    /**
     * @OA\GET(
     *     path="/cms/analysis/report",
     *     summary="Get analyses report by given subscription package",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'filter' => 'required|array',
            'filter.subscription_packages_id' => 'required|json',
            'filter.plot_ids' => 'sometimes|required|json',
            'filter.barcode' => 'sometimes|required|string',
            'filter.lab_number' => 'sometimes|required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $queryParams = $request->all();
        $result = $this->analysisReportService->getAnalysesReport($queryParams);

        return new JsonResponse($result, 200);
    }

    /**
     * @OA\Get(
     *     path="/cms/analysis/report/export
     *     summary="Export recommendations report in .xls format.",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     *     )
     * )
     *
     * @param Request $request
     * @return BinaryFileResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function exportReport(Request $request): BinaryFileResponse
    {
        $requestData = $request->all();
        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'filter' => 'required|array',
            'filter.subscription_packages_id' => 'required|json',
            'filter.plot_ids' => 'sometimes|required|json',
            'filter.barcode' => 'sometimes|required|string',
            'filter.lab_number' => 'sometimes|required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $reportFilePath = $this->analysisReportService->exportXLSAnalysesReport($headerParams);
        $customFileName = uniqid('analyses_report_', true) . '.xls';

        return response()->download($reportFilePath, $customFileName)->deleteFileAfterSend(true);
    }

    /**
     * @OA\Get(
     *     path="/cms/analysis/report/send
     *     summary="Send recommendations report in .xls format to specific email",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     *     )
     * )
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function sendReport(Request $request): JsonResponse
    {
        $requestData = $request->all();
        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'filter' => 'required|array',
            'filter.subscription_packages_id' => 'required|json',
            'filter.plot_ids' => 'sometimes|required|json',
            'filter.barcode' => 'sometimes|required|string',
            'filter.lab_number' => 'sometimes|required|string',
            'email' => 'required|email',
            'subject' => 'sometimes|string',
            'message' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $email = $request->get('email');
        $subject = $request->get('subject', 'GeoSCAN analyses report');
        $message = $request->get('message', '');
        $title = trans('emailReport.analysesReport');

        $reportFilePath = $this->analysisReportService->exportXLSAnalysesReport($headerParams);
        $customFileName = uniqid('analyses_report_', true) . '.xls';

        $this->mailService->sendFileToEmail($email, $title, $subject, $message, $reportFilePath, $customFileName);
        unlink($reportFilePath);

        return new JsonResponse('Success', 200);
    }
}
