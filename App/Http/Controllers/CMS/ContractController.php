<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\CMS;


use App\Classes\CMS\ContractService;
use App\Classes\CMS\PackageFieldService;
use App\Classes\CMS\PackageService;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Ability;
use App\Models\Integration;
use App\Models\Plot;
use App\Models\UserStation;
use Auth;
use Config;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ContractController extends BaseController
{
    private $contractService;
    private $packageService;
    private $packageFieldService;

    public function __construct(ContractService $contractService, PackageService $packageService, PackageFieldService $packageFieldService)
    {
        $this->contractService = $contractService;
        $this->packageService = $packageService;
        $this->packageFieldService = $packageFieldService;
    }

    public function list(Request $request)
    {
        $requestData = $request->all();

        $validator = Validator::make($requestData, [
            'organization_identity_number' => 'required|string',
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
            'sort' => 'array'

        ]);

        if($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        return $this->contractService->list($requestData);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function listContractsShortDataForChosenOrg(Request $request)
    {
        $identityNumber = null;

        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
        ]);

        if($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $response = $this->contractService->getContractsShortDataByIdentityNumber($headerParams);
        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function listContractsShortDataByOrganizationsAndPackages(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'sometimes|required|integer',
            'offset' => 'sometimes|required|integer',
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $data = $this->contractService->getContractsShortDataByOrganizations($headerParams);

        $response = [
            'items' => $data['items'],
            'total' => $data['total'],
        ];

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function manageFields(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'plot_ids' => 'required|array',
            'plot_ids.*' => 'integer',
            'packages' => 'required|array',
            'packages.*.id' => 'required|integer',
            'packages.*.type' => 'required|in:service,subscription'
        ]);

        if($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotIds = $request->get('plot_ids');
        $plots = Plot::whereIn('gid', $plotIds)->get();
        $errors = [];

        $packages = $request->get('packages');

        foreach($packages as $package) {
            try {
                if ($package['type'] == 'subscription') {
                    $this->contractService->addFieldsToSubscription($plots, $package["id"]);
                }

                if ($package['type'] == 'service') {
                    $this->contractService->addFieldsToService($plots, $package["id"]);
                }
            } catch (Exception $e) {
                $errors[] = $package["id"];
            }
        }

        if (!empty($errors)) {
            return response()->json($errors, 400);
        }

        return response()->json("success");
    }

    // TODO:: GPS-1540 change endpoint to be contracts/{contractId}/packages//{type?}/
    public function listPackagesByContract(Request $request, string $contractId, string $type = null): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
            'filter' => 'sometimes|required|array'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $headerParams = $request->all();

        if ($type) {
            $response = $this->contractService->getCmsDataForPackagesByContractIdAndFilter($type, $headerParams, $contractId);
            return new JsonResponse($response, 200, ['Content-Type: application/json']);
        }

        $response = $this->contractService->getAllServiceOrSubscriptionPackagesByContractId($contractId, $headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function listPackagesForCard(Request $request, string $orgIdentityNumber)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1'
        ]);

        if($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $response = $this->contractService->listPackagesForCard($request->all(), $orgIdentityNumber);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function getAllOrdersIdByContractId($contractId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $queryParams = $request->all();
        return $this->contractService->getAllOrdersIdByContractId($contractId, $queryParams);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function countContractsAmount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $response = $this->contractService->getCountAmountsForContracts($headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function getCardData()
    {
        $cards = [];
        $organizations = Auth::user()->organizations()->where('active', true)->whereNotNull('identity_number')->get();
        $organizationIdentityNumbers = json_encode(array_column($organizations->toArray(), 'identity_number'));
        $organizationIds = array_column($organizations->toArray(), 'id');

        $allCardsData = $this->contractService->getCardsData($organizationIdentityNumbers);

        if (Auth::user()->globalUser()->can(Ability::MANAGE_FIELDS)) {
            $cards[] = [
                'cardTitle' => 'Manage fields',
                'cardRout' => 'manage-fields',
                'staticTitle' => 'Manage fields',
                'availableItems' => null,
                'allItems' => ''
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::ADD_PLOTS_TO_CONTRACT)) {
            $cards[] = [
                'cardTitle' => 'Add plots to contract',
                'cardRout' => 'add-plots-to-contract',
                'staticTitle' => 'Contracts',
                'availableItems' => $allCardsData['add_plots_card']['available_items'],
                'allItems' => $allCardsData['add_plots_card']['all_items']
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::SELECT_FOR_SAMPLING)) {
            $cards[] = [
                'cardTitle' => 'Select for sampling',
                'cardRout' => 'select-for-sampling',
                'staticTitle' => 'Fields',
                'availableItems' => $allCardsData['select_for_sampling_card']['available_items'],
                'allItems' => $allCardsData['select_for_sampling_card']['all_items']
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::MANAGE_WEATHER_STATIONS)) {
            $stationContractsId = $allCardsData['manage_weather_stations_card']['station_contracts_id'];

            $cards[] = [
                'cardTitle' => 'Manage weather stations',
                'cardRout' => 'manage-stations',
                'staticTitle' => 'Stations',
                'availableItems' => $stationContractsId ? UserStation::getByOrganizationsAndContracts($stationContractsId)->count() : 0,
                'allItems' => $allCardsData['manage_weather_stations_card']['all_items']
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::MANAGE_FARM_TRACK_INTEGRATIONS)) {
            $cards[] = [
                'cardTitle' => 'FT integrations',
                'cardRout' => 'integrations/farm-track',
                'staticTitle' => 'Farm track',
                'availableItems' => Integration::getLinkedQuery($organizationIds, 'FT')->get()->count(),
                'allItems' => $allCardsData['ft_integrations_card']['all_items']
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::MANAGE_IRRIGATION_MONITORING_INTEGRATIONS)) {
            $cards[] = [
                'cardTitle' => 'IM integrations',
                'cardRout' => 'integrations/irrigation-mngt',
                'staticTitle' => 'Irrigation mgmt',
                'availableItems' => Integration::getLinkedQuery($organizationIds, 'IM')->get()->count(),
                'allItems' => $allCardsData['im_integrations_card']['all_items']
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::APPROVE_RESULTS)) {
            $cards[] = [
                'cardTitle' => 'Approve results',
                'cardRout' => '/admin/approve-results',
                'staticTitle' => 'Results for approve',
                'availableItems' => null,
                'allItems' => $allCardsData['manage_results_for_approve']['all_items']
            ];
        }

        if (Auth::user()->globalUser()->can(Ability::MANAGE_AGRONOMIC_TASKS)) {
            $cards[] = [
                'cardTitle' => 'Agronomic tasks',
                'cardRout' => 'agronomic-tasks',
                'staticTitle' => 'Agronomic tasks',
                'availableItems' => null,
                'allItems' => $allCardsData['manage_results_for_agronomic_tasks']['all_items']
            ];
        }

        return new JsonResponse($cards, 200, ['Content-Type: application/json']);
    }

    public function getContractsStationsAmountByOrganizations(Request $request)
    {
        $withPagination = $request->get('withPagination', true);
        $offset = $request->get('offset', 0);
        $limit = $request->get('limit', 6);
        $customerIdentification = $request->get('customer_identification', null);

        $organizations = Auth::user()->organizations()->with('stations')->where('active', true)->whereNotNull('identity_number');
        if($customerIdentification) {
            $organizations->where('identity_number', $customerIdentification);
        }

        $organizations = $organizations->distinct()->get();
        $customerIdentifications = array_column($organizations->toArray(), 'identity_number');
        $stationsAmounts = $this->contractService->getStationsAmountByOrganizations($customerIdentifications, $withPagination, $offset, $limit);

        $stationsAmounts['items'] = array_map(function($item) use ($organizations) {
            $organizationIndex = array_search($item['customerIdentification'], array_column($organizations->toArray(), 'identity_number'));
            $organization = $organizations[$organizationIndex];
            $stations = array_filter($organization['stations']->toArray(), function ($station) use ($item) {
                return $station['contract_id'] == $item['contractId'];
            });

            $item['organizationName'] = $organization['name'];
            $item['organizationId'] = $organization['id'];
            $item['availableStations'] = count($stations);

            return $item;
        }, $stationsAmounts['items']);

        return new JsonResponse($stationsAmounts, 200, ['Content-Type: application/json']);
    }

    public function getIntegrationCardList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_slug' => 'required|string',
            'offset' => 'integer',
            'limit' => 'integer',
            'customer_identification' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $offset = $request->get('offset');
        $limit = $request->get('limit');
        $customerIdentification = $request->get('customer_identification');
        $packageSlug = $request->get('package_slug');

        $organizations = Auth::user()->organizations()->with('stations')->where('active', true)->whereNotNull('identity_number');
        if($customerIdentification) {
            $organizations->where('identity_number', $customerIdentification);
        }

        $organizations = $organizations->distinct()->get();
        $customerIdentifications = array_column($organizations->toArray(), 'identity_number');

        $list = $this->contractService->getIntegrationCardList($packageSlug, $offset, $limit, $customerIdentifications);

        $result['items'] = array_map(function($item) use ($organizations) {
            $organizationIndex = array_search($item['customerIdentification'], array_column($organizations->toArray(), 'identity_number'));
            $organization = $organizations[$organizationIndex];

            $item['organizationName'] = $organization['name'];
            $item['organizationId'] = $organization['id'];

            return $item;
        }, $list['items']);

        $result['total'] = $list['total'];

        return $result;
    }

    public function getPlotsOverlapByContract(Request $request, int $contractId)
    {
        $validator = Validator::make($request->all(), [
            'plot_uuids' => 'json',
            'organization_id' => 'integer',
            'lang' => 'string|min:2|max:2'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotUuids = json_decode($request->get('plot_uuids', '[]'));
        $lang = $request->get('lang') ? $request->get('lang') : Config::get('app.locale');
        $organizationId = $request->get('organization_id', Auth::user()->lastChosenOrganization->id);

        $plots = $this->contractService->getPlotsOverlapBySubscriptionContract($plotUuids, $contractId, $organizationId, $lang);
       
        return new JsonResponse($plots, 200, ['Content-Type: application/json']);
    }
}