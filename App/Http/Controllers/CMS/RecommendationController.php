<?php

namespace App\Http\Controllers\CMS;


use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Crop;
use App\Services\Common\MailService;
use App\Services\Recommendations\RecommendationEChartsService;
use App\Services\Recommendations\RecommendationPrintService;
use App\Services\Recommendations\RecommendationService;
use Auth;
use Config;
use DB;
use Excel;
use Exception;
use File;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class RecommendationController extends BaseController
{
    private $recommendationService;
    private $recommendationEChartsService;
    private $recommendationPrintService;
    private $mailService;


    public function __construct(
        RecommendationService        $recommendationService,
        RecommendationEChartsService $recommendationEChartsService,
        RecommendationPrintService   $recommendationPrintService,
        MailService                  $mailService
    )
    {
        $this->recommendationService = $recommendationService;
        $this->recommendationEChartsService = $recommendationEChartsService;
        $this->recommendationPrintService = $recommendationPrintService;
        $this->mailService = $mailService;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function list(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
            'sort' => 'array',
            'filter' => 'sometimes|required|array',
            'array_result' => 'sometimes|required|bool'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $arrayResult = $request->get('array_result', true);
        $headerParams = $request->request->all();

        $params = $this->recommendationService->list($headerParams, $arrayResult);

        return new JsonResponse($params, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Get(
     *     path="/cms/recommendation/plot/{uuid}",
     *     summary="List recommendations by plot and farming year",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     *      )
     * )
     *
     *
     */
    public function listByPlot(string $plotUuid, Request $request)
    {
        $validator = Validator::make(array_merge_recursive($request->all(), ['plot_uuid' => $plotUuid]), [
            'plot_uuid' => 'required|string',
            'farm_year' => 'required|integer',
            'status' => ['sometimes', 'max:255', "in:For approve,Declined,Delivered"],
            'lang' => 'sometimes|string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $response = $this->recommendationService->listByPlot($plotUuid, $headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Get(
     *     path="/cms/recommendations/report
     *     summary="Get recommendations report.",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     *     )
     * )
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'group_by' => 'required|string',
            'order_by' => 'sometimes|json',
            'filter' => 'sometimes|required|array',
            'filter.start_date' => 'sometimes|required_with:start_date|integer',
            'filter.end_date' => 'sometimes|required_with:end_date|integer',
            'filter.farm_ids' => 'sometimes|required|json',
            'filter.plot_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $headerParams = $request->request->all();

        $response = $this->recommendationService->getReportData($headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Get(
     *     path="/cms/recommendations/report/{type}
     *     summary="Export recommendations report in .pdf or .xls format.",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     *     )
     * )
     *
     * @param Request $request
     * @param string $type
     * @return BinaryFileResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function exportReport(Request $request, string $type): BinaryFileResponse
    {
        $requestData = $request->all();
        $requestData["type"] = $type;
        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'type' => 'required|string|in:xls,pdf',
            'group_by' => 'required|string',
            'order_by' => 'sometimes|json',
            'filter' => 'required|array',
            'filter.start_date' => 'sometimes|required_with:start_date|integer',
            'filter.end_date' => 'sometimes|required_with:end_date|integer',
            'filter.farm_ids' => 'sometimes|required|json',
            'filter.plot_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $filter = $request->get('filter');

        $reportFilePath = $this->recommendationService->getExportReportData($type, $headerParams);
        $customFileName = 'recommendation_report_' . $filter['start_date'] . '-' . $filter['end_date'] . '.' . $type;

        return response()->download($reportFilePath, $customFileName)->deleteFileAfterSend(true);
    }

    /**
     * @OA\Get(
     *     path="/cms/recommendations/report/{type}/send
     *     summary="Send recommendations report in .pdf or .xls format. to specific email",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     *     )
     * )
     *
     * @param Request $request
     * @param string $type
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function sendReport(Request $request, string $type): JsonResponse
    {
        $requestData = $request->all();
        $requestData["type"] = $type;
        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'type' => 'required|string|in:xls,pdf',
            'group_by' => 'required|string',
            'order_by' => 'sometimes|json',
            'filter' => 'required|array',
            'filter.start_date' => 'sometimes|required_with:start_date|integer',
            'filter.end_date' => 'sometimes|required_with:end_date|integer',
            'filter.farm_ids' => 'sometimes|required|json',
            'filter.plot_ids' => 'sometimes|required|json',
            'email' => 'required|email',
            'subject' => 'sometimes|string',
            'message' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $headerParams = $request->request->all();
        $filter = $request->get('filter');
        $email = $request->get('email');
        $subject = $request->get('subject', 'GeoSCAN recommendation report');
        $message = $request->get('message', '');
        $title = trans('emailReport.recommendationReport');

        $reportFilePath = $this->recommendationService->getExportReportData($type, $headerParams);
        $customFileName = 'recommendation_report_' . $filter['start_date'] . '-' . $filter['end_date'] . '.' . $type;

        $this->mailService->sendFileToEmail($email, $title, $subject, $message, $reportFilePath, $customFileName);
        unlink($reportFilePath);

        return new JsonResponse('Success', 200);
    }

    /**
     * @param Request $request
     * @param int $subscriptionPackageFieldId
     *
     * @return JsonResponse
     */
    public function getRecommendationLabResults(int $subscriptionPackageFieldId, Request $request)
    {
        $response = $this->recommendationService->getRecommendationLabResults($subscriptionPackageFieldId);
        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function getModels(Request $request)
    {
        $response = $this->recommendationService->getRecommendationModels();
        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function listByLastChosenOrganization(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
            'sort' => 'array',
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();

        $response = $this->recommendationService->listByLastChosenOrganization($headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @param Request $request
     * @return mixed
     */
    public function getCrops(Request $request)
    {
        $params = $request->request->all();
        $locale = (isset($params['locale']) ? $params['locale'] : Config::get('app.locale'));
        $crop_name = Config::get('globals.CROP_NAME');
        $crop_name_with_locale = $crop_name . '_' . $locale;
        $query = mb_strtolower((isset($params['q']) ? $params['q'] : ''), 'UTF-8');

        $crops = $this->recommendationService->getRecommendationsCrops();

        return Crop::select(['id', 'crop_code as code', '' . $crop_name_with_locale . ' as name', 'priority'])
            ->where($crop_name_with_locale, 'ILIKE', '%' . $query . '%')
            ->whereIn('id', array_column($crops, 'id'))
            ->orderBy($crop_name_with_locale, 'asc')
            ->get()
            ->toArray();
    }

    /**
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function getStatusAnalysisECharts(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'plot_ids' => 'json',
            'farm_ids' => 'json',
            'farm_year' => 'required|integer',
            'crop_ids' => 'json',
            'sort' => 'array',
            'sort.*' => 'string|in:asc,desc',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $lang = $request->get('lang');
        $sort = $request->get('sort', []);

        $filters["plot_ids"] = json_decode($request->get('plot_ids', "[]"));
        $filters["farm_ids"] = json_decode($request->get('farm_ids', "[]"));
        $filters["crop_ids"] = json_decode($request->get('crop_ids', "[]"));
        $filters["farm_year"] = json_decode($request->get('farm_year'));

        try {
            $result = $this->recommendationEChartsService->renderStatusAnalysisECharts($lang, $filters, $sort);
        } catch (Exception $e) {
            throw $e;
        }

        return $result;
    }

    /**
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function getVraAnalysisECharts(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'plot_ids' => 'json',
            'plot_name' => 'string',
            'farm_ids' => 'json',
            'date' => 'date_format:Y-m-d',
            'farm_year' => 'required|integer',
            'crop_ids' => 'json',
            'sort' => 'array',
            'sort.*' => 'string|in:asc,desc',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $lang = $request->get('lang');
        $sort = $request->get('sort', []);

        $filters["plot_ids"] = json_decode($request->get('plot_ids', "[]"));
        $filters["farm_ids"] = json_decode($request->get('farm_ids', "[]"));
        $filters["crop_ids"] = json_decode($request->get('crop_ids', "[]"));
        $filters["farm_year"] = json_decode($request->get('farm_year'));

        try {
            $result = $this->recommendationEChartsService->renderVraAnalysisECharts($lang, $filters, $sort);
        } catch (Exception $e) {
            throw $e;
        }

        return $result;
    }

    /**
     * @param Request $request
     * @return array
     * @throws Exception
     */
    public function getSoilAnalysisContracts(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'farm_year' => 'required|integer',
            'plot_uuid' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        try {
            $result = $this->recommendationService->getRecommendationsSoilContractList($headerParams);
        } catch (Exception $e) {
            throw $e;
        }

        return $result;
    }

    /**
     * @OA\Get(
     *     path="/cms/recommendations/client/soil-analysis/echarts",
     *     summary="Plots samples",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     *
     * @return array
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getSoilAnalysisECharts(Request $request): array
    {
        $validator = Validator::make($request->all(), [
            'plot_ids' => 'json',
            'plot_name' => 'string',
            'farm_ids' => 'json',
            'date' => 'date_format:Y-m-d',
            'farm_year' => 'required|integer',
            'crop_ids' => 'json',
            'status' => 'json',
            'package_type' => 'json',
            'organization_id' => 'sometimes|integer',
            'filter' => 'sometimes|required|array',
            'filter.meta_groups' => 'sometimes|required|json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $filters["plot_ids"] = json_decode($request->get('plot_ids', "[]"));
        $filters["farm_ids"] = json_decode($request->get('farm_ids', "[]"));
        $filters["crop_ids"] = json_decode($request->get('crop_ids', "[]"));
        $filters["status"] = json_decode($request->get('status', "[]"));
        $filters["package_type"] = json_decode($request->get('package_type', "[]"));
        $filters["farm_year"] = $request->get('farm_year');
        $filters["date"] = $request->get('date');
        $filters["organization_id"] = $request->get('organization_id');

        $queryParams['filter'] = $request->get('filter', []);

        try {
            $result = $this->recommendationEChartsService->renderSoilAnalysisECharts($filters, $queryParams);
        } catch (Exception $e) {
            throw $e;
        }

        return $result;
    }

    /**
     * @param int $id
     * @param Request $request
     *
     * @return JsonResponse
     * @throws GuzzleException
     *
     * @throws ValidationException
     */
    public function getRecommendationById(int $id, Request $request)
    {
        $validator = Validator::make([
            'id' => $id
        ], [
            'id' => 'required|integer'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $response = $this->recommendationService->getRecommendationById($id);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function getRecommendationCommentsRecommendationById(int $id, Request $request)
    {
        $validator = Validator::make(
            ['id' => $id, 'type' => $request->get('type', '')],
            ['id' => 'required|numeric', 'type' => 'sometimes|string|in:lab-results,recommendation-data']
        );

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $response = $this->recommendationService->getRecommendationCommentsRecommendationById($id, $request->get('type'));

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function getRecommendationVra(int $id, Request $request)
    {
        $validator = Validator::make(
            ['id' => $id],
            ['id' => 'required|numeric']
        );

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $headerParams = $request->request->all();
        $response = $this->recommendationService->getRecommendationVra($id, $headerParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function getRecommendationLabElementsResultsById(int $id, Request $request)
    {
        $validator = Validator::make(
            ['id' => $id],
            ['id' => 'required|numeric']
        );

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $response = $this->recommendationService->getRecommendationLabElementsResultsById($id, $request->get('type'));

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function getCalculations(int $subscriptionPackageFieldId, Request $request)
    {

        $validator = Validator::make(array_merge($request->all(), [
            'subscription_package_field_id' => $subscriptionPackageFieldId
        ]), [
            'subscription_package_field_id' => 'required|integer',
            'crop_id' => 'required|integer',
            'model_id' => 'required|integer',
            'humus' => 'required|numeric',
            'yield' => 'required|numeric'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $params = $request->all();
        $response = $this->recommendationService->getCalculations($subscriptionPackageFieldId, $params);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subscription_package_field_id' => 'required|integer',
            'plot_name' => 'required|string',
            'crop_id' => 'required|integer',
            'model_id' => 'required|integer',
            'humus' => 'required|numeric',
            'yield' => 'required|numeric',
            'results' => 'required|array',
            'results.*.result_element' => 'required|string',
            'results.*.result_element_value' => 'required|numeric',
            'comments' => 'required|array',
            'comments.*.result_element' => 'string|nullable',
            'comments.*.comment_text' => 'required|string',
            'orders' => 'array'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $response = $this->recommendationService->storeRecommendation($request->all());

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @param int $id
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function update(int $id, Request $request)
    {
        $validator = Validator::make(array_merge($request->all(), [
            'id' => $id
        ]), [
            'id' => 'required|integer',
            'results' => 'required|array',
            'results.*.id' => 'required|integer',
            'results.*.result_element_value' => 'required|numeric',
            'comments' => 'required|array',
            'comments.*.id' => 'integer|nullable',
            'comments.*.comment_text' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        try {
            $params = $request->all();
            $response = $this->recommendationService->update($id, $params);
        } catch (Exception $e) {
            return new JsonResponse($e->getMessage(), 200, ['Content-Type: application/json']);
        }

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function updateState(int $id, Request $request): JsonResponse
    {
        $validator = Validator::make(
            [
                'id' => $id,
                'state' => $request->get('state')
            ],
            [
                'id' => 'required|numeric',
                'state' => 'required|string'
            ]
        );


        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        try {
            $response = $this->recommendationService->updateState($id, $request->get('state'));
        } catch (Exception $e) {
            return new JsonResponse($e->getMessage(), 200, ['Content-Type: application/json']);
        }

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * @param Request $request
     * @param int $id The recommendation id
     * 
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function exportRecommendationToPdf(Request $request, int $id)
    {
        $validator = Validator::make([
            'id' => $id
        ], [
            'id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $recommendationPDF = $this->recommendationPrintService->printRecommendation($id);

        return response()->download($recommendationPDF['path'], $recommendationPDF['customFileName'])->deleteFileAfterSend(true);

    }

    /**
     * @OA\Get(
     *     path="/cms/recommendations/plots/search",
     *     summary="Search plot",
     *     @OA\Response(
     *          response="200",
     *          description="Array of plot uuids"
     *     )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function searchPlots(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'sometimes|required|integer',
            'name' => 'required|string',
            'year' => 'sometimes|required|integer',
            'crop_id' => 'sometimes|required|integer',
            'all_organizations' => 'sometimes|required|boolean'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $year = $request->get('year');
        $name = $request->get('name');
        $cropId = $request->get('crop_id');
        $allOrganizations = filter_var($request->get('all_organizations', false), FILTER_VALIDATE_BOOLEAN);

        $organizationId = null;
        if (!$allOrganizations) {
            $organizationId = $request->get('organization_id', Auth::user()->lastChosenOrganization->id);
        }

        $plots = $this->recommendationService->searchPlots($organizationId, $name, $year, $cropId);

        return new JsonResponse($plots, 200, ['Content-Type: application/json']);
    }
}
