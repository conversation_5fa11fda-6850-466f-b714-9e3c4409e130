<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\CMS;


use App\Classes\CMS\ContractService;
use App\Classes\CMS\PackageService;
use App\Exceptions\ValidationException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PackageController
{
    private $packageService;
    private $contractService;

    public function __construct(PackageService $packageService, ContractService $contractService)
    {
        $this->packageService = $packageService;
        $this->contractService = $contractService;
    }

    public function list()
    {
        return $this->packageService->list();
    }

    /**
     * @OA\Get(
     *     path="/cms/packages-by-filter",
     *     summary="List of pacakges by filter",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * Get subscription and service packages byt filter.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getSubsAndServPackagesByFilter(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $queryParams = $request->all();

        $subscriptionPackages = $this->contractService->getCmsDataForPackagesByContractIdAndFilter('subscription-package', $queryParams);
        $servicePackages = $this->contractService->getCmsDataForPackagesByContractIdAndFilter('service-package', $queryParams);

        $response = array_merge($subscriptionPackages, $servicePackages);
        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }
}
