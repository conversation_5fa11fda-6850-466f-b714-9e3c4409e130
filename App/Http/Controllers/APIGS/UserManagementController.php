<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\GlobalUser;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Rules\UsernameUnique;
use OpenApi\Annotations as OA;

class UserManagementController extends BaseController
{
    /**
     * @OA\Get(
     *     path="/apigs/user-management/users/list",
     *     summary="List with users for organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return User[]
     */
    public function usersList()
    {
        /** @var Organization $organization */
        $organization = Auth::user()->lastChosenOrganization;

        /** @var User[] $users */
        $users = $organization->users()->get();

        $users->map(function (User $user) use($organization) {
            $roles = [];
            $forbiddenAbilities = [];
            $globalUser = $user->globalUser();

            $roles[] = $globalUser->roles;
            $forbiddenAbilities[] = $globalUser->getForbiddenAbilities();

            $user->roles = reset($roles);
            $user->forbidden_abilities = reset($forbiddenAbilities);

            $user->farms = $user->farmsByOrganization($organization);
        });

        return $users;
    }

    /**
     * @OA\Post(
     *     path="/apigs/user-management/users/create",
     *     summary="Create user",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     * @return User
     * @throws ValidationException
     */
    public function createUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => [
                'required',
                'regex:/^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/',
                new UsernameUnique
            ],
            'password' => 'required|string',
            'confirmPassword' => 'required|string',
            'email' => 'required|email',
            'name' => 'required|string',
            'phone' => 'string',
            'farms' => 'required|array',
            'farms.*.id' => 'required|integer',
            'abilities' => 'required|array',
            'abilities.*.name' => 'required|string',
            'abilities.*.allowed' => 'required|boolean'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        if ($request->get('password') != $request->get('confirmPassword')) {
            throw new ValidationException('Please confirm the password correctly.');
        }

        return User::createUser($request);
    }

    /**
     * @OA\Post(
     *     path="/apigs/user-management/users/update/:userId",
     *     summary="Update user data",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @param User $user
     * @param Request $request
     * @throws ValidationException
     */
    public function updateUser(User $user, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'sometimes|string',
            'confirmPassword' => 'sometimes|string',
            'email' => 'sometimes|email',
            'name' => 'sometimes|string',
            'phone' => 'string',
            'farms' => 'required|array',
            'farms.*.id' => 'required|integer',
            'abilities' => 'required|array',
            'abilities.*.name' => 'required|string',
            'abilities.*.allowed' => 'required|boolean'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        if ($request->get('password') != $request->get('confirmPassword')) {
            throw new ValidationException('Please confirm the password correctly.');
        }

        $globalUser = $user->globalUser();

        if ($request->get('password')) {
            $password = Hash::make($request->get('password'));
            $user->password = $password;
            $globalUser->password = $password;
        }
        if ($request->get('email')) {
            $user->email = $request->get('email');
            $globalUser->email = $request->get('email');
        }
        if ($request->get('name')) {
            $user->name = $request->get('name');
            $globalUser->name = $request->get('name');
        }
        
        $user->phone = $request->get('phone');

        /** @var Organization $organization */
        $organization = Auth::user()->lastChosenOrganization;
        $farmsToDetach = $organization->farms->pluck('id')->toArray();
        
        $user->farms()->detach($farmsToDetach);
        foreach ($request->get('farms') as $farm) {
            $user->farms()->attach($farm["id"]);
        }

        foreach ($request->get('abilities') as $ability) {
            if ($ability["allowed"]) {
                $globalUser->unforbid($ability["name"]);
            } else {
                $globalUser->forbid($ability["name"]);
            }
        }

        $user->save();
        $globalUser->save();
    }

    /**
     * @OA\Post(
     *     path="/apigs/user-management/users/toggle/:userId",
     *     summary="Toggle user status",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @param User $user
     */
    public function toggleUserStatus(User $user)
    {
        $globalUser = $user->globalUser();
        $user->active = !$globalUser->active;
        $globalUser->active = !$globalUser->active;
        $user->save();
        $globalUser->save();
    }

    public function findUserByUsername(Request $request)
    {
        $username = $request->get('username');
        $user = GlobalUser::where('username', $username)->firstOrFail();

        return $user;
    }
}
