<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Classes\AgroLab\AgroLabService;
use App\Classes\CouchDBClient;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Analyzes;
use App\Models\SoilPoints;
use App\Models\SoilSampleNumber;
use Auth;
use Config;
use DB;
use Excel;
use Exception;
use File;
use Hash;
use Illuminate\Http\JsonResponse;
use Plupload;
use Request;
use Response;
use Validator;

class SoilsController extends BaseController
{
    private $agroLabService;

    public function __construct(CouchDBClient $couchDB, AgroLabService $agroLabService)
    {
        $this->couchDBClient = $couchDB->client;
        $this->agroLabService = $agroLabService;
    }

    /**
     * @return array
     * @throws ValidationException
     */
    public function getProbeContent()
    {

        $validator = Validator::make(Request::all(), [
            'soprId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $soprId = Request::get('soprId');

        $model = new SoilPoints();

        try {
            $data = $model->loadProbeContent((int)$soprId);

        } catch (Exception $e) {
            throw $e;
        }

        return [
            'total' => count($data),
            'rows' => $data,
            'elements' => [],
        ];
    }

    /**
     * @return array
     * @throws ValidationException
     */
    public function getAnalyzes()
    {

        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'sort' => 'string',
            'comment' => 'string',
            'username' => 'string',
            'original_name' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $userId = Auth::user()->id;
        $sort = Request::get('sort');
        $comment = Request::get('comment');
        $username = Request::get('username');
        $originalName = Request::get('original_name');

        $model = new Analyzes();

        try {
            $resultQuery = $model->loadAnalyzes($userId, $originalName, $username, $comment, $sort);
            $result = $resultQuery->paginate(Request::get('limit'));

        } catch (Exception $e) {
            throw $e;
        }

        return [
            'total' => $result->total(),
            'rows' => $result->items()
        ];
    }

    /**
     * @return array
     * @throws ValidationException
     */
    public function searchForTemplate()
    {
        $validator = Validator::make(Request::all(), [
            'batch' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $batch = Request::get('batch');

        $docData = $this->couchDBClient->findDocument("p_" . $batch);

        if (isset($docData->body["error"])) {
            return ['total' => 0, 'rows' => []];
        }

        try {
            $data = $this->dataForTemplate($docData);
        } catch (Exception $e) {
            throw $e;
        }

        return [
            'total' => count($data),
            'rows' => $data,
        ];
    }

    /**
     * @return array
     * @throws ValidationException
     */
    public function searchForTemplateExcell()
    {
        $validator = Validator::make(Request::all(), [
            'batch' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $batch = Request::get('batch');

        $docData = $this->couchDBClient->findDocument("p_" . $batch);

        if (isset($docData->body["error"])) {
            return ['total' => 0, 'rows' => []];
        }

        try {
            $rows = $this->dataForTemplate($docData);
        } catch (Exception $e) {
            throw $e;
        }

        $printData = array();
        $headers = array(
            trans('general.lab_number'),
            trans('general.block_name'),
            trans('general.cell_number')
        );

        foreach ($rows as $subkey => $subvalue) {

            $printData[] = array(
                $headers[0] => $subvalue['lab_number'],
                $headers[1] => $subvalue['name'],
                $headers[2] => $subvalue['sample_id'],
            );
        }

        $filename = 'ForTemplate';
        Excel::create($filename, function ($excel) use ($printData) {

            $excel->sheet('Sheetname', function ($sheet) use ($printData) {
                $sheet->fromArray($printData, null, 'A1');

                $sheet->row(1, function ($row) {
                    $row->setFontWeight('bold');
                });
            });

        })->download('xls');
    }

    /**
     * @return array
     * @throws ValidationException
     */
    public function recievedSamples()
    {
        $validator = Validator::make(Request::all(), [
            'sort' => 'required|string',
            'limit' => 'required|integer',
            'page' => 'required|integer',
            'batch' => 'string',
            'from_date' => 'date_format:Y-m-d',
            'to_date' => 'date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $batch = Request::get('batch');
        $fromDate = Request::get('from_date');
        $toDate = Request::get('to_date');
        $limit = intval(Request::get('limit'));
        $page = intval(Request::get('page'));
        $sort = Request::get('sort');

        $recievedSamples = $this->agroLabService->getRecievedSamples($batch, $fromDate, $toDate, $limit, $page, $sort);

        if ($recievedSamples == 'error') {
            return Response::json(array('error' => 'No result'), 400);
        }

        $result = SoilSampleNumber::joinBarcodesWith($recievedSamples['jsonDocs'], $sort);

        return array(
            'rows' => $result,
            'total' => $recievedSamples['totalCount']
        );
    }

    /**
     * @return array
     * @throws ValidationException
     */
    public function checkedBarcodes()
    {
        $validator = Validator::make(Request::all(), [
            'sort' => 'required|string',
            'limit' => 'required|integer',
            'page' => 'required|integer',
            'batch' => 'string',
            'barcode_lab' => 'string',
            'barcode_sampling' => 'string',
            'order_id' => 'integer',
            'plot_name' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $batch = Request::get('batch');
        $barcodeLab = Request::get('barcode_lab');
        $barcodeSampling = Request::get('barcode_sampling');
        $orderId = Request::get('order_id');
        $plotName = Request::get('plot_name');
        $sort = Request::get('sort');

        $filter = [
            'batch' => $batch,
            'barcodeLab' => $barcodeLab,
            'barcodeSampling' => $barcodeSampling,
            'orderId' => $orderId,
            'plotName' => $plotName
        ];

        $isFilterEmpty = $this->agroLabService->isFilterCheckedBarcodesEmpty($filter);

        if ($isFilterEmpty) {
            return array(
                'rows' => []
            );
        }

        $couchDbBarcodes = $this->agroLabService->getCouchDbBarcodes($filter, $sort);

        if ($couchDbBarcodes == 'error') {
            return Response::json(array('error' => 'Error occurred'), 400);
        }

        $result = $this->agroLabService->checkBarcodes($couchDbBarcodes['jsonDocs'], $filter, $sort);

        return array(
            'rows' => $result
        );
    }

    public function updateBarcode()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'sample_number' => 'required|string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gid = Request::get('gid');
        $barcode = Request::get('sample_number');

        if (SoilSampleNumber::barcodeExists($barcode)) {
            return new JsonResponse('The entered value already exists', 400);
        }

        SoilSampleNumber::updateBarcode($gid, $barcode);

        return new JsonResponse(["result" => "Changed successfully."], 200);
    }

    private function dataForTemplate($docData)
    {
        if (!$docData) {
            return [];
        }

        $arrCouch = $docData->body['samples'];

        $arrBarcodesCouch = array_map(function ($value) {
            return $value['barcode'];
        }, $arrCouch);

        $arrGeoScan = SoilSampleNumber::loadForTemplate($arrBarcodesCouch);

        $arrMerge = array_map(function ($value) use ($arrCouch) {

            $arrFound = array_filter($arrCouch, function ($elem) use ($value) {
                return $elem['barcode'] == $value['barcode'];
            });

            $arrFoundCouch = reset($arrFound);

            if (isset($arrFoundCouch['labnumber'])) {
                $value['lab_number'] = $arrFoundCouch['labnumber'];
            }

            $value['cell'] = $arrFoundCouch['cell'];

            return $value;
        }, $arrGeoScan);

        //Sort the array by cell
        usort($arrMerge, function ($a, $b) {
            return intval($a['cell']) > intval($b['cell']);
        });

        return $arrMerge;
    }
}
