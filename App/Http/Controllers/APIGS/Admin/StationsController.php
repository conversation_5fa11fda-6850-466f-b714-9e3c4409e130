<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Classes\CMS\ContractService;
use App\Classes\CMS\PackageService;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\UserStation;
use App\Services\Station\StationService;
use App\Service\Log\RequestLogService;
use Illuminate\Http\JsonResponse;
use Auth;
use Config;
use DB;
use Request;
use Response;
use Validator;

class StationsController extends BaseController
{
    private $stationService;
    private $requestLogService;
    private $packageService;
    private $contractService;

    public function __construct(StationService $stationService, RequestLogService $requestLogService, PackageService $packageService, ContractService $contractService)
    {
        $this->stationService = $stationService;
        $this->requestLogService = $requestLogService;
        $this->packageService = $packageService;
        $this->contractService = $contractService;
    }

    /**
     * @return mixed
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function postCreateStation()
    {
        $validator = Validator::make(Request::all(), [
            'name' => 'required|string|max:63',
            'custom_name' => 'sometimes|required|string|max:63',
            'organization_id' => 'required|integer',
            'contract_id' => 'required|integer',
            'radius' => 'required|numeric',
            'type' => 'required|string',
            'install_date' => 'required|date'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationName = Request::get('name');
        $stationCustomName = Request::get('custom_name');
        $stationType = Request::get('type');
        $stationInstallDate = Request::get('install_date');
        $radius = Request::get('radius');
        $contractId = Request::get('contract_id');
        $organizationId = Request::get('organization_id');

        $hasActivePackage = $this->contractService->hasPackage($contractId, 'Active', 'WS');
        if(!$hasActivePackage) {
            return Response::json(array('error' => 'No active Weather stations package.'), 403);
        }

        if (!$this->stationService->canAddStation($contractId)) {
            return Response::json(array('error' => 'No more stations allowed to create for this contract!'), 403);
        }

        $hasOverlapContracts = $this->stationService->hasOverlapContracts($stationName, $contractId, 'Active', true);
        if ($hasOverlapContracts) {
            return Response::json(array('error' => 'Station already exists!'), 403);
        }

        //CheckStation By User and Station Name
        $result = $this->stationService->isStationExists($stationName, $organizationId);
        if (is_object($result)) {
            return $result;
        }

        $station = new UserStation();
        $station->organization_id = $organizationId;
        $station->contract_id = $contractId;
        $station->name = $stationName;
        $station->radius = $radius;
        $station->active = true;
        $station->type = $stationType;
        $station->install_date = $stationInstallDate;

        $data = $station->getStationApi()->getStationData();

        if (!is_array($data) || !isset($data['dates']) || !isset($data['position']) || (is_array($data) && isset($data['error']))) {
            return Response::json(array('error' => 'Station not found.'), 404);
        }

        $lastCommunication = $data['dates']['last_communication'];
        $longitude = $data['position']['geo']['coordinates'][UserStation::LONGITUDE_INDEX];
        $latitude = $data['position']['geo']['coordinates'][UserStation::LATITUDE_INDEX];

        if (empty($stationCustomName) && isset($data['name']['custom'])) {
            $stationCustomName = $data['name']['custom'];
        }

        $station->latitude = $latitude;
        $station->longitude = $longitude;
        $station->last_communication = $lastCommunication;
        $station->geom = DB::raw('ST_Transform(ST_SetSRID(ST_MakePoint(' . $longitude . ', ' . $latitude . '), 4326), ' . Config::get("globals.DEFAULT_DB_CRS") . ')');
        $station->custom_name = $stationCustomName ? $stationCustomName : $stationName;

        $result = $station->save();

        if (!$result) {
            return Response::json(array('error' => 'Insert faild'), 500);
        }

        $this->packageService->stationAdded($contractId);

        return Response::json(array('Response' => 'Success', 'station' => $station));
    }

    /**
     * @return mixed
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function findStation()
    {
        $validator = Validator::make(Request::all(), [
            'name' => 'required|string|max:63',
            'organization_id' => 'required|integer'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationName = Request::get('name');
        $organizationId = Request::get('organization_id');

        $station = UserStation::where('name', $stationName)->first();
        $data = $station->getStationApi()->getStationData();

        if (!is_array($data) || !isset($data['dates']) || !isset($data['position']) || (is_array($data) && isset($data['error']))) {
            return Response::json(array('error' => 'Station not found.'), 403);
        }

        $hasOverlapContracts = $this->stationService->hasOverlapContracts($stationName, $station->contract_id, 'Active', true);
        if ($hasOverlapContracts) {
            return Response::json(array('error' => 'Station already exists!'), 403);
        }

        $result = $this->stationService->isStationExists($stationName, $organizationId);
        if (is_object($result)) {
            return $result;
        }

        return [
            "longitude" => $data['position']['geo']['coordinates'][UserStation::LONGITUDE_INDEX],
            "latitude" => $data['position']['geo']['coordinates'][UserStation::LATITUDE_INDEX]
        ];
    }

    /**
     * @return mixed
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function postUpdateStation()
    {
        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'organization_id' => 'required|integer',
            'name' => 'string',
            'custom_name' => 'string',
            'radius' => 'numeric',
            'install_date' => 'date'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $station = UserStation::select('*')
            ->where('id', Request::get('id'))
            ->where('organization_id', Request::get('organization_id'))->first();

        if (!$station) {
            return;
        }

        if (Request::get('set_active') !== null) {

            $setActive = Request::get('set_active');
            return $this->stationService->activateDeactivate($station, $setActive);
        }

        $stationName = Request::get('name');

        $object = UserStation::where('name', $stationName)->first()->getStationApi();
        $data = $object->getStationData();

        if (!is_array($data) || !isset($data['dates']) || !isset($data['position']) || (is_array($data) && isset($data['error']))) {
            return Response::json(array('error' => 'Station not found.'), 403);
        }

        //CheckStation By User and Station Name
        $stationId = UserStation::select('id')
            ->where('organization_id', Request::get('organization_id'))
            ->where('name', $stationName)
            ->first();

        if (intval($stationId->id) != intval(Request::get('id'))) {
            return Response::json(array('error' => 'Station already exists for this organization.'), 403);
        }

        $lastCommunication = $data['dates']['last_communication'];
        $longitude = $data['position']['geo']['coordinates'][UserStation::LONGITUDE_INDEX];
        $latitude = $data['position']['geo']['coordinates'][UserStation::LATITUDE_INDEX];

        $station->name = Request::get('name');
        $station->custom_name = Request::get('custom_name') ? Request::get('custom_name') : $stationName;
        $station->radius = intval(Request::get('radius'));
        $station->latitude = $latitude;
        $station->longitude = $longitude;
        $station->last_communication = $lastCommunication;
        $station->install_date = Request::get('install_date');
        $station->geom = DB::raw('ST_Transform(ST_SetSRID(ST_MakePoint(' . $longitude . ', ' . $latitude . '), 4326), ' . Config::get("globals.DEFAULT_DB_CRS") . ')');
        $station->save();

        return $station;
    }

    /**
     * @throws ValidationException
     */
    public function postDeleteStation()
    {

        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'organization_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $station = UserStation::select('*')
            ->where('id', Request::get('id'))
            ->where('organization_id', Request::get('organization_id'))->first();

        if (!$station) {
            return;
        }

        $station->delete();
    }

    public function getStationHistory(Request $request, $stationId)
    {
        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $log = $this->requestLogService->getStationHistory($stationId);
        return new JsonResponse(['rows' => $log], 200);
    }

}
