<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Seller;
use Auth;
use Config;
use DB;
use Hash;
use Request;
use Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Validator;

class SellersController extends BaseController
{

    /**
     * @return array
     * @throws ValidationException
     */
    public function getList()
    {
        throw new BadRequestHttpException('Do not use this method.');
        //Restrict end_client
        if (!Auth::user()->globalUser()->isAn('SUPER_ADMIN', 'SAMPLER_ADMIN', 'SERVICE_ADMIN')) {
            throw new ValidationException('No rights.');
        }

        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'name' => 'string|max:127',
            'phone' => 'string|max:127',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $resultQuery = Seller::select('*')
            ->where('user_id', Auth::user()->id)
            ->restOrderBy(Request::get('sort'));

        //Filter   
        if (Request::get('name')) {
            $resultQuery->where('name', 'ILIKE', trim('%' . Request::get('name') . '%'));
        }
        if (Request::get('phone')) {
            $resultQuery->where('phone', 'ILIKE', trim('%' . Request::get('phone') . '%'));
        }

        $result = $resultQuery->paginate(Request::get('limit'));

        return [
            'total' => $result->total(),
            'rows' => $result->items()
        ];
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function postCreateSeller()
    {
        throw new BadRequestHttpException('Do not use this method.');
        //Restrict end_client
        if (!Auth::user()->globalUser()->isAn('SUPER_ADMIN', 'SAMPLER_ADMIN', 'SERVICE_ADMIN')) {
            throw new ValidationException('No rights.');
        }

        $validator = Validator::make(Request::all(), [
            'name' => 'required|string|max:127',
            'phone' => 'string|max:127',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $seller = new Seller();
        $seller->user_id = Auth::user()->id;
        $seller->name = Request::get('name');
        $seller->phone = Request::get('phone');
        $result = $seller->save();

        if (!$result) {
            return Response::json(array('error' => 'Insert faild'), 500);
        }

        return Response::json(array('Response' => 'Success', 'sellerId' => $seller->id));
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function postUpdateSeller()
    {
        throw new BadRequestHttpException('Do not use this method.');
        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'name' => 'required|string|max:127',
            'phone' => 'string|max:127',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $seller = Seller::find((int)Request::get('id'));

        //Check Rights
        if (!Auth::user()->globalUser()->isAn('SUPER_ADMIN', 'SAMPLER_ADMIN',
                'SERVICE_ADMIN') || !$seller || ($seller->user_id != Auth::user()->id)) {
            throw new ValidationException('No rights.');
        }

        $seller->name = Request::get('name');
        $seller->phone = Request::get('phone');
        $seller->save();

        return Response::json(array('Response' => 'Success'));
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function postDeleteSeller()
    {
        throw new BadRequestHttpException('Do not use this method.');

        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $seller = Seller::find((int)Request::get('id'));

        //Check Rights
        if (!Auth::user()->globalUser()->isAn('SUPER_ADMIN', 'SAMPLER_ADMIN',
                'SERVICE_ADMIN') || !$seller || ($seller->user_id != Auth::user()->id)) {
            throw new ValidationException('No rights.');
        }

        $seller->delete();

        return Response::json(array('Response' => 'Success'));
    }

}
