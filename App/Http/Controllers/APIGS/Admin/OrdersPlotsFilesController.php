<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use DB;
use App\Models\Order;
use App\Models\Plot;
use App\Models\OrderPlotRel;
use App\Models\LayerPlot;
use App\Models\SoilPoints;
use App\Exceptions\ValidationException;
use Validator;
use Request;
use Plupload;
use File;
use Config;
use Response;
use App\Jobs\LoadSoilGridSHP;
use App\Jobs\LoadSoilPointsSHP;
use App\Jobs\LoadSoilTracksSHP;
use App\Models\Layer;
use App\Models\File as FileModel;
use Auth;
use Event;

class OrdersPlotsFilesController extends BaseController
{

    /**
     * @throws ValidationException
     */
    public function uploadSoilPointsSHP()
    {

        $validator = Validator::make(Request::all(), [
            'sopr_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $ordersPlotsRelId = Request::get('sopr_id');

        $orderPlotRel = OrderPlotRel::find($ordersPlotsRelId);
        if (!$orderPlotRel) {
            return;
        }

        $order = Order::find($orderPlotRel->order_id);
        if (!$order) {
            return;
        }

        $userId = Auth::user()->id;

        return Plupload::receive('file', function ($uploadedFile) use ($userId, $ordersPlotsRelId) {
            $file = new FileModel();
            $file->name = basename($uploadedFile->getClientOriginalName(),
                '.' . $uploadedFile->getClientOriginalExtension());
            $file->user_id = $userId;
            $file->filename = $userId . '_' . $uploadedFile->getClientOriginalName();
            $file->crs = (int)Request::input('srs', config('globals.DEFAULT_DB_CRS'));
            $file->shape_type = Layer::LAYER_TYPE_SOIL_POINTS;
            $file->device_type = 0;
            $file->group_id = $userId;
            $file->sopr_id = $ordersPlotsRelId;
            $file->path = config('globals.LAYERS_QUEUE_PATH') . $userId . '_' . $uploadedFile->getClientOriginalName();

            $uploadedFile->move(config('globals.LAYERS_QUEUE_PATH'), $file->filename);

            $file->save();

            $this->dispatch(new LoadSoilPointsSHP(FileModel::find($file->id)));

            return $file->id;
        });
    }

    /**
     * @throws ValidationException
     */
    public function uploadSoilGridSHP()
    {

        $validator = Validator::make(Request::all(), [
            'sopr_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $ordersPlotsRelId = Request::get('sopr_id');

        $orderPlotRel = OrderPlotRel::find($ordersPlotsRelId);
        if (!$orderPlotRel) {
            return;
        }

        $order = Order::find($orderPlotRel->order_id);
        if (!$order) {
            return;
        }

        $userId = Auth::user()->id;

        return Plupload::receive('file', function ($uploadedFile) use ($userId, $ordersPlotsRelId) {
            $file = new FileModel();
            $file->name = basename($uploadedFile->getClientOriginalName(),
                '.' . $uploadedFile->getClientOriginalExtension());
            $file->user_id = $userId;
            $file->filename = $userId . '_' . $uploadedFile->getClientOriginalName();
            $file->crs = (int)Request::input('srs', config('globals.DEFAULT_DB_CRS'));
            $file->shape_type = Layer::LAYER_TYPE_SOIL_GRID;
            $file->device_type = 0;
            $file->group_id = $userId;
            $file->sopr_id = $ordersPlotsRelId;
            $file->path = config('globals.LAYERS_QUEUE_PATH') . $userId . '_' . $uploadedFile->getClientOriginalName();

            $uploadedFile->move(config('globals.LAYERS_QUEUE_PATH'), $file->filename);

            $file->save();

            $this->dispatch(new LoadSoilGridSHP(FileModel::find($file->id)));

            return $file->id;
        });
    }

    /**
     * @throws ValidationException
     */
    public function uploadSoilArcsSHP()
    {

        $validator = Validator::make(Request::all(), [
            'sopr_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $ordersPlotsRelId = Request::get('sopr_id');

        $orderPlotRel = OrderPlotRel::find($ordersPlotsRelId);
        if (!$orderPlotRel) {
            return;
        }

        $order = Order::find($orderPlotRel->order_id);
        if (!$order) {
            return;
        }

        $userId = Auth::user()->id;

        return Plupload::receive('file', function ($uploadedFile) use ($userId, $ordersPlotsRelId) {
            $file = new FileModel();
            $file->name = basename($uploadedFile->getClientOriginalName(),
                '.' . $uploadedFile->getClientOriginalExtension());
            $file->user_id = $userId;
            $file->filename = $userId . '_' . $uploadedFile->getClientOriginalName();
            $file->crs = (int)Request::input('srs', config('globals.DEFAULT_DB_CRS'));
            $file->shape_type = Layer::LAYER_TYPE_TRACKS;
            $file->device_type = 0;
            $file->group_id = $userId;
            $file->sopr_id = $ordersPlotsRelId;
            $file->path = config('globals.LAYERS_QUEUE_PATH') . $userId . '_' . $uploadedFile->getClientOriginalName();

            $uploadedFile->move(config('globals.LAYERS_QUEUE_PATH'), $file->filename);

            $file->save();

            $this->dispatch(new LoadSoilTracksSHP(FileModel::find($file->id)));

            return $file->id;
        });
    }


}
