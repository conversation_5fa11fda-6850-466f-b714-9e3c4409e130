<?php

namespace App\Http\Controllers\APIGS\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Classes\TFConnect;
use App\Models\OrganizationDevice;
use Validator;
use App\Exceptions\ValidationException;
use Symfony\Component\Filesystem\Exception;
use Illuminate\Support\Facades\Storage;

class TFConnectController extends Controller
{
    /**
     * @var TFConnect
     */
    public $tfConnect;
    public $orgDevice;

	public function __construct(TFConnect $tfConnect, OrganizationDevice $orgDevice) {
        $this->tfConnect = $tfConnect;
        $this->orgDevice = $orgDevice;
    }
    
    public function getDevices(){
        return $this->tfConnect->getDevices();
    }

    public function getDevicesByOrganization(Request $request){
        $validator = Validator::make($request->all(), [
            'organizationId' => 'required|integer'
        ]);

        if($validator->fails()){
            return $validator->errors()->all();
        }

        return $this->orgDevice->getByOrgId($request->get('organizationId'))->get();
    }


    public function setDeviceActiveForOrganization(Request $request){
        $validator = Validator::make($request->all(), [
            'deviceId' => 'required|integer',
            'deviceSerial' => 'required|alpha_num|min:16|max:16',
            'deviceName' => 'required|string',
            'organizationId' => 'required|integer',
            'activeFrom' => 'required|date',
            'activeTo' => 'required|date',
            'description' => 'string|nullable',
            'force' => 'required|boolean'
        ]);
        
        if ($validator->fails()) {
            return $validator->errors()->all();
        }
        
        $deviceId = $request->get('deviceId');
        $deviceSerial = $request->get('deviceSerial');
        $deviceName = $request->get('deviceName');
        $organizationId = $request->get('organizationId');
        $activeFrom = $request->get('activeFrom');
        $activeTo = $request->get('activeTo');
        $description = $request->get('description');
        $force = $request->get('force');


        if($this->orgDevice->exists($deviceId, $organizationId)){
            return response()->json(["status" => "exists", "message" => "This device is already connected to the selected organization!"]);
        }

        if(!$force){
            $deviceActiveForOtherOrganizations = $this->orgDevice->getByActiveDevice($deviceId, $organizationId, $activeFrom, $activeTo);
            if(count($deviceActiveForOtherOrganizations) > 0){
                return response()->json(["status" => "active", "message" => "This device is active for other organizations!", "organizations" => $deviceActiveForOtherOrganizations]);
            }
        }
        
        $this->orgDevice->organization_id = $organizationId;
        $this->orgDevice->device_id = $deviceId;
        $this->orgDevice->device_name = $deviceName;
        $this->orgDevice->device_serial = $deviceSerial;
        $this->orgDevice->active_from = $activeFrom;
        $this->orgDevice->active_to = $activeTo;
        $this->orgDevice->is_active = true;

        if($description)
        {
            $this->orgDevice->description = $description;
        }

        try {
            $this->orgDevice->save();
        }catch(\Exception $e){
            $errCode = $e->errorInfo[0];
             throw new \Exception(["code" => $errCode, "message" => $e->getMessage()]);
        }
        
        return response()->json(["status" => "ok", "message" => "Device set to active!"]);
    }

    public function changeDeviceStatusForOrganization(Request $request){
        $validator = Validator::make($request->all(), [
            'deviceId' => 'required|integer',
            'organizationId' => 'required|integer',
            'status' => 'required|boolean'
        ]);

        if($validator->fails()){
            return $validator->errors()->all();
        }

        $deviceId = $request->get('deviceId');
        $organizationId =  $request->get('organizationId');
        $status =  $request->get('status');

        if(!$this->orgDevice->changeStatus($deviceId, $organizationId, $status)){
            throw new \Exception("Error changing device status!");
        }

        return response()->json(["status" => "ok", "message" => "Device status changed!"]);

    }

    public function deleteDeviceOrganization(Request $request){
        $validator = Validator::make($request->all(), [
            'deviceId' => 'required|integer',
            'organizationId' => 'required|integer'
        ]);

        if($validator->fails()){
            return $validator->errors()->all();
        }

        $deviceId = $request->get('deviceId');
        $organizationId =  $request->get('organizationId');

        if(!$this->orgDevice->deleteOrgDevice($deviceId, $organizationId)){
            throw new \Exception("Error removing device");
        }
        return response()->json(["status" => "ok", "message" => "Device-organization connection removed!"]);
    }

    public function editDeviceOrganizationActiveTo(Request $request){
        $validator = Validator::make($request->all(), [
            'deviceId' => 'required|integer',
            'organizationId' => 'required|integer',
            'activeTo' => 'required|date'
        ]);

        if($validator->fails()){
            return $validator->errors()->all();
        }

        $deviceId = $request->get('deviceId');
        $organizationId =  $request->get('organizationId');
        $activeToDate =  $request->get('activeTo');

        if(!$this->orgDevice->updateActiveToDate($deviceId, $organizationId, $activeToDate)){
            throw new ValidationException("Error updating 'active to' date!");
        }

        return response()->json(["status" => "ok", "message" => "Date updated!"]);

    }

    public function getFilesLogByDevice(Request $request){
        $validator = Validator::make($request->all(), [
            'deviceId' => 'required|integer'
        ]);

        if($validator->fails()){
            return $validator->errors()->all();
        }

        return $this->tfConnect->getFilesLogByDeviceId($request->get('deviceId'));
    }

    public function download($fileId) {
        $storeFilePath = storage_path("app/" . $this->tfConnect->downloadFile($fileId));

        return response()->download($storeFilePath)->deleteFileAfterSend();
    }

}
