<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Http\Controllers\BaseController;
use Request;
use Response;
use App\Models\Recommendation;
use App\Models\User;
use App\Models\RecommendationFile;
use DB;
use Auth;
use Config;
use Hash;
use Validator;
use App\Exceptions\ValidationException;
use Plupload;
use File;

class RecommendationsController extends BaseController
{

    /**
     * @return array
     * @throws ValidationException
     */
    public function getList()
    {

        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'title' => 'string|max:127',
            'description' => 'string',
            'active' => 'boolean',
            'user_id' => 'integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $resultQuery = Recommendation::selectRaw('su_users_recommendations.*, org.name as organization_name')
            ->join('su_organizations AS org', 'su_users_recommendations.organization_id', '=', 'org.id')
            ->restOrderBy(Request::get('sort'));

        //Consulting agency constraint
        if (!Auth::user()->globalUser()->isAn('SUPER_ADMIN')) {
            $resultQuery->where('parent_id', Auth::user()->id);
        }

        //Filter   
        if (Request::get('title')) {
            $resultQuery->where('title', 'ILIKE', trim('%' . Request::get('title') . '%'));
        }
        if (Request::get('descriptoin')) {
            $resultQuery->where('descriptoin', 'ILIKE', trim('%' . Request::get('descriptoin') . '%'));
        }
        if (strlen(Request::get('active'))) {
            $resultQuery->where('su_users_recommendations.active', Request::get('active'));
        }
        

        $result = $resultQuery->paginate(Request::get('limit'));

        return [
            'total' => $result->total(),
            'rows' => $result->items()
        ];
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function postCreateRecommendation()
    {

        $validator = Validator::make(Request::all(), [
            'title' => 'required|string|max:127',
            'description' => 'required|string',
            'user_id' => 'required|integer',
            'active' => 'boolean'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        //Consulting agency constraint
        $this->checkRights(Request::get('user_id'));

        $recom = new Recommendation();
        $recom->user_id = Request::get('user_id');
        $recom->title = Request::get('title');
        $recom->description = Request::get('description');
        $recom->active = Request::get('active', true);
        $result = $recom->save();

        if (!$result) {
            return Response::json(array('error' => 'Insert faild'), 500);
        }

        return Response::json(array('Response' => 'Success', 'recommendationId' => $recom->id));
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function postUpdateRecommendation()
    {

        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'title' => 'required|string|max:127',
            'description' => 'required|string',
            'user_id' => 'required|integer',
            'active' => 'boolean'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        //Consulting agency constraint
        $this->checkRights(Request::get('user_id'));

        $recom = Recommendation::find((int)Request::get('id'));
        $recom->user_id = Request::get('user_id');
        $recom->title = Request::get('title');
        $recom->description = Request::get('description');
        $recom->active = Request::get('active', true);
        $recom->save();

        return Response::json(array('Response' => 'Success'));
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function postUpdateRecommendationStatus()
    {

        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'set_active' => 'required|boolean',
            'user_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        //Consulting agency constraint
        $this->checkRights(Request::get('user_id'));

        $recom = Recommendation::find((int)Request::get('id'));
        $recom->active = Request::get('set_active');
        $recom->save();

        return Response::json(array('Response' => 'Success'));
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function postDeleteRecommendation()
    {

        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $recom = Recommendation::find((int)Request::get('id'));

        //Consulting agency constraint
        $this->checkRights($recom->user_id);

        $recomFiles = RecommendationFile::where('recommendation_id', $recom->id)->get();

        //remove files
        foreach ($recomFiles as $recomFile) {

            $path_parts = pathinfo($recomFile->file_name);
            $ext = $path_parts['extension'];
            $destinationPath = config('globals.RECOMMENDATIONS_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . $recom->user_id . '_user' . DIRECTORY_SEPARATOR . $recomFile->recommendation_id . '_rec';
            $file = $destinationPath . DIRECTORY_SEPARATOR . $recomFile->id . '.' . $ext;

            if (is_file($file)) {
                unlink($file);
            }
        }

        $recom->delete();

        return Response::json(array('Response' => 'Success'));
    }

    /**
     * @throws ValidationException
     */
    public function postUploadFileRecommendation()
    {
        $validator = Validator::make(Request::all(), [
            'recommendation_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $recom = Recommendation::find((int)Request::get('recommendation_id'));
        if (!$recom) {
            return;
        }

        //Consulting agency constraint
        $this->checkRights($recom->user_id);

        return Plupload::receive('file', function ($uploadedFile) use ($recom) {

            $allowedFileExtensions = array('zip', 'doc', 'docx', 'pdf', 'xls', 'xlsx', 'png', 'gif');

            $ext = $uploadedFile->getClientOriginalExtension();

            if (!$this->in_arrayi($ext, $allowedFileExtensions)) {
                return 'denied';
            }

            $recommendationFile = new RecommendationFile();
            $recommendationFile->recommendation_id = $recom->id;
            $recommendationFile->file_name = $uploadedFile->getClientOriginalName();
            $recommendationFile->save();

            //File name is $recommendationFile->id
            $fileName = $recommendationFile->id . "." . $uploadedFile->getClientOriginalExtension();
            $destinationPath = config('globals.RECOMMENDATIONS_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . $recom->user_id . '_user' . DIRECTORY_SEPARATOR . $recom->id . '_rec';
            $uploadedFile->move($destinationPath, $fileName);

            return 'ready';
        });
    }

    /**
     * @return array|void
     * @throws ValidationException
     */
    public function getFilesList()
    {

        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'recommendation_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $recom = Recommendation::find((int)Request::get('recommendation_id'));
        if (!$recom) {
            return;
        }

        //Consulting agency constraint
        $this->checkRights($recom->user_id);

        $resultQuery = RecommendationFile::selectRaw('su_recommendations_files.*')
            ->join('su_users_recommendations AS ur', 'ur.id', '=', 'su_recommendations_files.recommendation_id')
            ->join('su_users AS u', 'ur.user_id', '=', 'u.id')
            ->where('ur.user_id', '=', $recom->user_id)
            ->where('su_recommendations_files.recommendation_id', '=', Request::get('recommendation_id'))
            ->restOrderBy(Request::get('sort'));

        $result = $resultQuery->paginate(Request::get('limit'));

        return [
            'total' => $result->total(),
            'rows' => $result->items()
        ];
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function postDeleteFile()
    {

        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'user_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        //Consulting agency constraint
        $this->checkRights(Request::get('user_id'));

        $recomFile = RecommendationFile::find((int)Request::get('id'));

        $path_parts = pathinfo($recomFile->file_name);
        $ext = $path_parts['extension'];
        $destinationPath = config('globals.RECOMMENDATIONS_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . Request::get('user_id') . '_user' . DIRECTORY_SEPARATOR . $recomFile->recommendation_id . '_rec';
        $file = $destinationPath . DIRECTORY_SEPARATOR . $recomFile->id . '.' . $ext;

        //remove file
        if (is_file($file)) {
            unlink($file);
        }

        $result = $recomFile->delete();

        return Response::json(array('Response' => $result));
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function getDownloadFile()
    {

        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'user_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        //Consulting agency constraint
        $this->checkRights(Request::get('user_id'));

        $recomFile = RecommendationFile::find((int)Request::get('id'));

        $path_parts = pathinfo($recomFile->file_name);
        $ext = $path_parts['extension'];
        $destinationPath = config('globals.RECOMMENDATIONS_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . Request::get('user_id') . '_user' . DIRECTORY_SEPARATOR . $recomFile->recommendation_id . '_rec';
        $file = $destinationPath . DIRECTORY_SEPARATOR . $recomFile->id . '.' . $ext;

        return Response::download($file, $recomFile->file_name);
    }

    /**
     * @param $userId
     * @throws ValidationException
     */
    private function checkRights($userId)
    {
        //Consulting agency constraint
        if (!Auth::user()->globalUser()->isAn('SUPER_ADMIN')) {
            //check user_id
            $user = User::find($userId);

            if ($user->parent_id != Auth::user()->id) {
                throw new ValidationException('No rights.');
            }
        }
    }

}
