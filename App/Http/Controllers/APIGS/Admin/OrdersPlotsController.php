<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use DB;
use App\Models\Order;
use App\Models\Plot;
use App\Models\OrderPlotRel;
use App\Models\API\ViewSoilOrderedPlots;
use App\Models\SoilGridParams;
use Config;
use Request;
use Validator;
use App\Exceptions\ValidationException;
use App\Exceptions\ForbiddenException;
use Auth;
use Excel;
use App\Jobs\RefreshMaterializedView;
use Carbon\Carbon;
use App\Services\Order\OrderService;

class OrdersPlotsController extends BaseController
{
    private $orderService;

    public function __construct(OrderPlotRel $orderPlotRel, OrderService $orderService)
    {
        $this->orderPlotRel = $orderPlotRel;
        $this->orderService = $orderService;
    }

    /**
     * @param $orderId
     * @return array
     */
    public function getOrderPlots($orderId)
    {
        $sort = Helper::getSortRequest("-gid");

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $order = Order::find($orderId);

        $orderPlots = $this->orderService->getPlots($order, $sort, $areaCoef);

        return array(
            'rows' => $orderPlots,
            'total' => count($orderPlots)
        );
    }

    /**
     * @param $orderId
     * @throws ValidationException
     */
    public function postSoilPlotsData($orderId)
    {
        $arrValidator = [
            'orderId' => 'required|integer',
            'plotData' => 'required|array',
        ];

        $validator = Validator::make(Request::all(), $arrValidator);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $plotData = Request::get('plotData');

        for ($i = 0; $i < count($plotData); $i++) {
            $orderPlotRel = OrderPlotRel::find((int)$plotData[$i]['orders_plots_rel_id']);
            $orderPlotRel->note = $plotData[$i]['note'];

            if ($plotData[$i]['sampler_id'] != null) {
                if ($orderPlotRel->sampler_id != $plotData[$i]['sampler_id']) {
                    $orderPlotRel->date_assigned = Carbon::now();
                }
                $orderPlotRel->sampler_id = $plotData[$i]['sampler_id'];
            }
            $orderPlotRel->treatment_id = empty($plotData[$i]['treatment_id']) ? null : implode($plotData[$i]['treatment_id'],
                ',');
            $orderPlotRel->ekatte_code = strlen($plotData[$i]['ekatte_code']) ? $plotData[$i]['ekatte_code'] : '';
            $orderPlotRel->ekatte_name = strlen($plotData[$i]['ekatte_name']) ? $plotData[$i]['ekatte_name'] : '';

            if (is_array($plotData[$i]['leaf_sample_cells'])) {
                $orderPlotRel->leaf_sample_cells = empty($plotData[$i]['leaf_sample_cells']) ? '' : implode($plotData[$i]['leaf_sample_cells'],
                    ',');
            }

            $orderPlotRel->save();
        }
    }

    /**
     * @param $orderId
     * @param $plotId
     * @throws ValidationException
     */
    public function deletePlot($orderId, $plotId)
    {
        $validator = Validator::make([
            'order_id' => $orderId,
            'plot_id' => $plotId
        ], [
            'order_id' => 'required|numeric',
            'plot_id' => 'required|numeric'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $order = Order::find($orderId);

        if (!$order || $order->status != 'new') {
            return;
        }

        $orderPlot = Plot::from('su_satellite_plots as sp')->select(
            'sp.area', 'sopr.id as orders_plots_rel_id', 'sopr.price'
        )
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'sp.gid')
            ->where('sopr.order_id', $orderId)
            ->where('sopr.plot_id', $plotId)
            ->first();

        $orderPlotPrice = $orderPlot->price;
        $orderPlotArea = $orderPlot->area;
        $orderPlotRelId = $orderPlot->orders_plots_rel_id;

        //decrease area and price from the order cause deleting plot from it
        $order->price -= $orderPlotPrice;
        $order->area -= $orderPlotArea;
        $order->save();

        //delete relation between order and plot
        OrderPlotRel::destroy($orderPlotRelId);
    }

    /**
     * @param $orderId
     * @param $gid
     */
    public function updatePlot($orderId, $gid)
    {
        $orderPlotRel = OrderPlotRel::where('order_id', $orderId)
            ->where('plot_id', $gid)
            ->first();

        if (!$orderPlotRel) {
            return;
        }

        $orderPlotRel->note = Request::get('note');
        $orderPlotRel->save();

        return $orderPlotRel;
    }

    /**
     * @return array
     * @throws ValidationException
     */
    public function samplesPeriodReport()
    {
        $validator = Validator::make(Request::all(), [
            'sort' => 'required|string',
            'limit' => 'required|string',
            'page' => 'required|integer',

            'from_date' => 'string',
            'to_date' => 'string',
            'sampler' => 'integer',
            'company' => 'string',
            'order' => 'integer',
            'plot' => 'string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $arrSamples = $this->orderPlotRel->getOrdersPeriodReport();
        $limit = Request::get('limit');
        $result = $arrSamples->paginate($limit);

        $rows = $result->each(function ($order) {
            $order->date = date('d.m.Y H:i', strtotime($order->date));
        });

        return array(
            'rows' => $rows,
            'total' => $result->total()
        );
    }

    public function getSamplesPeriodReportExcell()
    {
        $arrSamples = $this->orderPlotRel->getOrdersPeriodReport();
        $result = $arrSamples->get();

        $rows = collect($result)->each(function ($order) {
            $order->date = date('d.m.Y H:i', strtotime($order->date));
        });

        $printData = array();
        $headers = array(
            trans('general.order'),
            trans('general.area') . "(" . Config::get('globals.AREA_UNIT_LABEL') . ")",
            trans('general.date'),
            trans('general.sampler'),
            trans('general.client'),
            trans('general.plot'),
            trans('general.samples_with_data'),
            trans('general.total_samples')
        );

        foreach ($rows as $subkey => $subvalue) {
            $printData[] = array(
                $headers[0] => $subvalue->order_id,
                $headers[1] => $subvalue->area,
                $headers[2] => $subvalue->sync_date,
                $headers[3] => $subvalue->sampler,
                $headers[4] => $subvalue->company_name,
                $headers[5] => $subvalue->plot_name,
                $headers[6] => $subvalue->samples_with_data,
                $headers[7] => $subvalue->total_samples
            );
        }

        $filename = 'SamplesPeriodReport';
        Excel::create($filename, function ($excel) use ($printData) {
            $excel->sheet('Sheetname', function ($sheet) use ($printData) {
                $sheet->fromArray($printData, null, 'A1');

                $sheet->row(1, function ($row) {
                    $row->setFontWeight('bold');
                });
            });
        })->download('xls');
    }

    /**
     * @deprecated
     *
     * @OA\Post(
     *     path="/apigs/admin/orders/:order/plots/grid",
     *     summary="Generate grid for order's plots.",
     *     @OA\Response(
     *     response="200",
     *     description="Json"
     * )
     * )
     *
     * @param $order
     * @return object
     * @throws ValidationException
     */
    public function generateGrid(Order $order)
    {
        $validator = Validator::make(Request::all(), [
            'type' => 'required|in:2ha,5ha,iso,vra,custom'
        ]);

        $validator->sometimes('area', 'required|numeric', function ($params) {
            return $params->type == 'custom';
        });

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $type = Request::get('type');
        $area = Request::get('area');

        if($type != 'custom') {
            $area = null;
        }


        $orderPlotsRel = $order->orderPlots()->with('plot')->get();

        $soilGridParams = SoilGridParams::getByOrderId($order->id);
        $soilGridParamsRow = $soilGridParams->first();

        if($soilGridParamsRow) {
            if($soilGridParamsRow->status == 'processing') {
                return response()->json(['message' => "Grid is currently processing. Please wait."], 400);
            }

            $soilGridParams->delete();
        }

        $soilGridParams = new SoilGridParams();

        $soilGridParams->type = $type;
        $soilGridParams->area = $area;
        $soilGridParams->order_id = $order->id;
        $soilGridParams->user_id = Auth::user()->id;
        $soilGridParams->datetime = date('Y-m-d H:i:s');
        $soilGridParams->save();

        DB::beginTransaction();
        try{
            foreach($orderPlotsRel as $sopr){
                $sopr->plot->createGrid($sopr->id, $type, $area);
            }
            DB::commit();
        } catch(\Exception $e) {
            DB::rollback();
            $soilGridParams->status = 'error';
            $soilGridParams->save();
            throw $e;
        }

        $soilGridParams->status = 'processed';
        $soilGridParams->save();
       return $soilGridParams;
    }

    /**
     * @OA\gET(
     *     path="/apigs/admin/orders/:order/grid/params",
     *     summary="Get last grid params by order id.",
     *     @OA\Response(
     *     response="200",
     *     description="Json"
     * )
     * )
     *
     * @param $order
     * @return object
     * @throws ValidationException
     */
    public function lastGridParams(Order $order)
    {
        $params = SoilGridParams::getByOrderId($order->id)->first();

        if(!$params) {
            return;
        }
        
        return response()->json(["type" => $params->type, "area" => $params->area, "status" => $params->status]);
    }
}
