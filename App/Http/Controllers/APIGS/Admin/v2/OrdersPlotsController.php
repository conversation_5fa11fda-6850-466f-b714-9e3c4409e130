<?php

namespace App\Http\Controllers\APIGS\Admin\v2;

use App\Classes\CMS\ContractService;
use App\Classes\PlotShape;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\OrderPlotRel;
use Carbon\Carbon;
use GuzzleHttp\Psr7\Response;
use Illuminate\Http\JsonResponse;
use Request;
use Validator;
use DB;

class OrdersPlotsController extends BaseController
{
    private $plotShape;
    private $contractService;

    public function __construct(PlotShape $plotShape, ContractService $contractService)
    {
        $this->plotShape = $plotShape;
        $this->contractService = $contractService;
    }

    /**
     * @OA\Post(
     *     path="/apigs/admin/v2/plots/:plotId/grid",
     *     summary="Generate grid for plots.",
     *     @OA\Response(
     *     response="200",
     *     description="Json"
     * )
     * )
     *
     * @param $plotId
     * @return object
     * @throws ValidationException
     */
    public function generateGrid($plotId)
    {
        $validator = Validator::make(Request::all(), [
            'type' => 'required|in:2ha,5ha,iso,vra,custom',
        ]);

        $validator->sometimes('customGridCellArea', 'required|numeric', function ($params) {
            return $params->type == 'custom';
        });

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $type = Request::get('type');
        $customCellArea = Request::get('customGridCellArea');

        if ($type != 'custom') {
            $customCellArea = null;
        }

        $response = $this->plotShape->generateGridByPlotId($plotId, $type, $customCellArea);
        return new Response(200, ['Content-Type: application/json'], $response);

    }

    /**
     * @OA\Post(
     *     path="/apigs/admin/v2/plots/:plotId/grid",
     *     summary="Generate grid for plots.",
     *     @OA\Response(
     *     response="200",
     *     description="Json"
     * )
     * )
     *
     * @param $plotId
     * @return object
     * @throws ValidationException
     */
    public function generateMultipleGrids()
    {
        $validator = Validator::make(Request::all(), [
            'gridIds' => 'required',
            'type' => 'required|in:2ha,5ha,iso,vra,custom',
        ]);

        $validator->sometimes('customGridCellArea', 'required|numeric', function ($params) {
            return $params->type == 'custom';
        });

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $gridIds = urldecode(Request::get('gridIds'));
        $gridIds = json_decode($gridIds, true);
        $type = Request::get('type');
        $customCellArea = Request::get('customGridCellArea');

        if ($type != 'custom') {
            $customCellArea = null;
        }

        $grids = [];
        foreach ($gridIds as $gridId) {
            $grids[] = json_decode($this->plotShape->generateGridByPlotId((int)$gridId, $type, $customCellArea));
        }

        return new Response(200, ['Content-Type: application/json'], json_encode($grids));
    }

    /**
     * @return Response
     * @throws ValidationException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function postSoilPlotsData()
    {
        $arrValidator = [
            'plotsData' => 'required|array',
        ];

        $validator = Validator::make(Request::all(), $arrValidator);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $plotsData = Request::get('plotsData');

        DB::beginTransaction();
        try {
            foreach ($plotsData as $plot) {
                $orderPlotRel = OrderPlotRel::where([['plot_uuid', $plot['plotUuid']], ['order_uuid', $plot['orderUuid']]])->first();

                if ($plot['sampler_id'] != null) {
                    if ($orderPlotRel->sampler_id != $plot['sampler_id']) {
                        $orderPlotRel->date_assigned = Carbon::now();
                    }
                    $orderPlotRel->sampler_id = $plot['sampler_id'];
                }
                $orderPlotRel->treatment_id = empty($plot['treatment_id']) ? null : implode($plot['treatment_id'],
                    ',');

                if (isset($plot['leaf_sample_cells']) && is_array($plot['leaf_sample_cells'])) {
                    $orderPlotRel->leaf_sample_cells = empty($plot['leaf_sample_cells']) ? '' : implode($plot['leaf_sample_cells'],
                        ',');
                }

                $orderPlotRel->save();
            }

            try {
                $this->contractService->changePackageFieldState('subscription', $plotsData);
            } catch (ClientException $client_exception) {
                return new Response(400, ['Content-Type: application/json'], 'Update field state is unavailable!');
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return new Response(400, ['Content-Type: application/json'], $e->getMessage());
        }
        return new Response(201, ['Content-Type: application/json']);

    }

    /**
     * @OA\Post(
     *     path="/apigs/admin/v2/order/exist",
     *     summary="Get existing orders by plot ids.",
     *     @OA\Response(
     *     response="200",
     *     description="Json"
     * )
     * )
     *
     * @return Response
     */
    public function getOrdersContainingPlots()
    {
        $plotsIds = urldecode(Request::get('plotsIds'));
        $plotsIds = json_decode($plotsIds, true);
        $ordersData = OrderPlotRel::findExistingOrdersByPlots($plotsIds);

        return new JsonResponse($ordersData, 200, ['Content-Type: application/json']);
    }

    /**
     * @OA\Post(
     *     path="/apigs/admin/v2/plots/grids/exist",
     *     summary="Get existsing grid by plots ids and order id.",
     *     @OA\Response(
     *     response="200",
     *     description="Json"
     * )
     * )
     *
     * @return Response
     */
    public function getExistingGridsData()
    {
        $plotsIds = urldecode(Request::get('plotsIds'));
        $orderId = urldecode(Request::get('orderId'));
        $plotsIds = json_decode($plotsIds, true);
        $orderId = json_decode($orderId, true);

        $existingGrids = $this->plotShape->getExistingGridsAsGeoJSON($plotsIds, $orderId);
        return new Response(200, ['Content-Type: application/json'], $existingGrids);
    }
}
