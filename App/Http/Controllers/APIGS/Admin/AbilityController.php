<?php
namespace App\Http\Controllers\APIGS\Admin;


use App\Http\Controllers\BaseController;
use App\Models\Ability;
use Illuminate\Http\Request;
use Validator;
use App\Exceptions\ValidationException;
use Bouncer;

class AbilityController extends BaseController
{
    public function index(Request $request)
    {   
        $abilitiesQuery = Ability::orderBy('id', 'desc');

        //Filter
        if ($request->get('name')) {
            $abilitiesQuery->where('name', 'ILIKE', trim('%' . $request->get('name') . '%'));
        }
        if ($request->get('title')) {
            $abilitiesQuery->where('title', 'ILIKE', trim('%' . $request->get('title') . '%'));
        }

        $abilities = $abilitiesQuery->paginate($request->get('limit'));

        return [
            'total' => $abilities->total(),
            'rows' => $abilities->items()
        ];
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'title' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $lowName = strtolower($request->get("name"));

        $ability = Bouncer::ability()->firstOrCreate([
            'name' => snake_case($lowName),
        ]);

        $ability->title = $request->get("title");
        $ability->save();
    }

    public function update(Ability $ability, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'title' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $lowName = strtolower($request->get("name"));
        $ability->name = snake_case($lowName);
        $ability->title = $request->get("title");
        $ability->save();
    }

    public function destroy(Ability $ability)
    {
        $ability->delete();
    }
}
