<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Events\GenerateWorkLayerMapFileEvent;
use App\Events\GenerateUserMapFileEvent;
use App\Http\Controllers\BaseController;
use Event;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Response;
use App\Models\Organization;
use App\Models\GlobalUser;
use App\Models\Farm;
use App\Models\User;
use DB;
use Auth;
use Config;
use Hash;
use Validator;
use App\Exceptions\ValidationException;
use App\Exceptions\ForbiddenException;
use App\Models\Role;
use Artisan;
use App\Models\Country;

class OrganizationsController extends BaseController
{

    /**
     * @param Request $request
     * @return array
     */
    public function index(Request $request)
    {
        $country = Country::find(Auth::user()->globalUser()->country);
        $orgQuery = Organization::with('stations')->where('iso_alpha_2_code',
            $country->iso_alpha_2_code)->where('service_provider_id',
            Auth::user()->globalUser()->serviceProvider->id);

        //Filter
        if ($request->get('username')) {
            $orgQuery->join('su_users', 'su_users.id', '=', 'su_organizations.created_by');
            $orgQuery->where('su_users.username', 'ILIKE', trim('%' . $request->get('username') . '%'));
        }
        if ($request->get('name')) {
            $orgQuery->where('name', 'ILIKE', trim('%' . $request->get('name') . '%'));
        }
        if ($request->get('email')) {
            $orgQuery->where('email', 'ILIKE', trim('%' . $request->get('email') . '%'));
        }

        $orgQuery->orderBy("su_organizations.id");
        $users = $orgQuery->paginate($request->get('limit'));

        return [
            'total' => $users->total(),
            'rows' => $users->items()
        ];
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'string|max:127',
            'address' => 'string|max:255',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $country = Country::find(Auth::user()->globalUser()->country);

        $organization = DB::transaction(function () use ($request, $country) {
            $organization = new Organization();
            $organization->name = $request->get("name");
            $organization->iso_alpha_2_code = $country->iso_alpha_2_code;
            $organization->email = $request->get("email");
            $organization->phone = $request->get("phone");
            $organization->address = $request->get("address");
            $organization->service_provider_id = Auth::user()->globalUser()->serviceProvider->id;
            $organization->save();

            $defaultFarm["name"] = "Main Farm";
            $defaultFarm["organization_id"] = $organization->id;
            $farm = Farm::create($defaultFarm);
            return $organization;
        });
    }

    public function update(Organization $organization, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'string|max:127',
            'address' => 'string|max:255',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organization->name = $request->get("name");
        $organization->email = $request->get("email");
        $organization->phone = $request->get("phone");
        $organization->address = $request->get("address");
        $organization->save();
    }

    public function toggleStatus(Organization $organization)
    {
        $organization->active = !$organization->active;
        $organization->save();
    }

    public function manageOrgUserRelation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'userId' => 'required|integer',
            'orgId' => 'required|integer',
            'action' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        /** @var Organization $org */
        $org = Organization::findOrFail($request->get("orgId"));

        /** @var User $user */
        $user = User::findOrFail($request->get("userId"));

        /** @var Farm[] $farms */
        $farms = $org->farms;

        if ($request->get("action") == "attach") {
            $this->attachUser($org, $user, $farms);
            return;
        }

        $this->detachUser($org, $user, $farms);
    }

    public function addUser(Organization $organization, User $user)
    {
        if($organization->users()->get()->contains($user->id)) {
            return new JsonResponse($user, 200);
        }

        $farms = $organization->farms;
        $this->attachUser($organization, $user, $farms);

        return new JsonResponse($user, 200);
    }

    public function removeUser(Organization $organization, User $user)
    {
        $farms = $organization->farms;
        $this->detachUser($organization, $user, $farms);

        return new JsonResponse($user, 200);
    }

    public function farms(Organization $organization)
    {
        return $organization->farms()->orderBy('created', 'desc')->get();
    }

    public function addFarm(Request $request, Organization $organization)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'comment' => 'string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $farm = $organization->farms()->create($request->all());

        return new JsonResponse($farm, 201);
    }

    public function deleteFarm(Request $request, Organization $organization)
    {
        $validator = Validator::make($request->all(), [
            'farmId' => 'required|integer'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        if (!$organization->farms()->where('id', $request->get('farmId'))->delete()) {
            throw new \Exception("Delete failed!");
        }

        return new JsonResponse("Deleted successfully!", 204);

    }

    public function updateFarm(Request $request, Organization $organization)
    {
        $validator = Validator::make($request->all(), [
            'farmId' => 'required|integer',
            'name' => 'required|string',
            'comment' => 'string|nullable'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $result = Farm::updateFarm($request->get('farmId'), $organization->id, $request->get('name'),
            $request->get('comment'));

        if (!$result) {
            throw new \Exception("Update failed!");
        }

        return new JsonResponse("Updated successfully!", 200);
    }

    private function detachUser(Organization $organization, User $user, $farms)
    {
        //Delete from su_organizations_users
        $organization->users()->detach($user);

        //Set last_chosen_organization_id
        if($user->last_chosen_organization_id == $organization->id) {
            $user->last_chosen_organization_id = $user->getLastAddedOrganizationId();
            $user->save();
        }

        foreach ($farms as $farm) {
            $farm->users()->detach($user);
        }
    }

    private function attachUser(Organization $organization, User $user, $farms)
    {
        //Insert in su_organizations_users
        $organization->users()->attach($user);

        //Set last_chosen_organization_id
        if(!$user->last_chosen_organization_id) {
            $user->last_chosen_organization_id = $organization->id;
            $user->save();
        }
        
        foreach ($farms as $farm) {
            $farm->users()->attach($user);
        }
    }
}
