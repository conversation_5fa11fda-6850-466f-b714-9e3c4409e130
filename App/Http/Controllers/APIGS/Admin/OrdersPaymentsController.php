<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use DB;
use App\Models\Payment;
use Request;
use Response;

//Validation
use Illuminate\Support\Facades\Validator;
use App\Exceptions\ValidationException;

class OrdersPaymentsController extends BaseController
{
    /**
     * @param $orderId
     * @return array
     */
    public function index($orderId)
    {
        if (!$orderId) {
            return array(
                'rows' => array(),
                'total' => 0
            );
        }

        $sort = Helper::getSortRequest();
        $limit = Helper::getLimitRequest();

        $query = Payment::where('order_id', '=', $orderId)
            ->select('id as payment_id', 'order_id', 'sum', 'date', 'note')
            ->restOrderBy($sort);

        $count = $query->get()->count();

        if (!$count) {
            return array(
                'rows' => array(),
                'total' => 0
            );
        }

        $totalPaySum = 0;
        $result = $query->paginate($limit)->each(function ($payment) use (&$totalPaySum) {

            $totalPaySum = $totalPaySum + $payment->sum;
        });

        return array(
            'rows' => $result,
            'total' => $count,
            'footer' => array(
                array('date' => '<b>Общо:</b>', 'sum' => $totalPaySum . " лв.")
            )
        );
    }

    /**
     * @param $orderId
     * @return mixed
     * @throws ValidationException
     */
    public function store($orderId)
    {
        $input = Request::all();

        $arrOrderId = array('order_id' => $orderId);
        $request = array_merge($arrOrderId, $input);

        $validator = Validator::make($request, [
            'order_id' => 'required|integer',
            'sum' => 'required|numeric',
            'note' => 'string|max:255',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $payment = new Payment;

        $payment->order_id = $orderId;
        $payment->sum = Request::get('sum');
        $payment->note = Request::get('note');
        $payment->date = date('o-m-d');
        $payment->save();

        return Response::json($payment);
    }

    /**
     * @param $orderId
     * @param $paymentId
     * @return mixed
     * @throws ValidationException
     */
    public function update($orderId, $paymentId)
    {
        $input = Request::all();

        $arrArguments = array(
            'order_id' => $orderId,
            'payment_id' => $paymentId,
        );
        $request = array_merge($arrArguments, $input);

        $validator = Validator::make($request, [
            'order_id' => 'required|integer',
            'payment_id' => 'required|integer',
            'sum' => 'required|numeric',
            'note' => 'string|max:255',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $payment = Payment::find($paymentId);
        $payment->sum = Request::get('sum');
        $payment->note = Request::get('note');
        $payment->save();

        return Response::json($payment);
    }

}
