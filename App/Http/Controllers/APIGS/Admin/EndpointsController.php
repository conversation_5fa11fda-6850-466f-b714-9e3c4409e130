<?php
namespace App\Http\Controllers\APIGS\Admin;


use App\Http\Controllers\BaseController;
use App\Models\ApiEndpoint;
use Illuminate\Http\Request;
use Validator;
use App\Exceptions\ValidationException;
use Bouncer;

class EndpointsController extends BaseController
{
    public function getAbilitiesByEndpoint(ApiEndpoint $endpoint, Request $request)
    {
    	$abilitiesAll = $endpoint->abilities()->get();
    	$abilities = $abilitiesAll->forPage($request->get('page'), $request->get('limit'));
    
    	return [
            'total' => count($abilitiesAll),
            'rows' => array_values($abilities->toArray())
        ];
    }	

    public function manageAbilities(ApiEndpoint $endpoint, Request $request)
    {
    	$validator = Validator::make($request->all(), [
            'ability_id' => 'required|integer',
            'action' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }	

        $action = $request->get("action");
        $abilityId = $request->get("ability_id");

        // Adding & removing abilities for endpoint
        $endpoint->abilities()->detach($abilityId);

        if($action == "attach"){
			$endpoint->abilities()->attach($abilityId);
        }
    }

    

    public function index(Request $request)
    {   
        //$endPoints = ApiEndpoint::orderBy('id', 'desc')->get();
        $endPointsQuery = ApiEndpoint::with('abilities')->restOrderBy($request->get('sort'));

        //Filter
        if ($request->get('class')) {
            $endPointsQuery->where('class', 'ILIKE', trim('%' . $request->get('class') . '%'));
        }
        if ($request->get('method')) {
            $endPointsQuery->where('method', 'ILIKE', trim('%' . $request->get('method') . '%'));
        }
        if ($request->get('description')) {
            $endPointsQuery->where('description', 'ILIKE', trim('%' . $request->get('description') . '%'));
        }

        $endPoints = $endPointsQuery->paginate($request->get('limit'));

        return [
            'total' => $endPoints->total(),
            'rows' => $endPoints->items()
        ];
    }
}
