<?php

namespace App\Http\Controllers\APIGS\Admin;

use App\Exceptions\ForbiddenException;
use App\Exceptions\NotFoundException;
use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use App\Models\Order;
use App\Models\OrderPlotRel;
use App\Models\API\ViewSoilOrderedPlots;
use App\Models\API\Notifications;
use App\Models\StaticModels\FarmingYear;
use App\Models\StaticModels\Dealer;
use App\Models\User;
use App\Models\Seller;
use App\Models\OrderVra;
use Auth;
use Request;
use Response;
use PDF;
use Excel;
//Validation
use Validator;
use App\Exceptions\ValidationException;
use Artisan;
use DB;
use Event;
use App\Events\SoilOrderPushNotificationEvent;
use App\Events\PushNotificationEvent;
use Carbon\Carbon;
use App\Jobs\RefreshMaterializedView;
use File;

class OrdersController extends BaseController
{

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * @return array
     */
    public function index()
    {
        $limit = Helper::getLimitRequest();

        $orders = $this->order->getOrders();

        $result = $orders->paginate($limit);

        $rows = $result->each(function ($order) {
            $order->status_txt = Order::getOrderStatuses()->get($order->status);
            $order->date = date('d.m.Y H:i', strtotime($order->date));
            $order->salesman = User::select('name')->where('id', $order->salesman)->first()['name'];
        });

        return array(
            'rows' => $rows,
            'total' => $result->total()
        );
    }

    /**
     * @param $orderId
     * @throws ValidationException
     */
    public function update($orderId)
    {
        $input = Request::all();

        $arrArguments = array(
            'orderId' => $orderId,
        );
        $request = array_merge($arrArguments, $input);

        $arrEnums = $this->order->getOrderStatusEnum();
        $arrStatusEnums = array();
        foreach ($arrEnums as $key => $value) {
            $arrStatusEnums[] = $value->enumlabel;
        }
        $sStatusEnums = implode(",", $arrStatusEnums);

        $validator = Validator::make($request, [
            'orderId' => 'required|integer',
            'status' => 'required|in:' . $sStatusEnums,
        ]);

        $validator->sometimes(['end_price'], 'required|numeric', function ($input) {
            return $input->status == 'waiting_payment';
        });
        $validator->sometimes(['salesman'], 'required|integer', function ($input) {
            return $input->status == 'waiting_payment';
        });


        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $order = Order::find($orderId);

        if (!$order || !$this->allowModify($order)) {
            return;
        }
        if (Request::get('status') == 'paid') {
            if ($order->status != 'waiting_payment') {
                throw new ValidationException('The status field value not allowed');
            }

            $order->status = Request::get('status');

            if ($order->type == 'vra') {
                $plot = $order->vraPlots()->first();
                $noty = array();
                $noty['group_id'] = $order->user_id;
                $noty['data'] = ['plots' => []];
                $noty['data']['plots'][] = $plot['gid'];
                $noty['data']['year'] = $order->year;
                $noty['data']['type'] = $order->type;
                $noty['plot_name'] = $plot['name'];

                $notyData = new Notifications();
                $notyData->group_id = $noty['group_id'];
                $notyData->title = trans('notifications.vra_orders', ['plotName' => $noty['plot_name']]);
                $notyData->message = trans('notifications.new_vra_orders_details', ['plotName' => $noty['plot_name']]);
                $notyData->data = json_encode($noty['data']);
                $notyData->type = 'processed_order';
                $notyData->is_pushed = false;
                $notyData->save();

                $order->status = 'processed';
                $order->save();

                event(new PushNotificationEvent($notyData->getId()));
            } else {
                $order->save();
            }

            return $order;
        }

        if ($order->type == 'soil') {
            //SEND SOIL-ORDERS PUSH NOTIFICATIONS
            $arrSamplers = OrderPlotRel::selectRaw("sampler_id")
                ->where('order_id', $orderId)
                ->get()->toArray();

            if (Request::get('status') == 'waiting_payment' && count($arrSamplers)) {
                for ($i = 0; $i < count($arrSamplers); $i++) {

                    if ($arrSamplers[$i]['sampler_id']) {
                        event(new SoilOrderPushNotificationEvent($arrSamplers[$i]['sampler_id']));

                        /*$exitCode = Artisan::call('soil-orders:send-push-notification', [
                            'groupId' => $arrSamplers[$i]['sampler_id']
                        ]);*/
                    }
                }
            }
        }

        $previousStatus = $order->status;

        if (Request::get('end_price') !== null) {
            $order->end_price = Request::get('end_price');
        }
        if (Request::get('salesman') !== null) {
            $order->salesman = Request::get('salesman');
        }
        if (Request::get('status') !== null) {
            $order->status = Request::get('status');
        }
        if (Request::get('status') == 'waiting_payment') {
            $order->approvedBy()->associate(Auth::user());
        }

        $order->save();
    }


    public function paymentOrderPDF()
    {
        $orderId = Request::get('orderId');
        if(!$orderId){
            return;
        }

        $order = $this->order->find($orderId);

        if (!$order || !$this->allowModify($order)) {
            return;
        }

        $orderPlots = $order->plots;

        $fullYearName = FarmingYear::getAll()->where('id', $order->year)->first()['title'];
        $totalArea = $orderPlots->sum('area');

        $printData = array(
            'orderId' => $orderId,
            'farm' => "",
            'year' => $fullYearName,
            'totalArea' => $totalArea,
            'plots' => $orderPlots->toArray(),
            'endPrice' => $order->end_price
        );

        $pdf = PDF::loadView('templates.satellite_payment_order', $printData);
        return $pdf->download('paymentOrder' . $orderId . '.pdf');
    }

    private function allowModify($order)
    {
        $allowModify = false;

        if (Auth::user()->globalUser()->isAn('SUPER_ADMIN') || Auth::user()->globalUser()->isAn('SAMPLER_ADMIN') || Auth::user()->globalUser()->isAn('SERVICE_ADMIN')) {
            $allowModify = true;
        }

        return $allowModify;
    }

    public function orderReportExcell()
    {
        $orderId = Request::get('orderId');
        if(!$orderId){
            return;
        }

        $order = $this->order->find($orderId);


        $filename = 'Order_' . $orderId;
        $allTreatments = array(
            0 => trans('general.samples_0_30'),
            1 => trans('general.samples_30_60'),
            2 => trans('general.leaf_samples')
        );

        $companyName = $order->company_name;
        $orderPlots = $order->plots;

        $areaCoef = config('globals.DKA_' . config('globals.AREA_UNIT'));

        $arrSamples = OrderPlotRel::from('su_satellite_orders_plots_rel as sopr')->selectRaw("sopr.ekatte_name, sopr.ekatte_code, sopr.sync_date, p.gid, p.name as plot_name, round((p.area*{$areaCoef})::numeric, 3) AS area, u.name, sp.sample_id, ssn.sample_number, ssn.treatment_type, ST_X(ST_Transform(sp.geom, 4326)), ST_Y(ST_Transform(sp.geom, 4326))")
            ->join('su_satellite_plots as p', 'p.gid', '=', 'sopr.plot_id')
            ->leftJoin('su_satellite_soil_points as sp', 'sp.sopr_id', '=', 'sopr.id')
            ->leftJoin('su_satellite_soil_sample_numbers as ssn', 'sp.gid', '=', 'ssn.gid')
            ->leftJoin('su_users as u', 'u.id', '=', 'sopr.sampler_id')
            ->where('order_id', $orderId)
            ->orderBy('plot_name', 'ASC')
            ->orderBy('sample_id', 'ASC')
            ->get()->toArray();

        $SamplerCollection = collect($arrSamples);
        $grouped = $SamplerCollection->groupBy('gid');
        $grouped->toArray();

        $printData[] = array(trans('general.client') . ':' . $companyName);
        $boldColumns[] = 1;
        foreach ($grouped as $key => $value) {
            $printData[] = array(trans('general.sampler') . ':' . $value[0]['name']);
            $boldColumns[] = count($printData);
            $printData[] = array(trans('general.area') . ':' . $value[0]['area'] . '(' . config('globals.AREA_UNIT_LABEL') . ')');
            $boldColumns[] = count($printData);

            $formattedDate = '';
            if ($value[0]['sync_date'] != null) {
                $formattedDate = Carbon::createFromFormat('Y-m-d H:i:s', $value[0]['sync_date'])->format('d.m.Y H:i');
            }
            $printData[] = array(trans('general.sync_date') . ':' . $formattedDate);
            $boldColumns[] = count($printData);

            $printData[] = array(
                'ekatte_code' => trans('general.ekatteCode'),
                'ekatte_name' => trans('general.ekatteName'),
                'plot_name' => trans('general.plot'),
                'sample_id' => trans('general.cellNumber'),
                'treatment_type' => trans('general.sampleType'),
                'sample_number' => trans('general.barcode'),
                'st_x' => trans('general.longitude'),
                'st_y' => trans('general.latitude'),
            );
            $boldColumns[] = count($printData);
            foreach ($value as $subkey => $subvalue) {
                $printData[] = array(
                    $subvalue['ekatte_code'],
                    $subvalue['ekatte_name'],
                    $subvalue['plot_name'],
                    $subvalue['sample_id'],
                    ($subvalue['treatment_type'] != null) ? $allTreatments[$subvalue['treatment_type']] : '',
                    $subvalue['sample_number'],
                    $subvalue['st_x'],
                    $subvalue['st_y'],
                );
            }
        }

        Excel::create($filename, function ($excel) use ($printData, $boldColumns) {

            $excel->sheet('Sheetname', function ($sheet) use ($printData, $boldColumns) {
                $sheet->fromArray($printData, null, 'A0');

                foreach ($boldColumns as $key => $value) {
                    $sheet->row($value, function ($row) {
                        $row->setFontWeight('bold');
                    });
                }
            });

        })->download('xls');
    }

    /**
     * @param $orderId
     * @return mixed
     * @throws ForbiddenException
     * @throws NotFoundException
     * @throws ValidationException
     */
    public function updateFromDate($orderId)
    {
        $arrArguments = array(
            'orderId' => $orderId,
        );
        $requestData = array_merge($arrArguments, Request::all());

        $validator = Validator::make($requestData, [
            'orderId' => 'required|integer',
            'from_date' => 'required|date_format:Y-m-d'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $order = Order::find($orderId);

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        if (!$this->allowModify($order)) {
            throw new ForbiddenException('Operation is not allowed');
        }

        if ($order->status != 'new') {
            throw new ValidationException('The status should be new');
        }

        $farmYearData = FarmingYear::getAll()->where('id', $order->year)->first();

        if (Request::get('from_date') < $farmYearData['from_date']) {
            throw new ValidationException('The from date should not be lower than ' . $farmYearData['from_date']);
        }

        $order->from_date = Request::get('from_date');
        $order->save();

        return $order;
    }

    /**
     * @param $orderId
     * @return mixed
     * @throws NotFoundException
     * @throws ValidationException
     */
    public function updateToDate($orderId)
    {
        $arrArguments = array(
            'orderId' => $orderId,
        );
        $requestData = array_merge($arrArguments, Request::all());

        $validator = Validator::make($requestData, [
            'orderId' => 'required|integer',
            'to_date' => 'required|date_format:Y-m-d'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $order = Order::find($orderId);

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        if (!$this->allowModify($order)) {
            throw new ValidationException('Operation is not allowed');
        }

        if ($order->status != 'new') {
            throw new ValidationException('The status should be new');
        }

        $farmYearData = FarmingYear::getAll()->where('id', $order->year)->first();

        if (Request::get('to_date') > $farmYearData['to_date']) {
            throw new ValidationException('The to date should not be greater than ' . $farmYearData['to_date']);
        }

        $order->to_date = Request::get('to_date');
        $order->save();

        return $order;
    }

    public function exportOrdersListExcell()
    {
        $orderTypes = array(
            'soil' => 'Soil Samples',
            'index' => 'Satellite Imaging',
            'meteo' => 'Meteo Data'
        );
        $orders = $this->order->getOrders();

        $result = $orders->get();

        $rows = collect($result)->each(function ($order) use ($orderTypes) {
            $order->status_txt = trans('general.' . (Order::getOrderStatuses()->get($order->status)));
            $order->date = date('d.m.Y H:i', strtotime($order->date));
            $order->salesman = User::select('name')->where('id', $order->salesman)->first()['name'];
            $order->type = trans('general.' . $orderTypes[$order->type]);
        });

        $printData = array();
        $headers = array(
            trans('general.number'),
            trans('general.orderType'),
            trans('general.organization_name'),
            trans('general.dealer'),
            trans('general.farmingYear'),
            trans('general.date'),
            trans('general.status'),
            trans('general.plotsCount'),
            trans('general.area'),
            trans('general.price'),
            trans('general.endPrice')
        );

        foreach ($rows as $subkey => $subvalue) {

            $printData[] = array(
                $headers[0] => $subvalue->order_id,
                $headers[1] => $subvalue->type,
                $headers[2] => $subvalue->organization_name,
                $headers[3] => $subvalue->salesman,
                $headers[4] => $subvalue->year,
                $headers[5] => $subvalue->date,
                $headers[6] => $subvalue->status_txt,
                $headers[7] => $subvalue->plots_num,
                $headers[8] => $subvalue->area,
                $headers[9] => $subvalue->price,
                $headers[10] => $subvalue->end_price
            );
        }

        $filename = 'OrdersList';
        Excel::create($filename, function ($excel) use ($printData) {

            $excel->sheet('Sheetname', function ($sheet) use ($printData) {
                $sheet->fromArray($printData, null, 'A1');

                $sheet->row(1, function ($row) {
                    $row->setFontWeight('bold');
                });
            });

        })->download('xls');
    }

    /**
     * @return array
     * @throws ValidationException
     */
    public function vraOrder()
    {

        $validator = Validator::make(Request::all(), [
            'orderId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $orderId = Request::get('orderId');


        $model = new OrderVra();

        try {
            $data = $model->loadVraByOrderId((int)$orderId);
        } catch (\Exception $e) {
            throw $e;
        }

        return array(
            'vra' => $data
        );
    }
}
