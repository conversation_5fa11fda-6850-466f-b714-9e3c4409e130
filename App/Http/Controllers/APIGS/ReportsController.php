<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use App\Models\LayerPlot;
use App\Models\OrderPlot;
use App\Models\Plot;
use App\Models\PlotNote;
use App\Models\SoilPoints;
use App\Models\StaticModels\FarmingYear;
use App\Services\Plot\PlotService;
use App\Services\Reports\PlotReportService;
use Auth;
use Config;
use DB;
use File;
use PDF;
use Request;
use Response;
use Validator;
use View;

class ReportsController extends BaseController
{

    /**
     * @OA\Post(
     *     path="/apigs/reports/download-plot-report",
     *     summary="Download plot report",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @return mixed
     * @throws ValidationException
     */
    public function postDownloadPlotReport()
    {
        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'year' => 'required|string',
            'from_date' => 'date_format:Y-m-d',
            'to_date' => 'date_format:Y-m-d',
            'uri_image_index' => 'nullable|string',
            'uri_images_soil' => 'nullable|array',
            'mean_index_legend' => 'nullable|array',
            'selected_index_data' => 'nullable|array',
            'uri_image_mean_index' => 'nullable|string',
            'gdd_image_uri' => 'nullable|string',
            'gdd_current_phase' => 'nullable|string',
            'gdd_sowing_days' => 'nullable|integer',
            'gdd_sowing' => 'nullable|string',
            'gdd_crop' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $crop_name = Config::get('globals.CROP_NAME');

        $plot = Plot::select([
            'su_satellite_plots.gid',
            'su_satellite_plots.name',
            DB::raw("ST_Extent(geom) as extent"),
            DB::raw("ST_XMin (geom) AS xmin"),
            DB::raw("ST_XMax (geom) AS xmax"),
            DB::raw("ST_YMin (geom) AS ymin"),
            DB::raw("ST_YMax (geom) AS ymax"),
            DB::raw("round((su_satellite_plots.area * {$areaCoef})::numeric, 3) as area"),
            DB::raw("MAX(slp.date_time) AS last_image_date_time"),
            DB::raw("MAX(slp.date) AS last_image_date"),
            DB::raw('(array_agg(slp.satellite_type ORDER BY slp.date DESC))[1] AS last_satellite_type'),
            DB::raw('(array_agg(slp.mean ORDER BY slp.date DESC))[1] AS last_mean'),
            DB::raw('(array_agg(slp.stats ORDER BY slp.date DESC))[1] AS last_stats'),
            DB::raw('ST_AsText(ST_Transform(geom, 900913)) AS geom'),
            DB::raw('ST_Extent(ST_Transform(geom, 900913)) AS extent_900913'),
            DB::raw('(ST_XMax(ST_Extent(ST_Transform(geom, 900913))) - ST_XMin(ST_Extent(ST_Transform(geom, 900913)))) AS extent_width_900913'),
            DB::raw('(ST_YMax(ST_Extent(ST_Transform(geom, 900913))) - ST_YMin(ST_Extent(ST_Transform(geom, 900913)))) AS extent_height_900913'),
            DB::raw('max(DISTINCT c.' . $crop_name . ') as crop'),
            DB::raw('(array_agg(slps.stats ORDER BY slps.date DESC))[1] AS last_stats_soil'),
            DB::raw("MAX(slps.date_time) AS last_soil_date"),
            DB::raw('COALESCE(COUNT(DISTINCT slps. ID), 0) as count_soil_map'),
            DB::raw('COALESCE(COUNT(DISTINCT (slp.date::TEXT || \'_\' || slp.plot_id::TEXT) ORDER BY (slp.date::TEXT || \'_\' || slp.plot_id::TEXT)), 0) as count_index_images'),
            DB::raw('COALESCE(count(DISTINCT pin.id), 0) as count_pins')
        ])
            ->leftJoin('su_satellite_layers_plots AS slp', function ($join) {
                $join->on('slp.plot_id', '=', 'su_satellite_plots.gid')
                    ->where('slp.type', '=', 'index')
                    ->where(DB::raw('slp.date::DATE'), '>=', Request::get('from_date'))
                    ->where(DB::raw('slp.date::DATE'), '<=', Request::get('to_date'));
            })
            ->leftJoin('su_satellite_layers_plots AS slps', function ($join) {
                $join->on('slps.plot_id', '=', 'su_satellite_plots.gid')
                    ->where('slps.type', '=', 'soil')
                    ->where('slps.stats_type', '=', 'summarized')
                    ->where(DB::raw('slps.date::DATE'), '>=', Request::get('from_date'))
                    ->where(DB::raw('slps.date::DATE'), '<=', Request::get('to_date'));
            })
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'su_satellite_plots.gid')
                    ->where('spc.is_primary', '=', true);
            })
            ->leftJoin('su_crop_codes AS c', 'c.id', '=', 'spc.crop_id')
            ->leftJoin('su_users_pins AS pin', function ($join) {
                $join->where('pin.group_id', '=', Auth::user()->group_id)
                    ->where(DB::raw('ST_Contains(su_satellite_plots.geom, ST_Transform(ST_SetSRID(ST_MakePoint(pin.lon, pin.lat), 900913), ' . Config::get("globals.DEFAULT_DB_CRS") . '))'),
                        '=', true);
            })
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('su_satellite_plots.gid', Request::get('gid'))
            ->groupBy('gid')
            ->first();

        if (!$plot) {
            return;
        }

        $imgPath = Config::get('globals.PROCESSED_ORDERS_PATH') . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR;

        $selectedIndexData = Request::get('selected_index_data');

        $lastIndexPlotImage = "";
        $plotIndexImage = LayerPlot::plotIndexImagePath(Request::get('gid'), $selectedIndexData['selected_index_date'], $selectedIndexData['selected_satellite_type']);
        if(!empty($plotIndexImage->path)) {
            $lastIndexPlotImage = $plotIndexImage->path;
        }

        $this->extentPlot = Helper::toOpenLayerFormat($plot['extent']);

        $this->xmin = $plot['xmin'];
        $this->xmax = $plot['xmax'];
        $this->ymin = $plot['ymin'];
        $this->ymax = $plot['ymax'];

        //Create Temp Soil Map Points Images
        $this->createSoilMapPointsImages($plot);

        $arrImagesSoil = $this->imagesSoil();

        $meanIndexLegendFirst = Config::get('reports.REPORT_PATH') . "mean_index_legend_first.png";
        $meanIndexLegendSecond = Config::get('reports.REPORT_PATH') . "mean_index_legend_second.png";

        $meanIndexLegend = Request::get('mean_index_legend');

        $selectedImageDateObj = date_create($selectedIndexData['selected_index_date']);
        $selectedImageDate = date_format($selectedImageDateObj, Config::get('globals.DATE_FORMAT'));

        //Get Notes    
        $plotNotes = $this->notes();

        $year = Request::get('year');
        $farmYears = FarmingYear::byOrganization(Auth::user()->lastChosenOrganization);
        $plotCrops = Plot::getPlotCrops(Request::get('gid'));
        $farmYears = PlotService::addCropDataToFarmYears($plotCrops, $farmYears, $year);
        $wizardParam = PlotReportService::getWizardParams($farmYears);

        $printData = array(
            'title' => trans('reportPlot.title'),
            'name_txt' => trans('reportPlot.name'),
            'name_value' => $plot['name'],
            'area_txt' => trans('reportPlot.area'),
            'area_value' => $plot['area'],
            'area_unit_label' => Config::get('globals.AREA_UNIT_LABEL'),
            'crop_txt' => trans('reportPlot.crop'),
            'crop_value' => $plot['crop'],
            'farm_year_txt' => trans('reportPlot.farm_year'),
            'farm_year_value' => Request::get('year'),
            'count_index_images_txt' => trans('reportPlot.count_index_images'),
            'count_index_images_value' => $plot['count_index_images'],
            'count_soil_map_txt' => trans('reportPlot.count_soil_map'),
            'count_soil_map_value' => $plot['count_soil_map'],
            'count_pins_txt' => trans('reportPlot.count_pins'),
            'count_pins_value' => $plot['count_pins'],

            'selected_image_txt' => trans('reportPlot.selected_image'),
            'selected_image_date' => $selectedImageDate,
            'selected_stats' => $selectedIndexData['selected_index_stats'],
            'uri_image_index' => Request::get('uri_image_index'),
            'last_index_plot_image' => $lastIndexPlotImage,
            'images_soil' => $arrImagesSoil,
            'soil_analysis_txt' => trans('reportPlot.soil_analysis'),
            'index_txt' => trans('reportPlot.index_txt'),
            'uri_image_mean_index' => Request::get('uri_image_mean_index'),
            'mean_index_analysis_txt' => trans('reportPlot.mean_index_analysis_txt'),
            'mean_index_legend_first' => $meanIndexLegendFirst,
            'mean_index_legend_second' => $meanIndexLegendSecond,
            'mean_index_legend_first_txt' => isset($meanIndexLegend['first_txt']) ? $meanIndexLegend['first_txt'] : null,
            'mean_index_legend_second_txt' => isset($meanIndexLegend['second_txt']) ? $meanIndexLegend['second_txt'] : null,
            'gdd_image_uri' => Request::get('gdd_image_uri'),

            'gdd_legend_first' => trans('reportPlot.gdd_legend_first'),
            'gdd_legend_second' => trans('reportPlot.gdd_legend_second'),
            'sowing_txt' => trans('reportPlot.sowing_txt'),
            'crop_txt' => trans('reportPlot.crop_txt'),
            'days_from_sowing_txt' => trans('reportPlot.days_from_sowing_txt'),
            'phase_of_evolution_txt' => trans('reportPlot.phase_of_evolution_txt'),

            'gdd_current_phase' => Request::get('gdd_current_phase'),
            'gdd_days_from_sowing' => Request::get('gdd_sowing_days'),
            'gdd_sowing' => Request::get('gdd_sowing'),
            'gdd_crop' => Request::get('gdd_crop'),

            'area_coef' => $areaCoef,

            'plot_notes' => $plotNotes,
            'notes_txt' => trans('reportPlot.notes_txt'),

            'farm_years' => $farmYears,
            'wizard_padding' => $wizardParam['padding'],
            'wizard_width' => $wizardParam['width'],
        );

        $pdf = PDF::loadView('templates.report_plot', $printData)->setPaper('a4',
            'portrait')->setOptions(['dpi' => 150]);

        $year = str_replace('/', '-', $year);
        $fileName = 'report_plot_' . Request::get('gid') . '_' . $year . '.pdf';
        $filePath = config('reports.REPORT_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->group_id . DIRECTORY_SEPARATOR . $fileName;

        if (!is_dir(config('reports.REPORT_PATH') . config('globals.MACHINE'))) {
            mkdir(config('reports.REPORT_PATH') . config('globals.MACHINE'), 0777);
        }

        if (file_exists($filePath)) {
            unlink($filePath);
        }

        $contentPdf = $pdf->download('reportPlot' . Request::get('gid') . '.pdf');

        @mkdir(dirname($filePath), 0777, true);

        if (!file_put_contents($filePath, $contentPdf)) {
            return Response::json(array('error' => 'No file saved.'), 500);
        }

        //Delete Temp Soil Map Points Images
        $this->deleteSoilMapPointsImages();

        return Response::json(array('fileName' => $fileName));
    }

    /**
     * @OA\Post(
     *      path="/apigs/reports/download-soil-report",
     *     summary="Download soil report",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @return mixed
     * @throws ValidationException
     */
    public function postDownloadSoilReport()
    {

        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'uri_images_soil' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $crop_name = Config::get('globals.CROP_NAME');

        $plot = Plot::select([
            'su_satellite_plots.gid',
            'su_satellite_plots.name',
            DB::raw("round((su_satellite_plots.area * {$areaCoef})::numeric, 3) as area"),
            DB::raw("ST_Extent(geom) as extent"),
            DB::raw("ST_XMin (geom) AS xmin"),
            DB::raw("ST_XMax (geom) AS xmax"),
            DB::raw("ST_YMin (geom) AS ymin"),
            DB::raw("ST_YMax (geom) AS ymax"),
            DB::raw('ST_AsText(ST_Transform(geom, 900913)) AS geom'),
            DB::raw('ST_Extent(ST_Transform(geom, 900913)) AS extent_900913'),
            DB::raw('(ST_XMax(ST_Extent(ST_Transform(geom, 900913))) - ST_XMin(ST_Extent(ST_Transform(geom, 900913)))) AS extent_width_900913'),
            DB::raw('(ST_YMax(ST_Extent(ST_Transform(geom, 900913))) - ST_YMin(ST_Extent(ST_Transform(geom, 900913)))) AS extent_height_900913'),
            DB::raw('max(DISTINCT c.' . $crop_name . ') as crop'),
            DB::raw('COALESCE(count(DISTINCT pin.id), 0) as count_pins')
        ])
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'su_satellite_plots.gid')
                    ->where('spc.is_primary', '=', true);
            })
            ->leftJoin('su_crop_codes AS c', 'c.id', '=', 'spc.crop_id')
            ->leftJoin('su_users_pins AS pin', function ($join) {
                $join->where('pin.group_id', '=', Auth::user()->group_id)
                    ->where(DB::raw('ST_Contains(su_satellite_plots.geom, ST_Transform(ST_SetSRID(ST_MakePoint(pin.lon, pin.lat), 900913), ' . Config::get("globals.DEFAULT_DB_CRS") . '))'),
                        '=', true);
            })
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('su_satellite_plots.gid', Request::get('gid'))
            ->groupBy('gid')
            ->first();

        if (!$plot) {
            return;
        }

        $this->extentPlot = Helper::toOpenLayerFormat($plot['extent']);

        $this->xmin = $plot['xmin'];
        $this->xmax = $plot['xmax'];
        $this->ymin = $plot['ymin'];
        $this->ymax = $plot['ymax'];

        //Create Temp Soil Map Points Images
        $this->createSoilMapPointsImages($plot);

        $arrImagesSoil = $this->imagesSoil();

        //Get Notes    
        $plotNotes = $this->notes();

        $printData = array(
            'title' => trans('reportPlot.title'),
            'name_txt' => trans('reportPlot.name'),
            'name_value' => $plot['name'],
            'area_txt' => trans('reportPlot.area'),
            'area_value' => $plot['area'],
            'area_unit_label' => Config::get('globals.AREA_UNIT_LABEL'),
            'crop_txt' => trans('reportPlot.crop'),
            'crop_value' => $plot['crop'],
            'count_soil_map_txt' => trans('reportPlot.count_soil_map'),
            'count_soil_map_value' => count($arrImagesSoil),
            'count_pins_txt' => trans('reportPlot.count_pins'),
            'count_pins_value' => $plot['count_pins'],

            'images_soil' => $arrImagesSoil,
            'soil_analysis_txt' => trans('reportPlot.soil_analysis'),

            'plot_notes' => $plotNotes,
            'notes_txt' => trans('reportPlot.notes_txt'),
        );

        $pdf = PDF::loadView('templates.report_soil', $printData)->setPaper('a4',
            'portrait')->setOptions(['dpi' => 150]);

        $fileName = 'report_soil_' . Request::get('gid') . '.pdf';
        $filePath = config('reports.REPORT_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->group_id . DIRECTORY_SEPARATOR . $fileName;

        if (!is_dir(config('reports.REPORT_PATH') . config('globals.MACHINE'))) {
            mkdir(config('reports.REPORT_PATH') . config('globals.MACHINE'), 0777);
        }

        if (file_exists($filePath)) {
            unlink($filePath);
        }

        $contentPdf = $pdf->download('reportPlot' . Request::get('gid') . '.pdf');

        @mkdir(dirname($filePath), 0777, true);

        if (!file_put_contents($filePath, $contentPdf)) {
            return Response::json(array('error' => 'No file saved.'), 500);
        }

        //Delete Temp Soil Map Points Images
        $this->deleteSoilMapPointsImages();

        return Response::json(array('fileName' => $fileName));
    }

    private function createSoilMapPointsImages($plot)
    {

        $this->result = LayerPlot::select([
            'su_satellite_layers_plots.layer_name',
            DB::raw("su_satellite_layers_plots.element"),
            DB::raw("su_satellite_layers_plots.stats_type"),
            'su_satellite_layers_plots.date',
            'su_satellite_layers_plots.stats',
            'su_satellite_layers_plots.plot_id',
            'su_satellite_layers_plots.order_id',
            'su_satellite_layers_plots.probe',
            'sopr.id',
            'sopr.soil_sample_status',
            'slpf.path'
        ])
            ->join('su_satellite_orders_plots_rel AS sopr', function ($join) {
                $join->on('sopr.plot_id', '=', 'su_satellite_layers_plots.plot_id')
                    ->on('sopr.order_id', '=', 'su_satellite_layers_plots.order_id');
            })
            ->join('su_satellite_plots AS p', 'p.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'p.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->join('su_satellite_layers_plots_files as slpf', 'slpf.layer_plot_id', '=', 'su_satellite_layers_plots.id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('su_satellite_layers_plots.plot_id', Request::get('gid'))
            ->where('su_satellite_layers_plots.type', 'soil')
            ->where('su_satellite_layers_plots.stats_type', 'summarized')
            ->where('sopr.soil_sample_status', 'processed')
            ->where('slpf.type', 'TIFF')
            ->get()->toArray();

        $this->createTmpImages($this->result, $plot);
    }

    private function deleteSoilMapPointsImages()
    {

        $result = $this->result;

        for ($i = 0; $i < count($result); $i++) {

            $imgPath = Config::get('globals.PROCESSED_ORDERS_PATH') . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->group_id . '/soils/tmp_' . $result[$i]['date'] . '/';

            File::deleteDirectory($imgPath);
        }

    }

    private function createTmpImages($result, $plot)
    {

        $soilUserPath = Config::get('globals.PROCESSED_ORDERS_PATH') . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->group_id . '/soils/';

        for ($i = 0; $i < count($result); $i++) {

            $imgPath = $soilUserPath . 'tmp_' . $result[$i]['date'] . '/';

            if (!File::exists($imgPath)) {
                File::makeDirectory($imgPath, 0755, true);
            }

            $element = $result[$i]['element'];

            $sourceFile = $result[$i]['path'];

            $outImg = $imgPath . 'tmp_' . $result[$i]['plot_id'] . '_' . $result[$i]['date'] . '_' . $element . '_' . $result[$i]['stats_type'] . '_points.png';

            if ($result[$i]['probe']) {
                $outImg = $imgPath . 'tmp_' . $result[$i]['plot_id'] . '_' . $result[$i]['date'] . '_' . $element . '_' . $result[$i]['stats_type'] . '_' . $result[$i]['probe'] . '_points.png';
            }

            $this->makePlotPng($result[$i]['id'], $sourceFile, $plot, $outImg, $element);
        }
    }

    /**
     * Creates an RGB Png file from the shp2img.map file.
     * @param $sourceFile
     * @param $plot
     * @param string $type The scale type.
     * @return string
     */
    private function makePlotPng($soprId, $sourceFile, $plot, $outImg, $element)
    {

        $ratio = $plot->extent_height_900913 / $plot->extent_width_900913;
        $extent = Helper::toOpenLayerFormat($plot->extent_900913);

        $width = 600;
        $height = round(600 * $ratio, 3);

        $pointsQuery = "\"geom FROM (select gid, sopr_id, sample_id, ST_Transform(geom, 3857) as geom from su_satellite_soil_points where sopr_id={$soprId}) AS subquery USING UNIQUE gid USING srid=3857\"";
        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'shp2img.map';
        $shp2imgLayer = 'shp2img_layer_' . Config::get('globals.MACHINE') . '_soil_' . $element;
        $pointsLayer = 'shp2img_layer_soil_points';

        $shp2img = Config::get('globals.SHP2IMG');
        $command = "{$shp2img} -m {$mapFilePath} -l \"{$shp2imgLayer} {$pointsLayer}\" -s {$width} {$height} -e {$extent} -d \"{$shp2imgLayer}\" {$sourceFile} -d \"{$pointsLayer}\" {$pointsQuery}";
        $command .= " -o \"{$outImg}\"";

        $output = array();
        exec($command . " 2>&1", $output);
    }

    private function pointsMapData($result)
    {
        $queryPoints = SoilPoints::selectRaw("gid, sopr_id, sample_id, geom")
            ->where('sopr_id', $result['id'])
            ->toSqlWithBindings();

        $layerNamePoints = $result['layer_name'] . "_points_" . $result['plot_id'];

        $defaultdb = Config::get('database.default');

        $dataPoints = [
            'layername' => $layerNamePoints,
            'type' => 'POINT',
            'group' => '',
            'maxextent' => $this->extentPlot,
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$queryPoints}) AS subquery USING UNIQUE gid",
            'gid' => "gid",
            'transparency' => "100",
            'tag_label' => "sample_id",
            'classitem' => false,
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'classes' => [
                [
                    "name" => $layerNamePoints . "_ready",
                    "symbol" => [
                        "name" => "circle",
                        "size" => "10",
                        "color" => "'#2aec09'",
                        "outlinecolor" => false,
                        "angle" => false,
                    ],
                    "border_color" => '0 131 201',
                    "label_color" => '0 0 0',
                    "tags" => true,
                    "size" => 10,
                    "width" => 2,
                    "label_text" => "'[sample_id]'"
                ]
            ]
        ];

        return View::make('maps.soil-grid', $dataPoints)->render();
    }

    private function imagesSoil()
    {

        $imgPath = Config::get('globals.PROCESSED_ORDERS_PATH') . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR;

        $arrImagesSoil = array_map(function ($row) use ($imgPath) {

            $name = str_replace('soil_raster_', '', $row['layer_name']);
            $arrName = explode("_", $name);
            $date = isset($arrName[0]) ? $arrName[0] : '';
            $compound = isset($arrName[1]) ? $arrName[1] : '';

            $unit = explode("-", $compound);
            $unit = isset($unit[1]) ? $unit[1] : '';

            if (strlen($unit)) {
                $name = str_replace('-' . $unit, '', $name);
            }

            $type = isset($arrName[2]) ? $arrName[2] : '';

            $lastValue = end($arrName);

            $depth = trans('reportPlot.depth') . ' ' . $lastValue;

            if ($lastValue == "summarized" || $lastValue == "absolute") {
                $depth = "";
            }

            $plotMapSoilPath = $imgPath . Auth::user()->group_id . '/soils/tmp_' . $date . '/tmp_' . Request::get('gid') . '_' . $name . '_points.png';

            $dateObj = date_create($date);

            $arrData = array(
                'stat_image_uri' => $row['image_uri'],
                'path_soil_map' => $plotMapSoilPath,
                'date' => date_format($dateObj, Config::get('globals.DATE_FORMAT')),
                'compound' => str_replace('-', ' ', $compound),
                'type' => trans('reportPlot.' . $type),
                'depth' => $depth,
            );
            return $arrData;
        }, Request::get('uri_images_soil'));

        return $arrImagesSoil;
    }

    private function notes()
    {
        //Get Notes
        $plotNotes = PlotNote::select([
            'su_satellite_plots_notes.id',
            'su_satellite_plots_notes.plot_id',
            'su_satellite_plots_notes.note',
            DB::raw("to_char(su_satellite_plots_notes.created_at, '" . Config::get('globals.DATE_FORMAT_SQL') . " HH24:MI:SS') as created_date")
        ])
            ->join('su_satellite_plots', 'su_satellite_plots.gid', '=', 'su_satellite_plots_notes.plot_id')
            ->where('su_satellite_plots_notes.plot_id', Request::get('gid'))
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->orderBy('su_satellite_plots_notes.id', 'desc')->get();

        return $plotNotes;
    }
}
