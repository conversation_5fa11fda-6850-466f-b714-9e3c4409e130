<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Helpers\Helper;
use App\Http\Controllers\BaseController;
use App\Models\Order;
use App\Models\OrderPlot;
use App\Models\OrderPlotRel;
use App\Models\OrderSatelliteVra;
use App\Models\OrderSoilVra;
use App\Models\Plot;
use Auth;
use Carbon\Carbon;
use Config;
use DateInterval;
use DB;
use Event;
use Exception;
use Plupload;
use Request;
use Response;
use Validator;
use Webpatser\Uuid\Uuid;

class OrdersController extends BaseController
{

    public function __construct()
    {
        $this->middleware('verify-write-rights', ['only' => ['postStore']]);
    }

    /**
     * @OA\Get(
     *     path="/apigs/orders",
     *     summary="Get orders",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getIndex()
    {
        $validator = Validator::make(Request::all(), [
            'sort' => 'required|string',
            'order' => 'required|string',
            'limit' => 'required|string',
            'page' => 'required|integer',
            'year' => 'nullable|integer',
            'name' => 'nullable|string',
            'crop_id' => 'nullable|integer',
            'area_from' => 'nullable|integer',
            'area_to' => 'nullable|integer',
            'order_type' => 'nullable|json',
            'farms' => 'nullable|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $sort = Request::get('sort');
        $limit = Request::get('limit');
        $usedFilter = Request::get('used_filter');
        $farmYear = Request::get('year');

        $organizationId = Auth::user()->lastChosenOrganization->id;

        if ($usedFilter) {
            $q = Plot::join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
                ->join('su_satellite_orders', 'su_satellite_orders.id', '=', 'sopr.order_id')
                ->leftJoin('su_satellite_layers_plots AS slp', function ($join) {
                    $join->on('slp.plot_id', '=', 'su_satellite_plots.gid')
                        ->on('slp.type', '=', 'su_satellite_orders.type');
                })
                ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                    $join->on('spc.plot_id', '=', 'su_satellite_plots.gid')
                        ->where('spc.is_primary', '=', true);
                })
                ->leftJoin('su_crop_codes AS c', 'c.id', '=', 'spc.crop_id')
                ->whereRaw("(CASE WHEN slp.type = 'soil' THEN slp.layer_name LIKE '%summarized%' ELSE TRUE END)")
                ->where('su_satellite_orders.organization_id', $organizationId)
                ->groupBy('su_satellite_orders.id');

            if (Request::get('name') && trim(Request::get('name')) !== '') {
                $q->where('name', 'ILIKE', trim('%' . Request::get('name') . '%'));
            }
            if (Request::get('farms')) {
                $arrFarms = json_decode(Request::get('farms'), true);
                $q->whereIn('su_satellite_plots.farm_id', $arrFarms);
            }
            if (Request::get('crop_id')) {
                $q->where('spc.crop_id', Request::get('crop_id'));
            }
            if (Request::get('area_from')) {
                $q->where('su_satellite_plots.area', '>=', Request::get('area_from'));
            }
            if (Request::get('area_to')) {
                $q->where('su_satellite_plots.area', '<=', Request::get('area_to'));
            }
            if (Request::get('order_type')) {
                $q->whereIn('su_satellite_orders.type', json_decode(Request::get('order_type')));
            }
        } else {
            $q = Order::where('su_satellite_orders.organization_id', $organizationId);
        }

        if ($farmYear) {
            $q->where("su_satellite_orders.year", '=', $farmYear);
        }
        if ($sort) {
            $q->orderBy($sort);
        }

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $opResult = $q->paginate($limit, [
            "su_satellite_orders.id",
            "su_satellite_orders.type",
            "su_satellite_orders.status",
            "su_satellite_orders.price",
            "su_satellite_orders.end_price",
            DB::raw("round((su_satellite_orders.area*{$areaCoef})::numeric, 3) as area"),
            "su_satellite_orders.from_date",
            "su_satellite_orders.to_date",
            "su_satellite_orders.date"
        ]);

        return [
            'total' => $opResult->total(),
            'rows' => $opResult->items()
        ];
    }

    /**
     * @OA\Post(
     *     path="/apigs/orders/store",
     *     summary="Create order",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function postStore()
    {
        $user = Auth::user();
        $availableDemoArea = 0;

        $validator = Validator::make(Request::all(), [
            'farm' => 'string',
            'type' => 'required|array',
            'dates.*.from' => 'required|date',
            'dates.*.to' => 'required|date',
            'dates.*.year' => 'required|integer',
            'company_name' => 'required|string',
            'company_eik' => 'required|integer',
            'company_mol' => 'nullable|string',
            'company_address' => 'nullable|string',
            'note' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $types = Request::get('type');

        foreach ($types as $key => $value) {

            $availableDemoArea = $this->createOrder($value['id']);
        }


        if ($user->is_trial) {
            return [
                'available_demo_area' => $availableDemoArea
            ];
        }
    }

    /**
     * @OA\Get(
     *     path="/apigs/orders/vra-orders",
     *     summary="Get VRA orders",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getVraOrders()
    {
        $validator = Validator::make(Request::all(), [
            'plotId' => 'required|integer',
            'farmYear' => 'integer|required'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $vraModel = new OrderSatelliteVra();
        $soilVraModel = new OrderSoilVra();
        $plotId = Request::get('plotId');
        $farmYear = Request::get('farmYear');

        try {
            $vraOrders = $vraModel->getByPlot((int)$plotId, (int)$farmYear);
            $soilVraOrders = $soilVraModel->getByPlot((int)$plotId, (int)$farmYear);
        } catch (Exception $e) {
            throw $e;
        }

        $orders = array_merge($vraOrders->toArray(), $soilVraOrders->toArray());

        return $orders;
    }

    /**
     * @OA\Post(
     *     path="/apigs/orders/",
     *     summary="Get soil VRA orders",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param $orderId
     * @throws ValidationException
     */
    public function duplicateOrder($orderId)
    {

        $validator = Validator::make([
            'order_id' => $orderId
        ], [
            'order_id' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        DB::beginTransaction();

        try {
            $order = Order::find($orderId);

            if (!$order) {
                return;
            }

            //clone existed order
            $newOrder = $order->replicate();

            //change properties for the new order
            $newOrder->year = $order->year + 1;
            $newOrder->date = Carbon::now();
            $newOrder->from_date = $order->from_date->add(new DateInterval('P1Y'));
            $newOrder->to_date = $order->to_date->add(new DateInterval('P1Y'));
            $newOrder->status = 'new';
            $newOrder->createdBy()->associate(Auth::user());
            $newOrder->save();

            if ($order->orderPlots->count() > 0) {
                $orderPlotsArray = array();

                foreach ($order->orderPlots as $orderPlots) {
                    $orderPlotsCopy = $orderPlots->replicate();
                    $orderPlotsCopy->order_id = $newOrder->id;

                    array_push($orderPlotsArray, $orderPlotsCopy);
                }

                $newOrder->orderPlots()->saveMany($orderPlotsArray);
            }

            DB::commit();
            return Response::json(array('Response' => 'Successfully added order.'));

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * @OA\Post(
     *     path="/apigs/orders/:orderId/plots/duplicate",
     *     summary="Duplicate order",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param $orderId
     * @return mixed
     * @throws ValidationException
     */
    public function duplicateOrderByPlots($orderId)
    {

        $validator = Validator::make(Request::all(), [
            'order_id' => 'required|numeric',
            'gids' => 'required|array|filled'
        ]);


        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotIds = Request::get('gids');

        DB::beginTransaction();

        try {
            $order = Order::find($orderId);

            if (!$order) {
                return;
            }

            //clone existed order
            $newOrder = $order->replicate();

            //change properties for the new order
            $newOrder->year = $order->year + 1;
            $newOrder->date = Carbon::now();
            $newOrder->from_date = $order->from_date->add(new DateInterval('P1Y'));
            $newOrder->to_date = $order->to_date->add(new DateInterval('P1Y'));
            $newOrder->status = 'new';
            $newOrder->end_price = null;
            $newOrder->createdBy()->associate(Auth::user());

            $orderPlotsArray = array();
            $oderPlotsPrice = 0;
            $oderPlotsArea = 0;

            $filteredOrderPlots = $order->orderPlots()
                ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_orders_plots_rel.plot_id')
                ->whereIn('plot_id', $plotIds)
                ->get(["su_satellite_orders_plots_rel.*", "sp.area"])
                ->each(function ($orderPlots) use ($newOrder, &$orderPlotsArray, &$oderPlotsPrice, &$oderPlotsArea) {
                    //clone existed order plots
                    $orderPlotsCopy = $orderPlots->replicate();
                    $orderPlotsCopy->order_id = $newOrder->id;

                    //sum order plots area and price
                    $oderPlotsPrice += $orderPlotsCopy->price;
                    $oderPlotsArea += $orderPlotsCopy->area;

                    unset($orderPlotsCopy->area);
                    array_push($orderPlotsArray, $orderPlotsCopy);
                });

            $newOrder->price = $oderPlotsPrice;
            $newOrder->area = $oderPlotsArea;
            $newOrder->save();

            $newOrder->orderPlots()->saveMany($orderPlotsArray);

            DB::commit();
            return Response::json(array('Response' => 'Successfully added order.'));

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * @param $type
     * @return float|int|mixed
     * @throws Exception
     * @deprecated
     *
     */
    private function createOrder($type)
    {

        $user = Auth::user();

        $pricePerDka = 0;
        if (isset(Config::get('globals.PRICES_PER_DKA')[$type])) {
            $pricePerDka = Config::get('globals.PRICES_PER_DKA')[$type];
        }

        $plotsArea = Plot::whereIn('gid', Request::get('fields'))
            ->get(['gid', 'uuid', DB::raw('round((ST_Area(geom)/1000)::numeric, 3) as area')]);

        $dates = Request::get('dates');

        DB::beginTransaction();

        try {
            foreach($dates as $date){
                $order = new Order();
                $order->organization()->associate($user->lastChosenOrganization);
                $order->farm = Request::get('farm');
                $order->type = $type;
                $order->year = (int)$date["year"];
                $order->from_date = $date["from"];
                $order->to_date = $date["to"];
                $order->note = Request::get('note');
                $order->date = DB::raw('now()');
                $order->company_name = Request::get('company_name');
                $order->company_mol = Request::get('company_mol');
                $order->company_eik = (int)Request::get('company_eik');
                $order->company_address = Request::get('company_address');
                $order->status = 'new';
                $order->color = Helper::getRandomHexColor();
                $order->createdBy()->associate($user);
                $order->uuid = Uuid::generate(4)->string;

                $plots = [];
                $wholeArea = 0;
                $wholePrice = 0;
                for ($i = 0; $i < count($plotsArea); $i++) {
                    $area = $plotsArea[$i]->area;
                    $plotId = $plotsArea[$i]->gid;
                    $price = round($area * $pricePerDka, 2);

                    $plots[$plotId] = [
                        'price' => $price,
                        'uuid' => $plotsArea[$i]->uuid
                    ];

                    $wholePrice += $price;
                    $wholeArea += $area;
                }

                //add whole price and area for a order
                $order->price = $wholePrice;
                $order->area = $wholeArea;

                $availableDemoArea = max(Config::get('globals.ALLOWED_DEMO_AREA') - (float)$user->ordered_area, 0);
                if ($user->is_trial && $wholeArea <= $availableDemoArea && $type === 'index') {
                    $order->status = 'paid';
                    $order->end_price = 0;

                    $user->ordered_area += $wholeArea;
                    $user->save();

                    $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
                    $availableDemoArea = max(Config::get('globals.ALLOWED_DEMO_AREA') - (float)$user->ordered_area,
                            0) * $areaCoef;
                }

                $order->save();

                $pivotRows = [];
                foreach ($plots as $plotId => $plotData) {
                    $pivotRows[] = [
                        'order_id' => $order->id,
                        'order_uuid' => $order->uuid,
                        'plot_id' => $plotId,
                        'plot_uuid' => $plotData['uuid'],
                        'price' => $plotData['price']
                    ];
                }

                OrderPlotRel::insert($pivotRows);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }

        return $availableDemoArea;
    }

    /**
     * @OA\Get(
     *     path="/apigs/orders/{order}/index-adjustment",
     *     summary="Get orders",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getIndexAdjustment()
    {
        $validator = Validator::make(Request::all(), [
            'orderId' => 'required|integer',
            'date' => 'required|date',
            'satelliteType' => 'required|string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $orderId = Request::get('orderId');
        $date = Request::get('date');
        $satelliteType = Request::get('satelliteType');

        $order = Order::findOrFail($orderId);

        $orderTiffFile = $order->files->where('type', 'TIFF')->where('date', $date)->where('layer_type', 'index')->first();

        if(empty($orderTiffFile->path) || !file_exists($orderTiffFile->path)){
            throw new Exception('No order tiff file.');
        }

        $publicPath = $satelliteType . DIRECTORY_SEPARATOR . $date . '_' . $orderId;
        $publicFile = $publicPath . '_remap.tif';
        $imgPath = Config::get('globals.PROCESSED_ORDERS_PATH') . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR;
        $geoTiffPath = $imgPath . $publicPath;

        //Get remapedTiffPath if exists
        $remapedTiffPath = $imgPath . $publicFile;
        if (file_exists($remapedTiffPath)) {
            return ['path' => $publicFile];
        }

        //Generate remaped Tiff
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');

        $command = "{$gdalBinPath}gdal_calc.py -A {$orderTiffFile->path} --outfile={$geoTiffPath}_remap.tif --calc=\"(((10-0)/(0.1+1))*(A+1)+0)*((A>-1)*(A<=0.1))+(((20-10)/(0.2-0.1))*(A-0.1)+10)*((A>0.1)*(A<=0.2))+(((30-20)/(0.3-0.2))*(A-0.2)+20)*((A>0.2)*(A<=0.3))+(((40-30)/(0.38-0.3))*(A-0.3)+30)*((A>0.3)*(A<=0.38))+(((50-40)/(0.44-0.38))*(A-0.38)+40)*((A>0.38)*(A<=0.44))+(((60-50)/(0.52-0.44))*(A-0.44)+50)*((A>0.44)*(A<=0.52))+(((70-60)/(0.60-0.52))*(A-0.52)+60)*((A>0.52)*(A<=0.60))+(((80-70)/(0.68-0.6))*(A-0.6)+70)*((A>0.6)*(A<=0.68))+(((90-80)/(0.77-0.68))*(A-0.68)+80)*((A>0.68)*(A<=0.77))+(((100-90)/(0.85-0.77))*(A-0.77)+90)*((A>0.77)*(A<=0.85))+(((110-100)/(1-0.85))*(A-0.85)+100)*((A>0.85)*(A<=1))\" --NoDataValue=-127 --type=Byte";

        $output = array();
        exec($command . " 2>&1", $output);

        if (!file_exists($geoTiffPath."_remap.tif")) {
            throw new Exception('Error remaped Tiff.');
        }

        return ['path' => $publicFile];
    }    
}
