<?php

namespace App\Http\Controllers\APIGS;

use App\Http\Controllers\BaseController;
use App\Models\OrderSoilVra;
use Auth;
use OpenApi\Annotations as OA;
use Request;
use Response;
use Plupload;
use App\Models\Order;
use App\Models\OrderPlot;
use App\Models\OrderVra;
use Config;
use Validator;
use App\Exceptions\ValidationException;
use App\Exceptions\ForbiddenException;
use DB;
use Event;
use App\Classes\TFConnect;
use App\Services\Order\OrderVraService;

class OrderSoilVraController extends BaseController
{

    private $tfc;
    private $orderVraService;

    public function __construct(TFConnect $tfc, OrderVraService $orderVraService)
    {
        $this->tfc = $tfc;
        $this->orderVraService = $orderVraService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/order-soil-vra/orders",
     *     summary="Get soil VRA orders",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getOrders()
    {
        $validator = Validator::make(Request::all(), [
            'plotId' => 'required|integer',
            'farmYear' => 'integer|required'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $model = new OrderSoilVra();
        $plotId = Request::get('plotId');
        $farmYear = Request::get('farmYear');

        try {
            $order = $model->getByPlot((int)$plotId, (int)$farmYear);
        } catch (\Exception $e) {
            throw $e;
        }

        return isset($order[0]) ? $order : [];
    }

    /**
     * @OA\Post(
     *     path="/apigs/order-soil-vra/create-order",
     *     summary="Create soil VRA order",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return mixed
     * @throws ForbiddenException
     * @throws ValidationException
     */
    public function postCreateOrder()
    {
        $validator = Validator::make(Request::all(), [
            'plot_id' => 'integer|required',
            'order' => 'array|required',
            'class_number' => 'integer|required',
            'layer_id' => 'integer|required',
            'flat_rate' => 'numeric|required',
            'flat_rate_total' => 'numeric|required',
            'variable_rate_total' => 'numeric|required',
            'difference' => 'numeric|required',
            'difference_percent' => 'numeric|required',
            'data' => 'array|required',
            'vector_data' => 'array|required',
            'product_percent' => 'numeric|required'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        if (!Auth::user()->globalUser()->can('create_vra_soil_maps')) {
            throw new ForbiddenException();
        }

        $model = new Order();

        try {
            $id = $model->createVraOrder(Request::all());
        } catch (\Exception $e) {
            throw $e;
        }

        return \Response::json(['result' => 'order created', 'id' => $id], 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/order-soil-vra/export",
     *     summary="Export soil VRA order",
     *     @OA\Response(
     *     response="200",
     *     description="File"
     * )
     * )
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     * @throws ValidationException
     */
    public function getExport()
    {
        $validator = Validator::make(Request::all(), [
            'orderId' => 'required|integer',
            'format' => ['required', 'max:255', "in:john_deere,shp,trimble,isoxml"],
            'forTfc' => 'boolean',
            'deviceSerial' => 'alpha_num|min:16|max:16'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $forTfc = Request::get('forTfc');
        $deviceSerial = Request::get('deviceSerial');

        try {
            $shape = $this->orderVraService->exportVraMap(
                Request::get('orderId'),
                OrderVraService::VRA_SOIL_ORDER_TYPE,
                Request::get('format'),
                Request::get('soil_element')
            );
        } catch (\Exception $e) {
            throw $e;
        }

        if ($forTfc) {
            $info = $this->tfc->uploadFile($deviceSerial, $shape);
            $this->tfc->setStatus($deviceSerial, $info['file_id'], 'sending');
            return \Response::json(['result' => 'exported_tfc', 'info' => $info], 200);
        }

        return response()->download($shape)->deleteFileAfterSend(false);
    }
}
