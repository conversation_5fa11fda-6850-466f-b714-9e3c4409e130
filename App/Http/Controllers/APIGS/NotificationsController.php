<?php

namespace App\Http\Controllers\APIGS;

use App\Http\Controllers\BaseController;
use OpenApi\Annotations as OA;
use Request;
use App\Models\PushNotification;
use DB;
use Auth;
use App\Helpers\Helper;
use Validator;
use App\Exceptions\ValidationException;
use Response;
use Plupload;
use Image;

class NotificationsController extends BaseController
{

    /**
     * @OA\Get(
     *     path="/apigs/notifications",
     *     summary="Get notifications",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param null $id
     * @return array
     * @throws ValidationException
     */
    public function getIndex($id = null)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'id' => $id
        ]), [
            'id' => 'nullable|integer',
            'plot_id' => 'nullable|integer'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $nQuery = PushNotification::where('group_id', Auth::user()->group_id)
//                ->where('isDeleted', false)
            ->restOrderBy(Request::get('sort'));

        //Filter
        if ($id) {
            $nQuery->where('id', trim($id));
        }
        if (Request::get('plot_id')) {
            $nQuery->where(DB::raw("(data::json->'plots')::text"), 'ILIKE',
                trim('%"' . Request::get('plot_id') . '"%'));
        }

        $notifications = $nQuery->paginate(Request::get('limit'), [
            'date',
            'message'
        ]);

        return [
            'total' => $notifications->total(),
            'rows' => $notifications->items()
        ];
    }

}
