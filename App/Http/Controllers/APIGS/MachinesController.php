<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\CurrentMachineData;
use App\Models\MachineEvent;
use App\Services\Common\MailService;
use App\Services\Implement\MachineImplementService;
use App\Services\Machine\MachineEventService;
use App\Services\Machine\MachineProductService;
use App\Services\Machine\MachineProductsReportService;
use App\Services\Machine\MachineReportService;
use Auth;
use Config;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class MachinesController extends BaseController
{

    public function __construct(
        MachineReportService $machineReportService, 
        MailService $mailService, 
        MachineImplementService $machineImplementService,
        MachineProductService $machineProductService,
        MachineEventService $machineEventService,
        MachineProductsReportService $machineProductsReportService
    ) {
        $this->machineReportService = $machineReportService;
        $this->mailService = $mailService;
        $this->machineImplementService = $machineImplementService;
        $this->machineProductService = $machineProductService;
        $this->machineEventService = $machineEventService;
        $this->machineProductsReportService = $machineProductsReportService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/state/echart
     *     summary="Returns machine state by organization for echart",
     *     @OA\Response(
     *          response="200",
     *          description="Array"
     *      )
     * )
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getStateForEchart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer',
            'plot_ids' => 'sometimes|required|json',
            'machine_ids' => 'sometimes|required|json',
            'farm_ids' => 'sometimes|required|json',
            'crop_ids' => 'sometimes|required|json',

        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id');
        if (!$organizationId) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $filters = [
            'plot_ids' => json_decode($request->get('plot_ids', '[]')),
            'machine_ids' => json_decode($request->get('machine_ids', '[]')),
            'farm_ids' => json_decode($request->get('farm_ids', '[]')),
            'crop_ids' => json_decode($request->get('crop_ids', '[]'))
        ];

        $echartData = CurrentMachineData::getStateEchart($organizationId, $filters);

        return new JsonResponse($echartData, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/trailer/echart
     *     summary="Returns machines' trailer by organization for echart",
     *     @OA\Response(
     *          response="200",
     *          description="Array"
     *      )
     * )
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getTrailerForEchart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer',
            'names' => 'sometimes|required|json',
            'plot_ids' => 'sometimes|required|json',
            'machine_ids' => 'sometimes|required|json',
            'farm_ids' => 'sometimes|required|json',
            'crop_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id');
        if (!$organizationId) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $filters = [
            'names' => json_decode($request->get('names', '[]')),
            'plot_ids' => json_decode($request->get('plot_ids', '[]')),
            'machine_ids' => json_decode($request->get('machine_ids', '[]')),
            'farm_ids' => json_decode($request->get('farm_ids', '[]')),
            'crop_ids' => json_decode($request->get('crop_ids', '[]'))
        ];

        $trailerEchartData = CurrentMachineData::getEchartTrailers($organizationId, $filters);

        return new JsonResponse($trailerEchartData, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/map
     *     summary="Returns machine data for map by organization",
     *     @OA\Response(
     *          response="200",
     *          description="Json"
     *      )
     * )
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getForMap(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer',
            'plot_ids' => 'sometimes|required|json',
            'machine_ids' => 'sometimes|required|json',
            'farm_ids' => 'sometimes|required|json',
            'trailers' => 'sometimes|required|json',
            'statuses' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $filters = [
            'plot_ids' => json_decode($request->get('plot_ids', '[]')),
            'machine_ids' => json_decode($request->get('machine_ids', '[]')),
            'farm_ids' => json_decode($request->get('farm_ids', '[]')),
            'trailers' => json_decode($request->get('trailers', '[]')),
            'statuses' => json_decode($request->get('statuses', '[]')),
        ];

        $organizationId = $request->get('organization_id');

        if (!$organizationId) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $data = CurrentMachineData::getFilteredGeoJSON($organizationId, $filters);

        if (!$data) {
            return new JsonResponse([], 200);
        }

        $dataForMap = $data->geojson;
        return new JsonResponse($dataForMap, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/events
     *     summary="Get Machine Events",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getEvents(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'required_without:event_ids|integer',
            'limit' => 'required_without:event_ids|integer',
            'from' => 'required_without:event_ids|required_with:to|integer',
            'to' => 'required_without:event_ids|required_with:from|integer',
            'lang' => 'string|min:2|max:2',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'machine_ids' => 'json',
            'event_ids' => 'json',
            'work_operations' => 'json',
            'types' => 'json',
            'stages' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $filter = [
            'from' => $request->get('from'),
            'to' => $request->get('to'),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'machineIds' => json_decode($request->get('machine_ids', '[]')),
            'eventIds' => json_decode($request->get('event_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]')),
            'types' => json_decode($request->get('types', '[]')),
            'stages' => json_decode($request->get('stages', '[]')),
            'page' => $request->get('page'),
            'limit' => $request->get('limit'),
        ];

        $lang = $request->get('lang', Config::get('app.locale'));

        $result = MachineEvent::getList($organizationId, $filter, $lang);

        $response = [
            'total' => $result->total(),
            'rows' => $result->items()
        ];

        return new JsonResponse($response, 200);
    }

    /**
     * @OA\POST(
     *     path="/apigs/machine/events/{id}/approve"
     *     summary="Approve machine event",
     *     @OA\Response(
     *          response="200",
     *          description="Array"
     *     )
     * )
     * 
     * @param Request $request
     * @param int $id
     * 
     * @return JsonResponse
     * @throws ValidationException
     */
    public function approveEvent(Request $request, int $id)
    {
        $validator = Validator::make($request->all(), [
            'event_type' => 'required|string',
            'driver' => 'required|string',
            'implement_id' => 'sometimes|required|integer',
            'implement_width' => 'sometimes|required|integer',
            'work_operation_id' => 'sometimes|required|integer',
            'geom_cultivated' => 'sometimes|required|json',
            'products' => 'sometimes|required|array',
            'products.*.id' => 'required|integer',
            'products.*.rate' => 'required|numeric',
            'products.*.value' => 'required|numeric',
            'products.*.pest_name' => 'sometimes|required|string',
            'products.*.pest_application' => 'sometimes|required|string',
            'products.*.pest_quarantine' => 'sometimes|required|int',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $eventType = $request->get('event_type');
        $driver = $request->get('driver');
        $implementId = $request->get('implement_id');
        $implementWidth = $request->get('implement_width');
        $workOperationId = $request->get('work_operation_id');
        $geomCultivated = $request->get('geom_cultivated');
        $products = $request->get('products', []);

        $this->machineEventService->approveEvent($id, $eventType, $implementId, $implementWidth, $workOperationId, $driver, $geomCultivated, $products);
        return new JsonResponse('Success', 200);
    }

    /**
     * @OA\Get(
     *     path="/machine/events/{id}/calculate-cultivated-geom"
     *     summary="Calculate the event's cultivated geom by specified implement size",
     *     @OA\Response(
     *          response="200",
     *          description="Array"
     *     )
     * )
     * 
     * @param Request $request
     * @param int $id The event's id
     * 
     * @return JsonResponse
     * @throws ValidationException
     */
    public function calculateCultivatedGeom(Request $request, int $id)
    {
        $validator = Validator::make($request->all(), [
            'implement_width' => 'required|numeric|min:1'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId =  Auth::user()->lastChosenOrganization->id;
        $implementWidth = $request->get('implement_width');

        $response = MachineEvent::getCultivatedGeomCustom($organizationId, $id, $implementWidth);

        return new JsonResponse($response, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/events/type/echart
     *     summary="Get Machine Events by type for echart pie chart",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getEventsTypeEchart(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'machine_ids' => 'json',
            'work_operations' => 'json',
            'types' => 'json',
            'stages' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'machineIds' => json_decode($request->get('machine_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]')),
            'types' => json_decode($request->get('types', '[]')),
            'stages' => json_decode($request->get('stages', '[]'))
        ];

        $result = MachineEvent::getDataByTypeQuery($organizationId, $filter)->get()->toArray();
        return new JsonResponse($result, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/events/work-operations/echart
     *     summary="Get Machine Events by implements for echart pie chart",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getEventsWorkOperationsEchart(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'machine_ids' => 'json',
            'types' => 'json',
            'stages' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'machineIds' => json_decode($request->get('machine_ids', '[]')),
            'types' => json_decode($request->get('types', '[]')),
            'stages' => json_decode($request->get('stages', '[]'))
        ];

        $result = $this->machineImplementService->getWorkOperationsWithImplementsCnt($filter);

        return new JsonResponse($result, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/events/tasks/echart
     *     summary="Get Machine Events data for Tasks BarChart",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getEventsTasksEchart(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'machine_ids' => 'json',
            'work_operations' => 'json',
            'types' => 'json',
            'stages' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'machineIds' => json_decode($request->get('machine_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]')),
            'types' => json_decode($request->get('types', '[]')),
            'stages' => json_decode($request->get('stages', '[]'))
        ];

        $result = MachineEvent::getDataTasks($organizationId, $filter);
        return new JsonResponse($result, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/events/report
     *     summary="Get Machine Events Report",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getEventsReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'group_by' => 'required|string',
            'order_by' => 'json',
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'driver' => 'string',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'machine_ids' => 'json',
            'implements' => 'json',
            'types' => 'json',
            'stages' => 'json',
            'crop_ids' => 'json',
            'work_operations' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'machineIds' => json_decode($request->get('machine_ids', '[]')),
            'implements' => json_decode($request->get('implements', '[]')),
            'types' => json_decode($request->get('types', '[]')),
            'stages' => json_decode($request->get('stages', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]')),
            'page' => $request->get('page'),
            'limit' => $request->get('limit'),
            'driver' => $request->get('driver'),
        ];

        $groupBy = $request->get('group_by');
        $orderBy = json_decode($request->get('order_by', '[]'), true);
        $organizationId = Auth::user()->lastChosenOrganization->id;

        $result = $this->machineReportService->getReport($organizationId, $filter, $groupBy, $orderBy);
        return new JsonResponse($result, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/events/report/{type}
     *     summary="Export Machine Events Report in .pdf or .xls format",
     *     @OA\Response(
     *          response="200",
     *          description="A PDF or XLS file"
     *     )
     * )
     *
     * @param Request $request
     * @param string $type
     *
     * @throws ValidationException
     * @return File
     */
    public function exportEventsReport(Request $request, string $type)
    {
        $requestData = $request->all();
        $requestData["type"] = $type;

        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'type' => 'required|string|in:pdf,xls',
            'group_by' => 'required|string',
            'order_by' => 'json',
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'machine_ids' => 'json',
            'implements' => 'json',
            'types' => 'json',
            'stages' => 'json',
            'crop_ids' => 'json',
            'work_operations' => 'json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'machineIds' => json_decode($request->get('machine_ids', '[]')),
            'implements' => json_decode($request->get('implements', '[]')),
            'types' => json_decode($request->get('types', '[]')),
            'stages' => json_decode($request->get('stages', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]'))

        ];

        $groupBy = $request->get('group_by');
        $orderBy = json_decode($request->get('order_by', '[]'), true);

        $reportFilePath = $this->machineReportService->generateReport($type, $organizationId, $filter, $groupBy, $orderBy);
        $customFileName = 'machines_report_' . date('d_m_Y', $filter['from']) . '-' . date('d_m_Y', $filter['to']) . '.' . $type;

        return response()->download($reportFilePath, $customFileName)->deleteFileAfterSend(true);
    }


    /**
     * @OA\Get(
     *     path="/apigs/machine/events/report/{type}/send
     *     summary="Send Machine Events Report in .pdf or .xls format to specific email",
     *     @OA\Response(
     *          response="200",
     *          description="Success"
     *     )
     * )
     *
     * @param Request $request
     * @param string $type
     *
     * @throws ValidationException
     * @return JsonResponse
     */
    public function sendEventsReport(Request $request, string $type)
    {
        $requestData = $request->all();
        $requestData["type"] = $type;

        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'type' => 'required|string|in:pdf,xls',
            'email' => 'required|email',
            'subject' => 'string',
            'message' => 'string',
            'group_by' => 'required|string',
            'order_by' => 'json',
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'machine_ids' => 'json',
            'implements' => 'json',
            'types' => 'json',
            'stages' => 'json',
            'crop_ids' => 'json',
            'work_operations' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'machineIds' => json_decode($request->get('machine_ids', '[]')),
            'implements' => json_decode($request->get('implements', '[]')),
            'types' => json_decode($request->get('types', '[]')),
            'stages' => json_decode($request->get('stages', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]'))
        ];

        $groupBy = $request->get('group_by');
        $orderBy = json_decode($request->get('order_by', '[]'), true);
        $email = $request->get('email');
        $subject = $request->get('subject', 'GeoSCAN Machines report');
        $message = $request->get('message', '');
        $title = trans('emailReport.machinesReport');

        $reportFilePath = $this->machineReportService->generateReport($type, $organizationId, $filter, $groupBy, $orderBy);
        $customFileName = 'machines_report_' . date('d_m_Y', $filter['from']) . '-' . date('d_m_Y', $filter['to']) . '.' . $type;

        $this->mailService->sendFileToEmail($email, $title, $subject, $message, $reportFilePath, $customFileName);
        unlink($reportFilePath);

        return new JsonResponse('Success', 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/machine/events/report/schedule/machine-events
     *     summary="Run machine events reprot by organization, concret date and unit or all units",
     *     @OA\Response(
     *          response="200",
     *          description="Success"
     *     )
     * )
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function scheduleMachineEventsReport(Request $request)
    {
        $requestData = $request->all();

        $validator = Validator::make($requestData, [
            'organization_id' => 'required|integer',
            'wialon_unit_id' => 'sometimes|required|integer',
            'to_date' => 'required|integer',
            'from_date' => 'required|integer',
        ]);

        $organizationId = $request->get('organization_id');
        $wialonUnitId = $request->get('wialon_unit_id', null);
        $toDate = $request->get('to_date');
        $fromDate = $request->get('from_date');

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $this->machineReportService->scheduleMachineEventsReport($organizationId, $wialonUnitId, $toDate, $fromDate);

        return new JsonResponse('Success', 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/events/products
     *     summary="Get Machine Events products",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getAllProducts(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), ['organization_id' => 'integer',]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id');
        if (!$organizationId) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $results = $this->machineProductService->getAllProductsByOrganization($organizationId);

        return new JsonResponse($results, 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/machine/events/products/add
     *     summary="Add new products",
     *     @OA\Response(
     *          response="200",
     *          description="Success"
     *     )
     * )
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function addProduct(Request $request)
    {
        $requestData = $request->all();

        $validator = Validator::make($requestData, [
            'name' => 'required|string',
            'type' => 'required|string|in:fertiliser,fuel,ppp,seeds,others',
            'rate' => 'required|numeric',
            'state' => 'required|string|in:Active,Inactive',
            'unit' => 'required|string|in:kg,ton,pc.,litre,millilitre,milligram,gram,gallon,metre,centimetre',
            'unit_per_area' => 'required|boolean',
            'organization_id' => 'integer',
        ]);


        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id');
        if (!$organizationId) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $this->machineProductService->add(
            $request->get('name'),
            $request->get('type'),
            (float)$request->get('rate'),
            $request->get('state'),
            $request->get('unit'),
            $request->get('unit_per_area'),
            $organizationId
        );

        return new JsonResponse('Success', 200);
    }

    /**
     * @OA\Put(
     *     path="/apigs/machine/events/products/{productId}/update",
     *     summary="Update product",
     *          @OA\Response(
     *          response="200"
     *      )
     * )
     *
     * @param int $productId
     * @param Request $request
     * 
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateProduct(int $productId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'type' => 'required|string|in:fertiliser,fuel,ppp,seeds,others',
            'rate' => 'required|numeric',
            'state' => 'required|string|in:Active,Inactive',
            'unit' => 'required|string|in:kg,ton,pc.,litre,millilitre,milligram,gram,gallon,metre,centimetre',
            'unit_per_area' => 'required|boolean',
            'organization_id' => 'integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id');
        if (!$organizationId) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $product = $this->machineProductService->update(
            $productId,
            $request->get('name'),
            $request->get('type'),
            (float)$request->get('rate'),
            $request->get('state'),
            $request->get('unit'),
            $request->get('unit_per_area'),
            $organizationId
        );

        return new JsonResponse($product, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machines/events/drivers",
     *     summary="Get the machine driver from all reports by organization",
     *     @OA\Response(
     *          response="200"
     *          description="array"
     *     )
     * )
     *
     * @param Request $request
     * 
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getDrivers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string',
            'organization_id' => 'sometimes|required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id', Auth::user()->lastChosenOrganization->id);
        $driverName = $request->get('name');

        $drivers = MachineEvent::getDrivers($organizationId, $driverName);

        return new JsonResponse($drivers, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/events/types",
     *     summary="Get machine event types",
     *     @OA\Response(
     *          response="200"
     *          description="array"
     *     )
     * )
     *
     * @param Request $request
     * 
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getEventTypes(Request $request)
    {
        $eventTypes = MachineEvent::getTypes();

        return new JsonResponse($eventTypes, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/events/products/report
     *     summary="Get Machine Events Products Report",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getEventsProductsReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page'            => 'required|integer',
            'limit'           => 'required|integer',
            'group_by'        => 'required|string',
            'order_by'        => 'json',
            'from'            => 'required_with:to|integer',
            'to'              => 'required_with:from|integer',
            'driver'          => 'string',
            'farm_ids'        => 'json',
            'plot_ids'        => 'json',
            'machine_ids'     => 'json',
            'implements'      => 'json',
            'types'           => 'json',
            'stages'          => 'json',
            'crop_ids'        => 'json',
            'work_operations' => 'json',
            'product_ids'    => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $filter = [
            'from'            => $request->get('from', strtotime('- 20 days')),
            'to'              => $request->get('to', time()),
            'farmIds'         => json_decode($request->get('farm_ids', '[]')),
            'plotIds'         => json_decode($request->get('plot_ids', '[]')),
            'machineIds'      => json_decode($request->get('machine_ids', '[]')),
            'implements'      => json_decode($request->get('implements', '[]')),
            'types'           => json_decode($request->get('types', '[]')),
            'stages'          => json_decode($request->get('stages', '[]')),
            'cropIds'         => json_decode($request->get('crop_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]')),
            'page'            => $request->get('page'),
            'limit'           => $request->get('limit'),
            'driver'          => $request->get('driver'),
            'productIds'      => json_decode($request->get('product_ids', '[]')),
        ];

        $groupBy = $request->get('group_by');
        $orderBy = json_decode($request->get('order_by', '[]'), true);

        $result = $this->machineProductsReportService->getReport($organizationId, $filter, $groupBy, $orderBy);

        return new JsonResponse($result, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine/events/products/report/{type}
     *     summary="Export Machine Events Products Report in .pdf or .xls format",
     *     @OA\Response(
     *          response="200",
     *          description="A PDF or XLS file"
     *     )
     * )
     *
     * @param Request $request
     * @param string $type
     *
     * @throws ValidationException
     * @return File
     */
    public function exportEventsProductsReport(Request $request, string $type)
    {
        $requestData = $request->all();
        $requestData["type"] = $type;

        $validator = Validator::make($requestData, [
            'lang'            => 'required|string|min:2|max:2',
            'type'            => 'required|string|in:pdf,xls',
            'group_by'        => 'required|string',
            'order_by'        => 'json',
            'from'            => 'required_with:to|integer',
            'to'              => 'required_with:from|integer',
            'farm_ids'        => 'json',
            'plot_ids'        => 'json',
            'machine_ids'     => 'json',
            'implements'      => 'json',
            'types'           => 'json',
            'stages'          => 'json',
            'crop_ids'        => 'json',
            'work_operations' => 'json',
            'products_ids'    => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $filter = [
            'from'            => $request->get('from', strtotime('- 20 days')),
            'to'              => $request->get('to', time()),
            'farmIds'         => json_decode($request->get('farm_ids', '[]')),
            'plotIds'         => json_decode($request->get('plot_ids', '[]')),
            'machineIds'      => json_decode($request->get('machine_ids', '[]')),
            'implements'      => json_decode($request->get('implements', '[]')),
            'types'           => json_decode($request->get('types', '[]')),
            'stages'          => json_decode($request->get('stages', '[]')),
            'cropIds'         => json_decode($request->get('crop_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]')),
            'productIds'      => json_decode($request->get('products_ids', '[]')),
        ];

        $groupBy = $request->get('group_by');
        $orderBy = json_decode($request->get('order_by', '[]'), true);

        $reportFilePath = $this->machineProductsReportService->generateReport($type, $organizationId, $filter, $groupBy, $orderBy);
        $customFileName = 'machines_products_report_' . date('d_m_Y', $filter['from']) . '-' . date('d_m_Y', $filter['to']) . '.' . $type;

        return response()->download($reportFilePath, $customFileName)->deleteFileAfterSend(true);
    }


    /**
     * @OA\Get(
     *     path="/apigs/machine/events/products/report/{type}/send
     *     summary="Send Machine Events Products Report in .pdf or .xls format to specific email",
     *     @OA\Response(
     *          response="200",
     *          description="Success"
     *     )
     * )
     *
     * @param Request $request
     * @param string $type
     *
     * @throws ValidationException
     * @return JsonResponse
     */
    public function sendEventsProductsReport(Request $request, string $type)
    {
        $requestData = $request->all();
        $requestData["type"] = $type;

        $validator = Validator::make($requestData, [
            'lang'            => 'required|string|min:2|max:2',
            'type'            => 'required|string|in:pdf,xls',
            'email'           => 'required|email',
            'subject'         => 'string',
            'message'         => 'string',
            'group_by'        => 'required|string',
            'order_by'        => 'json',
            'from'            => 'required_with:to|integer',
            'to'              => 'required_with:from|integer',
            'farm_ids'        => 'json',
            'plot_ids'        => 'json',
            'machine_ids'     => 'json',
            'implements'      => 'json',
            'types'           => 'json',
            'stages'          => 'json',
            'crop_ids'        => 'json',
            'work_operations' => 'json',
            'product_ids'     => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $filter = [
            'from'            => $request->get('from', strtotime('- 20 days')),
            'to'              => $request->get('to', time()),
            'farmIds'         => json_decode($request->get('farm_ids', '[]')),
            'plotIds'         => json_decode($request->get('plot_ids', '[]')),
            'machineIds'      => json_decode($request->get('machine_ids', '[]')),
            'implements'      => json_decode($request->get('implements', '[]')),
            'types'           => json_decode($request->get('types', '[]')),
            'stages'          => json_decode($request->get('stages', '[]')),
            'cropIds'         => json_decode($request->get('crop_ids', '[]')),
            'work_operations' => json_decode($request->get('work_operations', '[]')),
            'productIds'      => json_decode($request->get('product_ids', '[]')),
        ];

        $groupBy = $request->get('group_by');
        $orderBy = json_decode($request->get('order_by', '[]'), true);
        $email = $request->get('email');
        $subject = $request->get('subject', 'GeoSCAN Products report');
        $message = $request->get('message', '');
        $title = trans('emailReport.machinesProductsReport');

        $reportFilePath = $this->machineProductsReportService->generateReport($type, $organizationId, $filter, $groupBy, $orderBy);
        $customFileName = 'machines_products_report_' . date('d_m_Y', $filter['from']) . '-' . date('d_m_Y', $filter['to']) . '.' . $type;

        $this->mailService->sendFileToEmail($email, $title, $subject, $message, $reportFilePath, $customFileName);
        unlink($reportFilePath);

        return new JsonResponse('Success', 200);
    }
}
