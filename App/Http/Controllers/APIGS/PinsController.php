<?php

namespace App\Http\Controllers\APIGS;

use App\Http\Controllers\BaseController;
use App\Services\Pin\PinService;
use OpenApi\Annotations as OA;
use Request;
use Validator;
use App\Exceptions\ValidationException;
use hanneskod\classtools\Exception\LogicException;
use Response;

class PinsController extends BaseController
{
    private $pinService;

    public function __construct(PinService $pinService)
    {
        $this->middleware('verify-write-rights',
            ['only' => ['deleteSelectedPin', 'postEditPin', 'postCreatePin', 'postUploadImages']]);

        $this->pinService = $pinService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/pins/index/:id?",
     *     summary="Get pins",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param null $pinId
     * @return array
     * @throws ValidationException
     */
    public function getIndex($pinId = null)
    {
        $validator = Validator::make(Request::all(),
            [
                'from_date' => 'sometimes|required|date',
                'to_date' => 'sometimes|required|date',
                'name' => 'sometimes|required|string',
                'limit' => 'required|string',
                'sort' => 'required|string',
            ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $fromDate = Request::get('from_date', null);
        $toDate = Request::get('to_date', null);
        $limit = Request::get('limit');
        $name = Request::get('name', null);
        $sort = Request::get('sort');
        $pins = $this->pinService->getIndex($pinId, $fromDate, $toDate, $name, $limit, $sort);

        return [
            'total' => $pins->total(),
            'rows' => $pins->items()
        ];
    }

    /**
     * @OA\Delete(
     *     path="/apigs/pins/selected-pin",
     *     summary="Delete pin",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @throws ValidationException
     */
    public function deleteSelectedPin()
    {
        $validator = Validator::make(Request::all(), [
            'id' => 'required|string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $pinId = Request::get('id');
        $this->pinService->deleteSelectedPin($pinId);

        return Response::json(array('Response' => 'Success'));
    }

    /**
     * @OA\Delete(
     *     path="/apigs/pins/pin-image",
     *     summary="Delete pin image",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @throws ValidationException
     */
    public function deletePinImage()
    {
        $validator = Validator::make(Request::all(), [
            'pin_id' => 'required|string',
            'file_name' => 'sometimes|required|string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $pinId = Request::get('pin_id');
        $fileName = Request::get('file_name', '');
        $this->pinService->deletePinImage($pinId, $fileName);

        return Response::json(array('Response' => 'Success'));
    }

    /**
     * @OA\Get(
     *     path="/apigs/pins/load-pin",
     *     summary="Load pin",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @throws ValidationException
     */
    public function getLoadPin()
    {
        $validator = Validator::make(Request::all(), [
            'id' => 'string|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $pinId = Request::get('id');
        $pin = $this->pinService->loadPin($pinId);

        return $pin;
    }

    /**
     * @OA\Post(
     *     path="/apigs/pins/edit-pin",
     *     summary="Edit pin",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @throws ValidationException
     */
    public function postEditPin()
    {
        $validator = Validator::make(Request::all(), [
            'id' => 'required|string',
            'title' => 'required|string|max:31',
            'comment' => 'sometimes|nullable|string|max:255',
            'farm_id' => 'sometimes|int'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $id = Request::get('id');
        $title = Request::get('title');
        $comment = Request::get('comment', null);
        $farmId = Request::get('farm_id', null);
        $updatedPin = $this->pinService->editPin($id, $title, $comment, $farmId);

        return Response::json(array('Response' => 'Success', 'pinId' => $updatedPin->id));
    }

    /**
     * @OA\Post(
     *     path="/apigs/pins/create-pin",
     *     summary="Create pin",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @return mixed
     * @throws ValidationException
     */
    public function postCreatePin()
    {
        $validator = Validator::make(Request::all(), [
            'title' => 'required|string|max:31',
            'comment' => 'sometimes|nullable|string|max:255',
            'lon' => 'required|numeric',
            'lat' => 'required|numeric',
            'type' => 'nullable|string',
            'sopr_id' => 'sometimes|required|int',
            'farm_id' => 'sometimes|required|int'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $longitude = Request::get('lon');
        $latitude = Request::get('lat');
        $title = Request::get('title');
        $comment = Request::get('comment', null);
        $type = Request::get('type', null);
        $soprId = Request::get('sopr_id', null);
        $farmId = Request::get('farm_id', null);

        try {
            $pinId = $this->pinService->createPin($title, $longitude, $latitude, $comment, $type, $soprId, $farmId);
        } catch (LogicException $e) {
            return Response::json(array('error' => $e->getMessage()), 500);
        }

        return Response::json(array('Response' => 'Success', 'pinId' => $pinId));
    }

    /**
     * @OA\Post(
     *     path="/apigs/pins/upload-images/:pinId",
     *     summary="Upload images for pin",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @param $pinId
     * @return mixed
     * @throws ValidationException
     */
    public function postUploadImages($pinId)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'pinId' => $pinId
        ]), [
            'pinId' => 'string|required',
            'files' => 'sometimes|required|array',
            'files.*' => 'mimes:jpeg,gif,png',
            'file' => 'sometimes|required|mimes:jpeg,gif,png',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $result = [];

        if (Request::hasfile('files')) {
            $result = $this->pinService->uploadImages($pinId);
            return Response::json($result);
        }

        if (Request::hasfile('file')) {
            $result = $this->pinService->uploadImage($pinId);
        }

        return Response::json($result);
    }

    /**
     * @OA\Get(
     *     path="/apigs/pins/for-map",
     *     summary="Get pins for map",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getForMap()
    {
        $validator = Validator::make(Request::all(), [
            'from_date' => 'required|date',
            'to_date' => 'required|date',
            'farm_ids' => 'json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $fromDate = Request::get('from_date');
        $toDate = Request::get('to_date');
        $farmIds = json_decode(Request::get('farm_ids', '[]'));

        $pins = $this->pinService->getForMap($fromDate, $toDate, $farmIds);

        return $pins;
    }

    /**
     * @OA\Get(
     *     path="/apigs/pins/for-soil-samples",
     *     summary="Get pins for soil samples",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getForSoilSamples()
    {
        $validator = Validator::make(Request::all(), [
            'sopr_id' => 'required|int'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $soprId = Request::get('sopr_id');
        $pins = $this->pinService->pinsForSoilSamples($soprId);

        return $pins;
    }
}
