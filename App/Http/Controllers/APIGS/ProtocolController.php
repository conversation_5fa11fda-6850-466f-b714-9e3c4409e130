<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS;


use App;
use App\Classes\CMS\ProtocolService;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Order;
use App\Models\OrderPlotRel;
use App\Models\Organization;
use App\Models\Plot;
use Auth;
use Barryvdh\DomPDF\Facade as PDF;
use Config;
use DateTime;
use Dompdf\Dompdf;
use Illuminate\Http\Request;
use Knp\Snappy\Pdf as SnappyPdf;
use Validator;

class ProtocolController extends BaseController
{

    private $protocolService;

    public function __construct(ProtocolService $protocolService)
    {
        $this->protocolService = $protocolService;
    }

    public function generateProtocolByOrder(Order $order)
    {
        $date = new DateTime();
        $plots = OrderPlotRel::getPlotsForProtocol($order);
        $totalArea = 0;
        foreach ($plots as $each) {
            $totalArea += $each->area;
        }

        /** @var Dompdf $domPdf */
        $domPdf = PDF::getDomPDF();

        $data = [
            'period' => (new DateTime($order->from_date))->format('d-m-Y') . ' - ' . (new DateTime($order->to_date))->format('d-m-Y'),
            'customerName' => $order->company_name,
            'plots' => $plots,
            'totalArea' => $totalArea,
            'date' => $date->format('d-m-Y'),
            'pdf' => $domPdf
        ];

        $pdf = PDF::loadView('templates.contract_protocol_by_order', $data)->setPaper('a4',
            'portrait')->setOptions(['dpi' => 150, "enable_php" => true]);

        return $pdf->download('protocol_' . $date->format('Y-m-d') . '.pdf');
    }

    public function generatePackagesProtocol(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'packages' => 'required|json',
            'plotIds' => 'required|json'
        ]);

        if($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $date = new DateTime();
        $plotIds = json_decode($request->get('plotIds'));
        $packages = json_decode($request->get('packages'));

        $plots = Plot::getForProtocol($plotIds);

        $totalArea = 0;
        foreach ($plots as $each) {
            $totalArea += $each->area;
        }

        /** @var Dompdf $domPdf */
        $domPdf = PDF::getDomPDF();

        $data = [
            'period' => (new DateTime())->format('d-m-Y') . ' - ' . (new DateTime())->format('d-m-Y'),
            'customerName' => 'CHANGE ME',
            'plots' => $plots,
            'totalArea' => $totalArea,
            'date' => $date->format('d-m-Y'),
            'pdf' => $domPdf
        ];


        $pdf = PDF::loadView('templates.packages_protocol', $data)->setPaper('a4',
            'portrait')->setOptions(['dpi' => 150, "enable_php" => true]);

        return $pdf->download('protocol_' . $date->format('Y-m-d') . '.pdf');
    }

    public function generateProtocolById(Request $request, int $protocolId)
    {
        $requestData = $request->all();
        $requestData["protocol_id"] = $protocolId;

        $validator = Validator::make($requestData, [
            'protocol_id' => 'integer|min:1',
            'organization_identity_number' => 'string|required',
            'lang' => 'string|min:2|max:2'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $lang = $request->get('lang', 'en');
        App::setLocale($lang);

        $organization = Organization::where('identity_number', $requestData['organization_identity_number'])->first();
        $serviceProvider = Auth::user()->globalUser()->serviceProvider;

        $protocolData = $this->protocolService->listProtocols($requestData, $protocolId);
        $plots = OrderPlotRel::getPlotsProtocolData($protocolData["orderUuids"], $protocolData["plotUuids"], $organization->identity_number);
        $protocolDate = (new DateTime($protocolData["protocolDate"]))->format('d-m-Y');

        $totalArea = 0;
        $totalGridCells = 0;
        foreach ($plots as $each) {
            $totalArea += $each->area;
            $totalGridCells += $each->grid_cells_num;

        }

        $mainHtmldata = [
            'customerName' => $organization->name,
            'serviceManager' => $protocolData["responsibleUser"],
            'contractId' => $protocolData["contractId"],
            'contractStartDate' => (new DateTime($protocolData["contractStartDate"]))->format('d-m-Y'),
            'contractEndDate' => (new DateTime($protocolData["contractEndDate"]))->format('d-m-Y'),
            'contractArea' => $protocolData["contractArea"],
            'protocolId' => $protocolData["protocolId"],
            'plots' => $plots,
            'totalArea' => $totalArea,
            'totalGridCells' => $totalGridCells,
            'date' => $protocolDate,
            'areaUnit' => trans('general.' . Config::get('globals.AREA_UNIT_LABEL')),
            'serviceProvider' => $serviceProvider
        ];

        $headerHtmlData = [
            'serviceProvider' => $serviceProvider,
        ];

        $mainHtml = view('templates.contract_protocol_by_id', $mainHtmldata)->render();
        $headerHtml = view('templates.helpers.contract_protocol_header', $headerHtmlData)->render();
        $footerHtml = view('templates.helpers.contract_protocol_footer')->render();

        $binary = Config::get('snappy.pdf.binary');
        $options = [
            'dpi' => 150,
            'page-size' => 'a4',
            'orientation' => 'portrait',
            'margin-top' => '2.5cm',
            'margin-bottom' => '2.5cm',
            'margin-right' => '2.5cm',
            'margin-left' => '2.5cm',
            'header-html' => $headerHtml,
            'footer-html' => $footerHtml
        ];

        $pdf = new SnappyPdf($binary, $options);

        $tmpFilePath = sys_get_temp_dir() . '/' . uniqid('contract_protocol_by_id_', true) . '.pdf';
        $pdf->generateFromHtml($mainHtml, $tmpFilePath, ['enable-local-file-access' => true]);

        $customFileName = "protocol_{$protocolDate}.pdf";

        return response()->download($tmpFilePath, $customFileName)->deleteFileAfterSend(true);
    }
}
