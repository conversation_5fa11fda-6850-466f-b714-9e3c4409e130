<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\MachineUnit;
use App\Services\Machine\MachineUnitService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Auth;
use Illuminate\Support\Facades\Validator;

class MachineUnitsController extends BaseController
{
    private $machineUnitService;

    public function __construct(MachineUnitService $machineUnitService)
    {
        $this->machineUnitService = $machineUnitService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine-units/sync
     *     summary="Sync machine units from wialon for all integrations attached to current organization.",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function sync(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors->first());
        }

        $organizationId = $request->get('organization_id');

        $this->machineUnitService->syncUnits($organizationId);
        $machineUnits = MachineUnit::getUnits()->where('organization_id', $organizationId)->orderBy('id', 'desc')->get();

        return new JsonResponse($machineUnits, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine-units
     *     summary="Get units by organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function get(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer|sometimes|required',
            'name' => 'string|sometimes|required',
            'option_all' => 'boolean|sometimes|required',
        ]);


        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id');
        if (!$organizationId) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $unitsQuery = MachineUnit::getUnits()->where('organization_id', $organizationId);

        if ($request->get('name')) {
            $unitsQuery->where("name", 'ILIKE','%' . trim($request->get('name')) . '%');
        }
        $units = $unitsQuery->orderBy('id', 'desc')->get()->toArray();

        $addOptionAll = $request->get('option_all', false);
        if($addOptionAll) {
            array_push($units, [
                "id" => 0,
                "organization_id" => $organizationId,
                "name" => "All",
                "wialon_unit_imei" => 0,
                "type" => "",
                "last_communication" => '',
                "last_position" => '',
                "wialon_unit_id" => null
            ]);
        }

        return new JsonResponse($units, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine-units/types
     *     summary="Get machine unit types (e.g. Tractor, Harvester, Sprayer...)",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getTypes(Request $request)
    {
        $units = MachineUnit::getUnitTypes();
        return new JsonResponse($units, 200);
    }

    /**
     * @OA\Put(
     *     path="/apigs/machine-units/{unitId}
     *     summary="Update unit",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function update($unitId, Request $request)
    {
        $unitTypes = MachineUnit::getUnitTypes();

        $requestData = $request->all();
        $requestData['unit_id'] = $unitId;

        $validator = Validator::make($requestData, [
            'organization_id' => 'required|integer',
            'unit_id' => 'required|integer',
            'name' => 'required|string',
            'type' => 'required|string|in:' . implode(',', $unitTypes)
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $name = $request->get('name');
        $type = $request->get('type');

        $unit = MachineUnit::findOrFail($unitId);
        $unit->update(['name' => $name, 'type' => $type]);

       return new JsonResponse($unit, 200);
    }

    /**
     * @OA\Delete(
     *     path="/apigs/machine-units/{unitId}
     *     summary="Delete unit and all related events",
     *     @OA\Response(
     *          response="200",
     *          description="String"
     *     )
     * )
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function delete($unitId, Request $request)
    {
        $success = $this->machineUnitService->deleteUnitWithEvents($unitId);

        if (!$success) {
            return new JsonResponse('Error deleting unit!', 409);
        }
        
        return new JsonResponse('Success', 200);
    }

     /**
     * @OA\Get(
     *     path="/apigs/machine-units/{unitId}/track
     *     summary="Get unit track geojson for specified time range",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getUnitTrack($unitId, Request $request)
    {    
        $requestData = $request->all();
        $requestData['unit_id'] = $unitId;

        $validator = Validator::make($requestData, [
            'unit_id' => 'required|integer',
            'from' => 'required_with:to|string',
            'to' => 'required_with:from|string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $from = $request->get('from');
        $to = $request->get('to');

        if(!isset($from) && !isset($to)) {
            $from = strtotime('-5 minutes');
            $to = time();
        }

       $machineUnitTrack = $this->machineUnitService->getUnitTrack($unitId, $organizationId, $from, $to);
       $machineUnitTrack = json_decode($machineUnitTrack, true);

       return new JsonResponse($machineUnitTrack, 200);
    }
       
}
