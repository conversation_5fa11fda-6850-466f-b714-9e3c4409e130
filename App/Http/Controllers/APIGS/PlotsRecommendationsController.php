<?php

namespace App\Http\Controllers\APIGS;

use App\Http\Controllers\BaseController;
use OpenApi\Annotations as OA;
use Request;
use App\Models\OrderPlot;
use App\Models\Plot;
use App\Models\PlotRecommendation;
use App\Models\LayerPlot;
use App\Models\Order;
use DB;
use Auth;
use App\Helpers\Helper;
use Validator;
use App\Exceptions\ValidationException;
use Config;

class PlotsRecommendationsController extends BaseController
{

    public function __construct()
    {
        $this->middleware('verify-write-rights', ['except' => ['getIndex']]);
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots/:plotId/recommendations",
     *     summary="List of plot recommendations",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param $plotId
     * @return array
     * @throws ValidationException
     */
    public function getIndex($plotId)
    {
        $validator = Validator::make([
            'plot_id' => $plotId,
            'from_date' => Request::get('from_date'),
            'to_date' => Request::get('to_date')
        ], [
            'plot_id' => 'required|integer',
            'from_date' => 'nullable|date_format:Y-m-d',
            'to_date' => 'nullable|date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $q = PlotRecommendation::where('plot_id', $plotId);

        //Filter
        if (Request::get('from_date')) {
            $q->where(DB::raw('created_at::DATE'), '>=', Request::get('from_date'));
        }
        if (Request::get('to_date')) {
            $q->where(DB::raw('created_at::DATE'), '<=', Request::get('to_date'));
        }

        $plotRecResult = $q->paginate(Request::get('limit'), ['id', 'title', 'description', 'created_at AS date']);

        return [
            'total' => $plotRecResult->total(),
            'rows' => $plotRecResult->items()
        ];
    }
}