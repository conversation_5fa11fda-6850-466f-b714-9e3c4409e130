<?php
/**
 * Created by PhpStorm.
 * User: l.nonchev
 * Date: 2/16/2021
 * Time: 8:41 AM
 */

namespace App\Http\Controllers\APIGS;


use App\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use App\Exceptions\ValidationException;
use App\Services\FarmTrackReportsLog\FarmTrackReportsLogService;

class FarmTrackReportsLogController extends BaseController
{
    private $farmTrackReportLogService;

    public function __construct(FarmTrackReportsLogService $farmTrackReportLogService)
    {
        $this->farmTrackReportLogService = $farmTrackReportLogService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/farm-track-reports-log/organization/:organizationId",
     *     summary="Get farm track reports log by organization",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @param int $organizationId
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getFarmTrackReportsByOrganization(int $organizationId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_slug_short' => 'sometimes|required|string',
            'type' => 'sometimes|required|string',
            'report_names' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $packageSlugShort = $request->get('package_slug_short', null);
        $state = $request->get('state', 'processing');
        $reportNames = $request->get('report_names', ['machine_events', 'irrigation_per_day']);

        $response = $this->farmTrackReportLogService->getFarmTrackReportsByOrganization($organizationId, $packageSlugShort, $state, $reportNames);

        return new JsonResponse($response, 200);
    }

}
