<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 4/3/2021
 * Time: 4:56 PM
 */

namespace App\Http\Controllers\APIGS\v2;


use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Services\Plot\PlotSoilService;
use GuzzleHttp\Exception\GuzzleException;
use Request;
use Validator;

class PlotsSoilController extends BaseController
{
    private $plotSoilService;

    public function __construct(PlotSoilService $plotSoilService)
    {
        $this->plotSoilService = $plotSoilService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/v2/plots/:ploId/soil/samples",
     *     summary="Plots samples",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param int $plotId
     *
     * @return array|mixed
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function getSamples(int $plotId)
    {
        $validator = Validator::make(array_merge(Request::all(), [
            'plotId' => $plotId,
        ]), [
            'page' => 'integer',
            'plotId' => 'required|integer',
            'orderId' => 'required|integer',
            'date' => 'required|string',
            'search_sample' => 'sometimes|required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $orderId = Request::get('orderId');
        $date = Request::get('date');
        $searchSample = Request::get('search_sample', null);

        $arrContent = $this->plotSoilService->samplesContent($plotId, $orderId, $date, $searchSample);


        return $arrContent;
    }

}
