<?php

namespace App\Http\Controllers\APIGS\v2;

use App\Classes\Meteo\MeteoBlue;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Farm;
use App\Models\Plot;
use App\Services\Order\OrderService;
use App\Services\Plot\PlotService;
use Auth;
use Illuminate\Http\JsonResponse;
use Request;
use Validator;

class PlotsController extends BaseController
{
    private $plotService;
    private $orderService;
    private $meteo;

    public function __construct(PlotService $plotService, MeteoBlue $meteo, OrderService $orderService)
    {
        $this->plotService = $plotService;
        $this->orderService = $orderService;
        $this->meteo = $meteo;
    }

    /**
     * @OA\Get(
     *     path="/apigs/v2/plots/search",
     *     summary="Search plots called from a8",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getSearch()
    {
        $validator = Validator::make(Request::all(), [
            'name' => 'string',
            'organization_id' => 'required|integer',
            'contract_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $name = Request::get('name');
        $organizationId = Request::get('organization_id');
        $contractId = Request::get('contract_id');
        $limit = Request::get('limit');

        return $this->plotService->searchDataByOrganizationAndContract($name, $organizationId, $contractId, $limit);
    }

    /**
     * @OA\Get(
     *     path="/apigs/v2/plots",
     *     summary="List of plots",
     *     @OA\Response(
     *          response="200",
     *          description="OK"
     *      )
     * )
     *
     * @throws ValidationException
     * @return JsonResponse
     */
    public function getPlots()
    {
        $validator = Validator::make(Request::all(), [
            'lang' => 'required|string|min:2|max:2',
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'filters' => 'array',
            'filters.plot_ids' => 'json',
            'filters.plot_name' => 'string',
            'filters.farm_ids' => 'json',
            'filters.date' => 'date_format:Y-m-d',
            'filters.farm_year' => 'integer',
            'filters.crop_ids' => 'json',
            'sort' => 'array',
            'sort.*' => 'string|in:asc,desc',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $lang = Request::get('lang');
        $page = Request::get('page');
        $limit = Request::get('limit');
        $filters = Request::get('filters', []);
        $sort = Request::get('sort', []);

        $filters["plot_ids"] = json_decode(Request::input('filters.plot_ids', "[]"));
        $filters["farm_ids"] = json_decode(Request::input('filters.farm_ids', "[]"));
        $filters["crop_ids"] = json_decode(Request::input('filters.crop_ids', "[]"));
        $filters["farm_year"] = json_decode(Request::input('filters.farm_year'));

        $plots = Plot::getFilteredPlotsData($lang, $filters, $sort)
            ->paginate($limit, ['gid'], 'page', $page);

        $response = [
            "rows" => $plots->items(),
            "total" => $plots->total(),
        ];

        return new JsonResponse($response, 200, []);
    }

    /**
     * @OA\Get(
     *     path="/apigs/v2/plots/{gid}/details",
     *     summary="Plot details",
     *     @OA\Response(
     *          response="200",
     *          description="OK"
     *      )
     * )
     *
     * @throws ValidationException
     * @return JsonResponse
     */
    public function getPlotDetails($gid)
    {
        $requestData = Request::all();
        $requestData['gid'] = $gid;

        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'date' => 'sometimes|required|date_format:Y-m-d',
            'farm_year' => 'required|integer',
            'gid' => 'required|integer'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $lang = Request::get('lang');
        $date = Request::get('date');
        $farmYear = Request::get('farm_year');

        $filters = [
            'date' => $date,
            'farm_year' => $farmYear,
            'plot_ids' => [$gid],
        ];

        $plotDetails = Plot::getDetails($filters, $lang);

        if (!$plotDetails) {
            return new JsonResponse([], 200);
        }

        $currentMeteoData = $this->meteo->getCurrentMeteo($gid);
        $weatherHistoryData = $this->meteo->getHistoryWeatherData($gid, $plotDetails['sowing_date'], $plotDetails['harvest_date']);
        $plotDetails['current_weather']['pictocode'] = null;
        $plotDetails['current_weather']['temperature'] = null;
        $plotDetails['rainfall_since_sowing'] = null;

        if (count($currentMeteoData) > 0) {
            $plotDetails['current_weather']['pictocode'] = $currentMeteoData['pictocode'];
            $plotDetails['current_weather']['temperature'] = $currentMeteoData['temperature'];
        }

        if (is_array($weatherHistoryData) && !isset($weatherHistoryData['error'])) {
            $rainfallSinceSowing = array_sum(array_column($weatherHistoryData, 'precipitation'));
            $plotDetails['rainfall_since_sowing'] = round($rainfallSinceSowing, 3);
        }

        $plotDetails['farming_years'] = $this->orderService->getFarmYearsWithOrders($gid);
        return new JsonResponse($plotDetails, 200, []);
    }

    /**
     * @OA\Put(
     *     path="/apigs/v2/plots/{gid}/update",
     *     summary="Update plot",
     *     @OA\Response(
     *          response="200",
     *          description="OK"
     *      )
     * )
     *
     * @throws ValidationException
     * @return JsonResponse
     */
    public function updatePlot($gid)
    {
        $requestData = Request::all();
        $requestData['gid'] = $gid;

        $validator = Validator::make($requestData, [
            'gid' => 'required|integer',
            'name' => 'string',
            'irrigated' => 'boolean',
            'crops' => 'array',
            'crops.*.id' => 'required|integer',
            'crops.*.sowing_date' => 'required|date_format:Y-m-d',
            'crops.*.harvest_date' => 'required|date_format:Y-m-d',
            'crops.*.plot_crop_rel_id' => 'integer',
            'crops.*.is_primary' => 'boolean',
            'crops.*.category_id' => 'integer',
            'crops.*.hybrid_id' => 'integer',
            'farm_id' => 'required|integer'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $plotName = Request::get('name', null);
        $crops = Request::get('crops', []);
        $irrigated = Request::get('irrigated', null);
        $farmId= Request::get('farm_id', null);

        $farm = Farm::findOrFail($farmId);
        $this->plotService->updateExistingPlot($farm, $gid, $plotName, [], $crops, $irrigated);
        return new JsonResponse('Success!', 200);
    }
}
