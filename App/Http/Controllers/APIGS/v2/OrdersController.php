<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS\v2;

use App\Models\OrderSatelliteVra;
use App\Models\OrderSoilVra;
use Auth;
use DB;
use http\Exception\RuntimeException;
use Validator;
use Request;
use Response;
use App\Exceptions\ValidationException;
use App\Services\Order\OrderService;
use Illuminate\Http\JsonResponse;


class OrdersController
{
    private $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    /**
     * @OA\Post(
     *    path="/apigs/v2/orders/store",
     *    summary="Generate orders.",
     *    @OA\Response(
     *            response="200",
     *            description="Json"
     *     )
     * )
     * @return JsonResponse
     * @throws ValidationException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function postStore()
    {
        $headerParams = [];
        $protocolData = null;

        $validator = Validator::make(Request::all(), [
            'contractId' => 'required|integer',
            'organizationId' => 'required|integer',
            'contractType' => 'required|string',
            'plots' => 'required|array',
            'existingOrderId' => 'sometimes|integer|nullable',
            'filter' => 'sometimes|required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $contractId = Request::get('contractId');
        $organizationId = Request::get('organizationId');
        $contractType = Request::get('contractType');
        $plots = Request::get('plots');
        $existingOrderId = Request::get('existingOrderId');
        $headerParams['filter'] = Request::get('filter', []);

        try {
            $protocolData = $this->orderService->postStore($contractId, $contractType, $plots, $organizationId, $headerParams, $existingOrderId);
        } catch (\Exception $e) {
            return new JsonResponse([$e->getMessage()], 503);
        }

        return new JsonResponse($protocolData, 201);
    }

    /**
     * @OA\Get(
     *     path="/apigs/orders/vra-orders",
     *     summary="Get VRA orders",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getVraOrders()
    {
        $validator = Validator::make(Request::all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'filters' => 'array',
            'filters.element' => 'string',
            'filters.farm_year' => 'integer',
            'filters.plot_ids' => 'json',
            'filters.farm_ids' => 'json',
            'filters.crop_ids' => 'json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $page = Request::get('page');
        $limit = Request::get('limit');
        $filters = Request::get('filters', []);


        $filters["plot_ids"] = json_decode(Request::input('filters.plot_ids', "[]"));
        $filters["farm_ids"] = json_decode(Request::input('filters.farm_ids', "[]"));
        $filters["crop_ids"] = json_decode(Request::input('filters.crop_ids', "[]"));
        $filters["farm_year"] = Request::input('filters.farm_year');
        $filters["element"] = Request::input('filters.element');

        $query = OrderSoilVra::getFilteredOrdersQuery($filters)->unionAll(OrderSatelliteVra::getFilteredOrdersQuery($filters));
        $listOfOrders = $query->paginate($limit, ['*'], 'page', $page);

        return new JsonResponse([
            'total' => $listOfOrders->total(),
            'rows' => $listOfOrders->items()
        ], 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/orders/vra/{vraOrderId}",
     *     summary="Get soil VRA order",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getSoilVraOrder(int $vraOrderId)
    {
        $order = OrderSoilVra::getVraOrderDetails($vraOrderId);

        return new JsonResponse($order, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/orders/nvra/{vraOrderId}",
     *     summary="Get satellite VRA order",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getSatelliteVraOrder(int $vraOrderId)
    {
        $order = OrderSatelliteVra::getVraOrderDetails($vraOrderId);

        return new JsonResponse($order, 200);
    }
}
