<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\MachineImplement;
use App\Services\Implement\MachineImplementService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Auth;

class MachineImplementController extends BaseController
{
    private $machineImplementService;

    public function __construct(MachineImplementService $machineImplementService)
    {
        $this->machineImplementService = $machineImplementService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine-implements/sync
     *     summary="Sync wialon implement based on organization's integrations",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function sync(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id');
        $this->machineImplementService->syncImplements($organizationId);
        $response = MachineImplement::where('organization_id', $organizationId)->orderBy('id', 'desc')->get();

        return new JsonResponse($response, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/machine-implements
     *     summary="Get all machine implements data",
     *     @OA\Response(
     *          response="200",
     *          description="Array"
     *     )
     * )
     * 
     * @param Request $request
     * 
     * @throws ValidationException
     * @return JsonResponse
     */
    public function get(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer',
            'name' => 'string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = $request->get('organization_id',  Auth::user()->lastChosenOrganization->id);
        $filters = [
            'name' => $request->get('name', null)
        ];

        $machineImplementsQuery = MachineImplement::getFilteredMachineImplementsQuery($organizationId, $filters);
        $machineImplements = $machineImplementsQuery->orderBy('id', 'desc')->get();

        return new JsonResponse($machineImplements, 200);
    }

    /**
     * @OA\PUT(
     *     path="/apigs/machine-implements/{machineImplementId}",
     *     summary="Update machine implement",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @param $machineImplementId
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function update($machineImplementId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'organization_id' => 'required|integer',
            'width' => 'required|numeric',
            'name' => 'required|string',
            'status' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $response = $this->machineImplementService->update($machineImplementId, $request->all());

        return new JsonResponse($response, 200);
    }


    /**
     * @OA\Get(
     *     path="/apigs/machine-implements/statuses",
     *     summary="Get all statuses for machine implements",
     *     @OA\Response(
     *          response="200",
     *          description="Array"
     *      )
     * )
     *
     * @return JsonResponse
     */
    public function getImplementStatuses()
    {
        $machineImplementStatuses = $this->machineImplementService->getImplementStatuses();
        return new JsonResponse($machineImplementStatuses, 200);
    }
}
