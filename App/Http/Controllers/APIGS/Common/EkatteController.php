<?php

namespace App\Http\Controllers\APIGS\Common;

use App\Http\Controllers\APIGS\BaseController;
use App\Models\Ekatte;
use Request;
use Validator;
use App\Exceptions\ValidationException;

class EkatteController extends \App\Http\Controllers\BaseController
{

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function getEkatte()
    {
        $arrValidator = [
            'name' => 'string',
        ];

        $validator = Validator::make(Request::all(), $arrValidator);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $resultQuery = Ekatte::select('ekatte_code as id', 'ekatte_name as name')
            ->orderBy('ekatte_name');

        //Filter   
        if (Request::get('name')) {
            $resultQuery->where('ekatte_name', 'ILIKE', trim('%' . Request::get('name') . '%'));
            $resultQuery->orWhere('ekatte_code', 'ILIKE', trim('%' . Request::get('name') . '%'));
        }

        $result = $resultQuery->get();

        return $result;
    }
}
