<?php

namespace App\Http\Controllers\APIGS\Common;

use App\Http\Controllers\APIGS\BaseController;
use App\Models\GlobalUser;
use App\Models\User;
use App\Models\Role;
use Auth;

class DealersController extends \App\Http\Controllers\BaseController
{

    /**
     * @return mixed
     */
    public function index()
    {
        $globalUsers = GlobalUser::whereIs('SELLER_ADMIN')->where('active', true)->get();
        $globalUsersIds = $globalUsers->map(function ($user) {
            return $user->only(['old_id']);
        });

        $dealers = User::select('su_users.id', 'su_users.name')->whereIn('id', $globalUsersIds)->orderBy('su_users.name')->get();

        return $dealers;
    }
}
