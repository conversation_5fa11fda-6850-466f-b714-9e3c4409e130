<?php

namespace App\Http\Controllers\APIGS\Common;

use App\Http\Controllers\BaseController;
use App\Models\Crop;
use App\Models\CropHybrid;
use Validator;
use App\Exceptions\ValidationException;
use Request;
use Config;
use App\Helpers\Helper;
use App\Models\CropCategory;
use Illuminate\Http\JsonResponse;

class CropsController extends BaseController
{

    /**
     * @return array
     */
    public function getIndex()
    {
        $crop_name = Config::get('globals.CROP_NAME');

        $cultures = Crop::get(['id', 'crop_code as code', '' . $crop_name . ' as name', 'priority'])->toArray();

        if (Request::get('priority_first') && Request::get('priority_first') == true) {
            usort($cultures, function ($itemA, $itemB) {
                return ($itemA['priority'] > $itemB['priority']) ? -1 : 1;
            });
        }

        $response = array_map(function ($culture) {
            unset($culture['priority']);
            return $culture;
        }, $cultures);

        return $response;
    }

    /**
     * @return array
     */
    public function getSearch()
    {
        $locale = Request::get('locale') ? Request::get('locale') : Config::get('app.locale');
        $crop_name = Config::get('globals.CROP_NAME');
        $crop_name_with_locale = $crop_name . '_' . $locale;
        $query = mb_strtolower(Request::get('q'),'UTF-8');
        $limit = Request::get('limit', 10);

        $cultures = Crop::select(['id', 'crop_code as code', '' . $crop_name_with_locale . ' as name', 'priority'])
            ->where($crop_name_with_locale, 'ILIKE', '%' . $query . '%')
            ->orderBy($crop_name_with_locale, 'asc')
            ->take($limit)
            ->get()
            ->toArray();

        return $cultures;
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function getHybrid()
    {
        $validator = Validator::make(Request::all(), [
            'crop_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $result = CropHybrid::select([
            'id',
            'crop_id',
            'name',
            'params',
        ])
            ->where('crop_id', Request::get('crop_id'))
            ->get()
            ->toArray();

        return $result;
    }

    /**
     * @return mixed
     */
    public function getGdd()
    {
        $locale = Request::get('locale') ? Request::get('locale') : Config::get('app.locale');
        $crop_name = Config::get('globals.CROP_NAME');
        $crop_name_with_locale = $crop_name . '_' . $locale;

        return Crop::selectRaw("id, gdd_collection, initcap(".$crop_name_with_locale.") as ".$crop_name."")
            ->where('gdd_collection', '!=', 'none')
            ->orderBy('priority')
            ->orderBy($crop_name)
            ->get()
            ->toArray();
    }

    /**
     * @throws ValidationException
     * @return JsonResponse
     */
    public function getCategoriesByCrop()
    {
        $validator = Validator::make(Request::all(), [
            'crop_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $cropId = Request::get('crop_id');
        $categoriesByCrop = CropCategory::getByCrop($cropId);

        return new JsonResponse($categoriesByCrop);
    }
}
