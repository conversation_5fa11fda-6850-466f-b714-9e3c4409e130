<?php

namespace App\Http\Controllers\APIGS\Common;

use App\Http\Controllers\APIGS\BaseController;
use App\Models\Organization;
use App\Models\StaticModels\FarmingYear;
use Auth;

class FarmingYearsController extends \App\Http\Controllers\BaseController
{

    /**
     * @return \Illuminate\Support\Collection
     */
    public function index()
    {
        return FarmingYear::getAll();
    }

    /**
     * @param Organization $organization
     * @return \Illuminate\Support\Collection
     */
    public function byOrganization(Organization $organization)
    {
        return FarmingYear::byOrganization($organization);
    }
}
