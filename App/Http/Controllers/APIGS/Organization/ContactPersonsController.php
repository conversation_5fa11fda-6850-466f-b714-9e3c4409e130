<?php
namespace App\Http\Controllers\APIGS\Organization;

use App;
use App\Exceptions\ValidationException;
use App\Models\Organization;
use Auth;
use Config;
use DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * Class OrganizationController
 * @package App\Http\Controllers\APIGS
 */
class ContactPersonsController extends App\Http\Controllers\BaseController
{
    /**
     * @OA\Get(
     *     path="/apigs/organization/:organizationId/contact-persons",
     *     summary="Get farms in organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization $organization
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function list(Organization $organization)
    {
        return array_map(function($item) use ($organization){
            $item["organization_name"] = $organization->name;
            return $item;
        }, $organization->contactsPersons()->get()->toArray());
    }

    /**
     * @OA\Post(
     *     path="/apigs/organization/:organizationId/contact-persons",
     *     summary="Create contact persons in organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization $organization
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function create(Organization $organization, Request $request)
    {
        $arrRequest = json_decode($request->get('data'), true);

        $validator = Validator::make($arrRequest, [
            'name' => 'required|string',
            'email' => 'required|email',
            'phone' => 'required|string',
            'position' => 'string',
            'note' => 'string',
            'is_representative' => 'boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $contactPersonObj = new Organization\ContactPerson();
        $contactPersonObj->fill($arrRequest);
        $contactPersonObj->organization()->associate($organization);
        $contactPersonObj->save();

        return new JsonResponse($contactPersonObj, 201);
    }

    /**
     * @OA\Put(
     *     path="/apigs/organization/:organizationId/contact-persons/:contactPersonId",
     *     summary="Update contact persons in organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization $organization
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function update(Organization $organization, Organization\ContactPerson $contactPerson, Request $request)
    {
        $arrRequest = json_decode($request->get('data'), true);

        $validator = Validator::make($arrRequest, [
            'name' => 'required|string',
            'email' => 'required|email',
            'phone' => 'required|string',
            'position' => 'string',
            'note' => 'string',
            'is_representative' => 'boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $contactPerson->update($arrRequest);

        return new JsonResponse($contactPerson, 200);
    }

    /**
     * @OA\Delete(
     *     path="/apigs/organization/:organizationId/contact-persons/:contactPersonId",
     *     summary="Delete contactPerson",
     *     @OA\Response(
     *     response="204"
     * )
     * )
     *
     * @param Organization $organization
     * @return JsonResponse
     * @throws \Exception
     */
    public function delete(Organization $organization, Organization\ContactPerson $contactPerson)
    {
        $contactPerson->delete();
        return new JsonResponse(null, 204);
    }
}
