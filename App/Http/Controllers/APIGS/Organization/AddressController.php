<?php
namespace App\Http\Controllers\APIGS\Organization;

use App;
use App\Exceptions\ValidationException;
use App\Models\Organization;
use Auth;
use Config;
use DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * Class OrganizationController
 * @package App\Http\Controllers\APIGS
 */
class AddressController extends App\Http\Controllers\BaseController
{
    /**
     * @OA\Get(
     *     path="/apigs/organization/:organizationId/address",
     *     summary="Get addresses in organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization $organization
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function list(Organization $organization)
    {
        return $organization->addresses()->get();
    }

    /**
     * @OA\Post(
     *     path="/apigs/organization/:organizationId/address",
     *     summary="Create addresses in organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization $organization
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function create(Organization $organization, Request $request)
    {
        $arrRequest = json_decode($request->get('data'), true);

        $validator = Validator::make($arrRequest, [
            'country' => 'required|string',
            'city' => 'required|string',
            'address' => 'required|string',
            'note' => 'string',
            'is_main' => 'boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $addressObj = new Organization\Address();
        $addressObj->fill($arrRequest);
        $addressObj->organization()->associate($organization);
        $addressObj->save();

        return new JsonResponse($addressObj, 201);
    }

    /**
     * @OA\Put(
     *     path="/apigs/organization/:organizationId/address/:addressId",
     *     summary="Update addresses in organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization $organization
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function update(Organization $organization, Organization\Address $address, Request $request)
    {
        $arrRequest = json_decode($request->get('data'), true);

        $validator = Validator::make($arrRequest, [
            'country' => 'required|string',
            'city' => 'required|string',
            'address' => 'required|string',
            'note' => 'string',
            'is_main' => 'boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $address->update($arrRequest);

        return new JsonResponse($address, 200);
    }

    /**
     * @OA\Delete(
     *     path="/apigs/organization/:organizationId/address/:addressId",
     *     summary="Delete contactPerson",
     *     @OA\Response(
     *     response="204"
     * )
     * )
     *
     * @param Organization $organization
     * @return JsonResponse
     * @throws \Exception
     */
    public function delete(Organization $organization, Organization\Address $address)
    {
        $address->delete();
        return new JsonResponse(null, 204);
    }
}
