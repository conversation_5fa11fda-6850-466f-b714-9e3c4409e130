<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS;


use App\Http\Controllers\BaseController;
use App\Models\Farm;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use OpenApi\Annotations as OA;
use Validator;
use Auth;
use App\Exceptions\ValidationException;

/**
 * Class FarmController
 * @package App\Http\Controllers\APIGS
 */
class FarmController extends BaseController
{
    /**
     * @OA\Get(
     *     path="/apigs/farms",
     *     summary="Get all farms",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return Farm[]|\Illuminate\Database\Eloquent\Collection
     */
    public function index()
    {
        return Farm::all();
    }

    /**
     * @OA\Post(
     *     path="/apigs/farms",
     *     summary="Create farm",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $name = $request->get("name");
        $organization = Auth::user()->lastChosenOrganization;

        if (Farm::existsBy($name, $organization)) {
            return new JsonResponse(["error" => "Farm already exists"], 400);
        }

        $arrRequest = $request->all();
        $arrRequest["organization_id"] = $organization->id;

        $farm = Farm::create($arrRequest);
        $farm->users()->attach(Auth::user(), ['is_visible' => true]);

        return new JsonResponse($farm, 201);
    }

    /**
     * @OA\Get(
     *     path="/apigs/farms/:farmId",
     *     summary="Get farm",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Farm $farm
     * @return Farm
     */
    public function show(Farm $farm)
    {
        return $farm;
    }

    /**
     * @OA\Put(
     *     path="/apigs/farms/:farmId",
     *     summary="Update farm",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     * @param Farm $farm
     * @return Farm
     * @throws \Exception
     */
    public function update(Request $request, Farm $farm)
    {
        $this->authorize('update', $farm);

        $dataRequest = json_decode($request->get('data'), true);
        if(!$dataRequest) {
            $dataRequest = $request->all();
        }

        $farm->update($dataRequest);
        return $farm;
    }

    /**
     * @OA\Delete(
     *     path="/apigs/farms/:farmId",
     *     summary="Delete farm",
     *     @OA\Response(
     *     response="204"
     * )
     * )
     *
     * @param Farm $farm
     * @return JsonResponse
     * @throws \Exception
     */
    public function destroy(Farm $farm)
    {
        $this->authorize('update', $farm);

        $farm->delete();
        return new JsonResponse(null, 204);
    }
}
