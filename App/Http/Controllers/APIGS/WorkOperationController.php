<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\WorkOperation;
use App\Services\Implement\WorkOperationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WorkOperationController extends BaseController
{
    private $workOperationService;

    public function __construct(WorkOperationService $workOperationService)
    {
        $this->workOperationService = $workOperationService;

    }

    /**
     * @OA\Get(
     *     path="/apigs/work-operation"
     *     summary="Get all work operation data",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * 
     * @param Request $request
     *
     * @throws ValidationException
     * @return JsonResponse
     */
    public function get(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'implement_id' => 'sometimes|required|integer'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }


        $filter = [
            'implement_id' => $request->get("implement_id")
        ];

        $workOperations = $this->workOperationService->getWorkOperationsFiltered($filter);

        return new JsonResponse($workOperations, 200);
    }

    /**
     * * @OA\Get(
     *     path="/apigs/work-operation/names
     *     summary="Get all work operation data",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getNameEnums(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array',
            'filter.start_date' => 'sometimes|required|date_format:Y-m-d',
            'filter.end_date' => 'sometimes|required|date_format:Y-m-d',
            'filter.farm_ids' => 'sometimes|required|json',
            'filter.plot_ids' => 'sometimes|required|json',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $filter = $request->get('filter', []);

        if (count($filter) === 0) {
            $workOperations = WorkOperation::getNameEnumsData();
        } else {
            $workOperations = $this->workOperationService->getWorkOperationsNameFilteredData($filter);
        }

        return new JsonResponse($workOperations, 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/work-operation",
     *     summary="Create work operation",
     *     @OA\Response(
     *     response="201",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'implement_id' => 'required|numeric',
            'name' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $data = $request->all();
        $workOperation = new WorkOperation();
        $workOperation->fill($data);
        $workOperation->save();

        return new JsonResponse($workOperation, 201);
    }

    /**
     * @OA\PUT(
     *     path="/apigs/work-operation/:implementId",
     *     summary="Update work operation data",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @param $implementId
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function update($implementId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'workOperations' => 'array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $workOperationsFromRequest = $request->get('workOperations', []);
        $response = $this->workOperationService->update($implementId, $workOperationsFromRequest);

        return new JsonResponse($response, 200);
    }

}
