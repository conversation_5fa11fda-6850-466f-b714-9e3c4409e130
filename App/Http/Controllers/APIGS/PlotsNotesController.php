<?php

namespace App\Http\Controllers\APIGS;

use App\Http\Controllers\BaseController;
use App\Models\User;
use OpenApi\Annotations as OA;
use Request;
use Response;
use App\Models\Plot;
use App\Models\PlotNote;
use DB;
use Auth;
use App\Helpers\Helper;
use Validator;
use App\Exceptions\ValidationException;
use App\Exceptions\ForbiddenException;
use Config;

class PlotsNotesController extends BaseController
{

    public function __construct()
    {
        $this->middleware('verify-write-rights', ['except' => ['getList']]);
    }

    /**
     * @OA\Get(
     *     path="/apigs/plots-notes/list",
     *     summary="List of plot notes",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ForbiddenException
     * @throws ValidationException
     */
    public function getList()
    {

        $validator = Validator::make(Request::all(), [
            'gid' => 'required|integer',
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'note' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $this->checkRights();

        $resultQuery = PlotNote::select('su_satellite_plots_notes.id', 'su_satellite_plots_notes.plot_id', 'su_satellite_plots_notes.note', 'su_satellite_plots_notes.created_at')
            ->join('su_satellite_plots', 'su_satellite_plots.gid', '=', 'su_satellite_plots_notes.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('su_satellite_plots_notes.plot_id', Request::get('gid'))
            ->orderBy('su_satellite_plots_notes.id', 'desc');

        //Filter   
        if (Request::get('note')) {
            $resultQuery->where('note', 'ILIKE', trim('%' . Request::get('note') . '%'));
        }

        $result = $resultQuery->paginate(Request::get('limit'));

        return [
            'total' => $result->total(),
            'rows' => $result->items()
        ];
    }

    /**
     * @OA\Post(
     *     path="/apigs/plots-notes/create-note",
     *     summary="Create plot note",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return mixed
     * @throws ForbiddenException
     * @throws ValidationException
     */
    public function postCreateNote()
    {

        $validator = Validator::make(Request::all(), [
            'note' => 'required|string',
            'gid' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $this->checkRights();

        $plotNote = new PlotNote();
        $plotNote->plot_id = Request::get('gid');
        $plotNote->note = Request::get('note');
        $result = $plotNote->save();

        if (!$result) {
            return Response::json(array('error' => 'Insert faild'), 500);
        }

        return Response::json(array('Response' => 'Success', 'noteId' => $plotNote->id));
    }

    /**
     * @OA\Post(
     *     path="/apigs/plots-notes/delete-note",
     *     summary="Delete plot note",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return mixed
     * @throws ForbiddenException
     * @throws ValidationException
     */
    public function postDeleteNote()
    {

        $validator = Validator::make(Request::all(), [
            'id' => 'required|integer',
            'gid' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $this->checkRights();

        $plotNote = PlotNote::find((int)Request::get('id'));

        if ($plotNote) {
            $plotNote->delete();

            return Response::json(array('Response' => 'Success'));
        }

        return Response::json(array('Response' => 'No note deleted.'));
    }


    /**
     * checkRights Check Rights
     * @return void
     */
    private function checkRights()
    {
        /**
         * @var Plot $plot
         */
        $plot = Plot::find(Request::get('gid'));

        /**
         * @var User $user
         */
        $user = Auth::user();

        if (!$user->farms()->get()->contains($plot->farm()->get()->first())) {
            throw new ForbiddenException();
        }
    }
}