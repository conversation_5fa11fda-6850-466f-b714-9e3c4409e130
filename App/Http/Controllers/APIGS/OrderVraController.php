<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\TFConnect;
use App\Exceptions\ForbiddenException;
use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Order;
use App\Models\OrderVra;
use App\Services\Order\OrderVraService;
use App\Services\Reports\PrintVraService;
use Auth;
use OpenApi\Annotations as OA;
use Request;
use Response;
use Validator;

class OrderVraController extends BaseController
{

    private $tfc;
    private $printVraService;
    private $orderVraService;

    public function __construct(TFConnect $tfc, PrintVraService $printVraService, OrderVraService $orderVraService)
    {
        $this->tfc = $tfc;
        $this->printVraService = $printVraService;
        $this->orderVraService = $orderVraService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/order-vra/orders",
     *     summary="Get VRA orders",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     */
    public function getOrders()
    {
        $validator = Validator::make(Request::all(), [
            'plotId' => 'required|integer',
            'farmYear' => 'integer|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $model = new OrderVra();
        $plotId = Request::get('plotId');
        $farmYear = Request::get('farmYear');

        try {
            $order = $model->getVraOrdersByPlots((int) $plotId, (int) $farmYear);
        } catch (\Exception $e) {
            throw $e;
        }

        return isset($order[0]) ? $order : [];
    }

    /**
     * @OA\Post(
     *     path="/apigs/order-vra/shape",
     *     summary="Export VRA shape",
     *     @OA\Response(
     *     response="200",
     *     description="File"
     * )
     * )
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     * @throws ValidationException
     */
    public function getShape()
    {
        $validator = Validator::make(Request::all(), [
            'orderId' => 'required|integer',
            'format' => ['required', 'max:255', "in:john_deere,shp,trimble,isoxml"],
            'forTfc' => 'boolean',
            'deviceSerial' => 'alpha_num|min:16|max:16',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $forTfc = Request::get('forTfc');
        $deviceSerial = Request::get('deviceSerial');

        try {
            $shape = $this->orderVraService->exportVraMap(
                Request::get('orderId'),
                OrderVraService::VRA_SATELLITE_ORDER_TYPE,
                Request::get('format'),
                Request::get('soil_element')
            );
        } catch (\Exception $e) {
            throw $e;
        }

        if ($forTfc) {
            $info = $this->tfc->uploadFile($deviceSerial, $shape);
            $this->tfc->setStatus($deviceSerial, $info['file_id'], 'sending');
            return \Response::json(['result' => 'exported_tfc', 'info' => $info], 200);
        }

        return response()->download($shape)->deleteFileAfterSend(false);
    }

    /**
     * @OA\Post(
     *     path="/apigs/order-vra/create-order",
     *     summary="Create VRA order",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @return mixed
     * @throws ForbiddenException
     * @throws ValidationException
     */
    public function postCreateOrder()
    {
        if (!Auth::user()->globalUser()->can('create_vra_satellite_maps') && !Auth::user()->globalUser()->can('create_vra_soil_maps')) {
            throw new ForbiddenException();
        }

        $validator = Validator::make(Request::all(), [
            'plot_id' => 'integer|required',
            'order' => 'array|required',
            'class_number' => 'integer|required',
            'layer_id' => 'integer|required',
            'flat_rate' => 'numeric|required',
            'flat_rate_total' => 'numeric|required',
            'variable_rate_total' => 'numeric|required',
            'difference' => 'numeric|required',
            'difference_percent' => 'numeric|required',
            'data' => 'array|required',
            'vector_data' => 'array|required',
            'product_percent' => 'numeric|required',
            'tiff_path' => 'required',
            'vra_name' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $model = new Order();

        try {
            $id = $model->createVraOrder(Request::all());
        } catch (\Exception $e) {
            throw $e;
        }

        return \Response::json(['result' => 'order created', 'id' => $id], 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/order-vra/cance-order",
     *     summary="Cancel VRA order",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @return mixed
     * @throws ValidationException
     */
    public function postCancelOrder()
    {
        $validator = Validator::make(Request::all(), [
            'order_id' => 'integer|required',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $model = new Order();

        try {

            $order = $model->loadWaitingOrder(Request::all());

            if (!$order) {
                throw new ForbiddenException();
            }

            $model->cancelOrder($order);
        } catch (\Exception $e) {
            throw $e;
        }

        return \Response::json(['result' => 'order canceled'], 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/order-vra/print",
     *     summary="Print VRA order",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @return mixed
     * @throws ValidationException
     */
    public function printOrder()
    {
        $validator = Validator::make(Request::all(), [
            'orderData' => 'json',
            'urlParams' => 'json',
            'pricePreparation' => 'integer',
            'chartImage' => 'nullable|string',
            'tableContent' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $orderData = json_decode(Request::get('orderData'), true);
        $urlParams = json_decode(Request::get('urlParams'), true);
        $pricePerKg = Request::get('pricePreparation');
        $chartImage = Request::get('chartImage');
        $tableContent = Request::get('tableContent');

        $result = $this->printVraService->createPdfFile($orderData, $urlParams, $pricePerKg, $chartImage, $tableContent);

        if (is_array($result) && isset($result['error'])) {
            //If No file saved
            return Response::json($result, 500);
        }

        return Response::json(array('fileName' => $result));
    }

}
