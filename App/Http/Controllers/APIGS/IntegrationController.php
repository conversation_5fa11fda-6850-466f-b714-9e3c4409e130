<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\CMS\ContractService;
use App\Exceptions\ValidationException;
use App\Models\IntegrationAddress;
use App\Models\Integration;
use App\Service\Log\RequestLogService;
use App\Services\Wialon\WialonService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseController;
use Illuminate\Support\Facades\Validator;


/**
 * Class IntegrationController
 * @package App\Http\Controllers\Controllers
 */
class IntegrationController extends BaseController
{
    private $requestLogService;
    private $contractService;
    private $wialonService;

    public function __construct(RequestLogService $requestLogService, ContractService $contractService, WialonService $wialonService)
    {
        $this->requestLogService = $requestLogService;
        $this->contractService = $contractService;
        $this->wialonService = $wialonService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/integration/organization/:organizationId",
     *     summary="Get integration",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param string $organizationId
     * @param Request $request
     * @return JsonResponse
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws ValidationException
     */
    function showByOrganization(string $organizationId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_slug_short' => 'string'
        ]);

        if($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $packageSlugShort = $request->get('package_slug_short');
        $integrations = Integration::getLinkedQuery([$organizationId], $packageSlugShort)->get(['su_integration.*'])->toArray();

        return new JsonResponse($integrations, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/integration/history/:integrationId",
     *     summary="Get integration history",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @param $integrationId
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getIntegrationHistory($integrationId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $log = $this->requestLogService->getIntegrationHistory($integrationId);

        return new JsonResponse(['rows' => $log], 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/integration",
     *     summary="Create integration",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|string',
            'organization_id' => 'required|numeric',
            'contract_id' => 'required|numeric',
            'package_id' => 'required|numeric',
            'package_slug_short' => 'required|string',
            'package_period' => 'required|string',
            'integration_address' => 'required|array',
            'status' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $data = $request->all();
        $baseUrl = $data['integration_address']['base_url'];
        $data['integration_address'] = $data['integration_address']['id'];
        $integration = new Integration();
        $integration->fill($data);
        // Get wialon data
        $token = $data['token'];
        // TO-DO: get baseUrl according to integration_address
        $wialonData = $this->wialonService->login($token, $baseUrl);
        $integration->remote_username = $wialonData['user']['nm'];
        $integration->remote_user_id = $wialonData['user']['id'];

        $integration->save();

        return new JsonResponse($integration, 201);
    }

    /**
     * @OA\Put(
     *     path="/apigs/integration/:integrationId",
     *     summary="Update integration data",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @param Integration $integration
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateIntegration(Integration $integration, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|string',
            'organization_id' => 'required|numeric',
            'contract_id' => 'required|numeric',
            'package_id' => 'required|numeric',
            'package_slug_short' => 'required|string',
            'package_period' => 'required|string',
            'integration_address' => 'required|array',
            'status' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $data = $request->all();
        $data['integration_address'] = $data['integration_address']['id'];
        $integration->update($data);

        return new JsonResponse($integration, 200);
    }

}
