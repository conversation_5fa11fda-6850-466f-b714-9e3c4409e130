<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\Echarts\EChartsFormatter;
use App\Classes\Meteo\IStation;
use App\Classes\MeteoService;
use App\Http\Controllers\BaseController;
use App\Services\Station\ReportService;
use OpenApi\Annotations as OA;
use Request;
use App\Models\UserStation;
use DB;
use Auth;
use Validator;
use App\Exceptions\ValidationException;
use Response;
use Cache;
use App\Classes\Heap;
use Carbon\Carbon;
use App\Services\Station\StationService;
use Config;
use Illuminate\Http\JsonResponse;

class StationsController extends BaseController
{
    private $meteoService;
    private $stationService;
    private $reportService;

    public function __construct(Heap $heap, MeteoService $meteoService, StationService $stationService, ReportService $reportService)
    {
        $this->heap = $heap;
        $this->meteoService = $meteoService;
        $this->stationService = $stationService;
        $this->reportService = $reportService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/for-map",
     *     summary="List meteo stations",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return mixed
     */
    public function getForMap()
    {
        $validator = Validator::make(Request::all(), [
            'geojson_format' => 'boolean'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $userId = Auth::user()->id;
        $organizationId = Auth::user()->lastChosenOrganization->id;
        $useGeojsonFormat = Request::get('geojson_format', false);
        
        $response = $useGeojsonFormat ? UserStation::getForMapGeoJson($userId, $organizationId) : UserStation::getForMap($userId, $organizationId);

        return new JsonResponse($response);
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/data-history",
     *     summary="History data from meteo station",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function getDataHistory()
    {
        $validator = Validator::make(Request::all(), [
            'station_id' => 'required|integer',
            'period' => ['sometimes', 'max:255', "in:7days,14days,30days,6months,1year"],
            'feed' => ['sometimes', 'string', 'in:raw,hourly,daily'],
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationId = Request::get('station_id');
        $period = Request::get('period') ?: '1week';
        $feed = Request::get('feed') ?: 'daily';

        $userStation = UserStation::where('id', $stationId)->first();

        if (!$userStation) {
            return Response::json(array('error' => 'User station not found.'), 500);
        }

        /** @var IStation $stationObject */
        $stationObject = $userStation->getStationApi();

        //update last
        $stationInfo = $stationObject->getStationData();

        if (is_array($stationInfo) && isset($stationInfo['error'])) {
            return Response::json(array('error' => $stationInfo['error']), $stationInfo['http_code']);
        }

        if (!is_array($stationInfo) || !isset($stationInfo['dates']) || !isset($stationInfo['position'])) {
            return Response::json(array('error' => 'Station not found.'), 500);
        }

        $lastCommunication = $stationInfo['dates']['last_communication'];

        $timeZone = config('app.timezone');
        if (isset($stationInfo['position']['timezoneCode'])) {
            $timeZone = $stationInfo['position']['timezoneCode'];
        }

        $lastCommunication = (new Carbon($lastCommunication, $timeZone))->getTimestamp() * 1000;

        $heap = $this->heap;

        $now = new \DateTime();
        $periodStart = clone $now;
        $periodStart->modify('-' . $period);
        $stationInstallDate = \DateTime::createFromFormat('Y-m-d', $userStation->install_date);

        if ($stationInstallDate > $periodStart) {
            $periodStart = $stationInstallDate;
        }

        $historyPeriod = mktime(0, 0, 0, $periodStart->format('m'), $periodStart->format('d'),
            $periodStart->format('Y'));

        $cacheMinutes = 15; //15 min.
        $chart_data = $stationObject->getHistoryDataFrom($cacheMinutes, $historyPeriod, $feed,
            function () use ($userStation, $heap, $period, $feed) {
                $heap->track("History Basic {$feed} - {$period} Period - " . $userStation->type, [
                    'Station' => $userStation->name,
                    'Type' => "{$period} Period"
                ]);
            });

        if (is_array($chart_data) && isset($chart_data['error'])) {
            return Response::json(array('error' => $chart_data['error']), $chart_data['http_code']);
        }

        if (!is_array($chart_data)) {
            return Response::json(array('error' => 'Station not found.'), 500);
        }

        $data = array('chart_data' => $chart_data, 'last_communication' => $lastCommunication);

        return $data;
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/data",
     *     summary="History data from meteo station",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return false|string
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function getData()
    {
        $result = $this->getStationDataHistory();

        if (is_object($result)) {
            return $result;
        }

        return $this->meteoService->formatSimple($result);
    }

    private function validateStationRequest()
    {
        $stationId = Request::get('station_id');
        $feed = Request::get('feed') ?: 'daily';
        $period = Request::get('period') ?: '1week';
        $sensors = json_decode(Request::get('sensors'), true);
        $sensors = $sensors ? $sensors : [];

        $data = array(
            'station_id' => $stationId,
            'feed' => $feed,
            "period" => $period,
            "sensors" => $sensors
        );

        $validator = Validator::make($data, [
            'station_id' => 'required|integer',
            'sensors' => [
                'required',
                'array'
            ],
            'sensors.*' => [
                'required',
                'max:255',
                'in:all,air_temperature_min,air_temperature_max,air_temperature_avg,
                soil_temperature_min,soil_temperature_max,soil_temperature_avg,
                air_pressure_min,air_pressure_max,air_pressure_avg,
                air_humidity_min,air_humidity_max,air_humidity_avg,precipitation_sum,precipitation_cumulative_sum,wind_speed_max,wind_speed_avg,battery_last,radiation_avg,panel_voltage_last'
            ],
            'period' => ['sometimes', 'max:255', "in:7days,14days,30days,6months,1year,custom"],
            'feed' => ['sometimes', 'string', 'in:raw,hourly,daily'],
            'from_date' => 'date_format:Y-m-d',
            'to_date' => 'date_format:Y-m-d',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/data-history-charts",
     *     summary="History data from meteo station formatted for ECharts",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return false|string
     */
    public function getDataHistoryEChartsFormatted()
    {
        try {
            $result = $this->getStationDataHistory();
        } catch (\Exception $e) {
            return Response::json(array('error' => $e->getMessage()), $e->getCode());
        }

        return EChartsFormatter::format($result);
    }

    private function getStationDataHistory()
    {
        $this->validateStationRequest();
        $stationId = Request::get('station_id');
        $feed = Request::get('feed') ?: 'daily';
        $period = Request::get('period') ?: '1week';
        $sensors = json_decode(Request::get('sensors'), true);
        $sensors = $sensors ? $sensors : [];
        $fromDate = Request::get('from_date');
        $toDate = Request::get('to_date');

        $stationObj = UserStation::where('id', $stationId)->first()->getStationApi();
        return $stationObj->getStationReportEChart($feed, $period, $sensors, $fromDate, $toDate);
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/last-data",
     *     summary="Last data from meteo station",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function getLastData()
    {
        $validator = Validator::make(Request::all(), [
            'station_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationId = Request::get('station_id');

        $userStation = UserStation::where('id', $stationId)->first();
        $stationObject = $userStation->getStationApi();

        if (!$userStation) {
            return Response::json(array('error' => 'User station not found.'), 500);
        }

        $stationName = $userStation->name;
        $stationType = $userStation->type;

        //update last communication
        $stationInfo = $stationObject->getStationData();

        if (is_array($stationInfo) && isset($stationInfo['error'])) {
            return Response::json(array('error' => $stationInfo['error']), $stationInfo['http_code']);
        }

        if (!is_array($stationInfo) || !isset($stationInfo['dates']) || !isset($stationInfo['position'])) {
            return Response::json(array('error' => 'Station not found.'), 500);
        }

        $heap = $this->heap;

        $cacheMinutes = 5; //5 minutes

        $station_data = $stationObject->getCurrentSensorValues($cacheMinutes,
            function () use ($stationName, $heap, $stationType) {
                $heap->track('Current Data - ' . $stationType, [
                    'Station' => $stationName,
                    'Type' => 'Current'
                ]);
            });

        $stationDaily = $stationObject->getDailySensorValues();

        $sensors_daily_data = $stationDaily ? $stationDaily['sensors'] : [];
        $sensors_daily_start_from = $stationDaily ? $stationDaily['start_from_ts'] : null;

        $timeZone = config('app.timezone');
        if (isset($stationInfo['position']['timezoneCode'])) {
            $timeZone = $stationInfo['position']['timezoneCode'];
        }

        $lastCommunicationTs = (new Carbon($stationInfo['dates']['last_communication'], $timeZone))->getTimestamp() * 1000;

        $data = array(
            'sensors' => $station_data,
            'sensors_daily' => $sensors_daily_data,
            'sensors_daily_start_from' => $sensors_daily_start_from,
            'last_communication' => $lastCommunicationTs,
        );

        return $data;
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/hourly-data-history",
     *     summary="History data by hour from meteo station",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return array|mixed
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function getHourlyDataHistory()
    {
        $validator = Validator::make(Request::all(), [
            'station_name' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $stationName = Request::get('station_name');
        $userStation = UserStation::where('name', $stationName)->first();

        $stationObject = $userStation->getStationApi();

        $station_data = $stationObject->getStationData();

        if (is_array($station_data) && isset($station_data['error'])) {
            return Response::json(array('error' => $station_data['error']), $station_data['http_code']);
        }

        if (!is_array($station_data) || !isset($station_data['dates']) || !isset($station_data['position'])) {
            return Response::json(array('error' => 'Station not found.'), 500);
        }

        $lastCommunication = $station_data['dates']['last_communication'];


        $cacheMinutes = 60; //1 hours

        $last_data = $stationObject->historyDataHourlyCommon($stationName, 1, $cacheMinutes, function () {
        });

        if (is_array($last_data) && isset($last_data['error'])) {
            return Response::json(array('error' => $last_data['error']), $last_data['http_code']);
        }

        if (!is_array($last_data)) {
            return Response::json(array('error' => 'Station not found.'), 500);
        }
        $last_data = array_shift($last_data);

        $last_data['last_communication'] = $lastCommunication;

        return $last_data;
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/report-list",
     *     summary="Get Stations Report List",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @throws ValidationException
     * @throws \App\Exceptions\ForbiddenException
     */
    public function getStationsReportList()
    {
        $validator = Validator::make(Request::all(), [
            'from' => 'required|string',
            'to' => 'required|string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $from = Request::get('from');
        $to = Request::get('to');

        return $this->stationService->getStationsReportData($from, $to);
    }

    /**
     * @OA\Get(
     *     path="/apigs/stations/download-report-list",
     *     summary="Download Report List",
     *     @OA\Response(
     *     response="200",
     *     description="File"
     * )
     * )
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     * @throws ValidationException
     */
    public function downloadReportList()
    {
        $validator = Validator::make(Request::all(), [
            'from' => 'required|string',
            'to' => 'required|string',
            'format' => 'string'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $from = Request::get('from');
        $to = Request::get('to');
        $format = Request::get('format');

        return $this->reportService->reportList($from, $to, $format);
    }
}
