<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 4/3/2020
 * Time: 11:04 AM
 */

namespace App\Http\Controllers\APIGS;


use App\Http\Controllers\BaseController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Exceptions\ValidationException;
use App\Models\IntegrationAddress;

class IntegrationAddressController extends BaseController
{
    /**
     * @OA\Get(
     *     path="/apigs/integration-address
     *     summary="Get all integrations address",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return JsonResponse
     */
    public function index()
    {
        $result = IntegrationAddress::queryList()->get();
        return new JsonResponse($result, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/integration-address/:integrationAddressId",
     *     summary="Get integration address",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param $integrationAddress
     * @return JsonResponse
     */
    public function show(IntegrationAddress $integrationAddress)
    {
        return new JsonResponse($integrationAddress, 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/integration-address",
     *     summary="Create integration address",
     *     @OA\Response(
     *     response="201",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'url' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $data = $request->all();
        $integration = new IntegrationAddress();
        $integration->fill($data);
        $integration->save();

        return new JsonResponse($integration, 201);
    }

    /**
     * @OA\Put(
     *     path="/apigs/integration-address/:integrationAddressId",
     *     summary="Update integration address data",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @param IntegrationAddress $integrationAddress
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function update(IntegrationAddress $integrationAddress, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'url' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $data = $request->all();
        $integrationAddress->update($data);

        return new JsonResponse($integrationAddress, 200);
    }

}
