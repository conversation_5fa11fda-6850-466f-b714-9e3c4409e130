<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 6/11/2020
 * Time: 2:24 PM
 */

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\IrrigationEvent;
use App\Services\Common\MailService;
use App\Services\Irrigation\IrrigationEventsService;
use App\Services\Irrigation\IrrigationReportService;
use Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class IrrigationEventsController extends BaseController
{
    public function __construct(IrrigationReportService $irrigationReportService, MailService $mailService, IrrigationEventsService $irrigationEventService)
    {
        $this->irrigationReportService = $irrigationReportService;
        $this->mailService = $mailService;
        $this->irrigationEventService = $irrigationEventService;
    }

    /**
     * @OA\Get(
     *     path="/apigs/irrigation-events/history
     *     summary="Get irrigation events history",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function history(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'organization_id' => 'integer',
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $data = $request->all();
        $platformId = $data['platform_id'] ?? null;
        $limit = $data['limit'] ?? 5;

        $irrigationHistory = IrrigationEvent::getEventsHistory($platformId, $limit);
        return new JsonResponse($irrigationHistory, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/irrigation-events/name
     *     summary="Get irrigation events name by type (e.g. Irrigation, Movement, PressureAlarm ...)",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getEventsName(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'types' => 'json',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'platform_ids' => 'json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'platformIds' => json_decode($request->get('platform_ids', '[]')),
            'types' => json_decode($request->get('types', '[]'))
        ];

        $response = $this->irrigationEventService->getEventsNameByType($organizationId, $filter);

        return new JsonResponse($response, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/irrigation-events/count/echart
     *     summary="Get irrigation events count by type (e.g. Irrigation, Movement, PressureAlarm ...) for echart",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getEventsCountEchart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'types' => 'json',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'platform_ids' => 'json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'platformIds' => json_decode($request->get('platform_ids', '[]')),
            'types' => json_decode($request->get('types', '[]'))
        ];

        $response = $this->irrigationEventService->getEventsCountByType($organizationId, $filter);

        return new JsonResponse($response, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/irrigation-events/avg-rate/echart
     *     summary="Get irrigation events average water rate and area by days (for echart).",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getEventsAvgRateEchart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'platform_ids' => 'json',
            'types' => 'json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'platformIds' => json_decode($request->get('platform_ids', '[]')),
            'types' => json_decode($request->get('types', '[]'))
        ];

        $irrigationEventsAvgRateEchart = IrrigationEvent::getIrrigationEventsAvgRateEchart($organizationId, $filter);
        return new JsonResponse($irrigationEventsAvgRateEchart, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/irrigation-events/report
     *     summary="Get irrigation events report.",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'required|integer',
            'limit' => 'required|integer',
            'group_by' => 'required|string',
            'order_by' => 'json',
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'platform_ids' => 'json',
            'types' => 'json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'platformIds' => json_decode($request->get('platform_ids', '[]')),
            'types' => json_decode($request->get('types', '[]')),
            'page' => $request->get('page'),
            'limit' => $request->get('limit')
        ];

        $groupBy = $request->get('group_by');
        $orderBy = json_decode($request->get('order_by', '[]'), true);

        $result = $this->irrigationReportService->getReport($organizationId, $filter, $groupBy, $orderBy);

        return new JsonResponse($result, 200);
    }

    /**
     * @OA\Get(
     *     path="/apigs/irrigation-events/report/{type}
     *     summary="Export Irrigation Events Report in .pdf or .xls format.",
     *     @OA\Response(
     *          response="200",
     *          description="A PDF or XLS file"
     *     )
     * )
     *
     * @param Request $request
     * @param string $type
     *
     * @throws ValidationException
     * @return File
     */
    public function exportIrrigationReport(Request $request, string $type)
    {
        $requestData = $request->all();
        $requestData["type"] = $type;

        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'type' => 'required|string|in:pdf,xls',
            'group_by' => 'required|string',
            'order_by' => 'json',
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'platform_ids' => 'json',
            'types' => 'json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'platformIds' => json_decode($request->get('platform_ids', '[]')),
            'types' => json_decode($request->get('types', '[]')),
        ];

        $groupBy = $request->get('group_by');
        $orderBy = json_decode($request->get('order_by', '[]'), true);

        $reportFilePath = $this->irrigationReportService->generateReport($type, $organizationId, $filter, $groupBy, $orderBy);
        $customFileName = 'irrigation_report_' . date('d_m_Y', $filter['from']) . '-' . date('d_m_Y', $filter['to']) . '.' . $type;

        return response()->download($reportFilePath, $customFileName)->deleteFileAfterSend(true);

    }

    /**
     * @OA\Get(
     *     path="/apigs/irrigation-events/report/{type}/send
     *     summary="Send Irrigation Events Report in .pdf or .xls format to specific email",
     *     @OA\Response(
     *          response="200",
     *          description="Success"
     *     )
     * )
     *
     * @param Request $request
     * @param string $type
     *
     * @throws ValidationException
     * @return JsonResponse
     */
    public function sendIrrigationReport(Request $request, string $type)
    {
        $requestData = $request->all();
        $requestData["type"] = $type;

        $validator = Validator::make($requestData, [
            'lang' => 'required|string|min:2|max:2',
            'type' => 'required|string|in:pdf,xls',
            'group_by' => 'required|string',
            'order_by' => 'json',
            'from' => 'required_with:to|integer',
            'to' => 'required_with:from|integer',
            'email' => 'required|email',
            'subject' => 'string',
            'message' => 'string',
            'farm_ids' => 'json',
            'plot_ids' => 'json',
            'crop_ids' => 'json',
            'platform_ids' => 'json',
            'types' => 'json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->lastChosenOrganization->id;
        $filter = [
            'from' => $request->get('from', strtotime('- 20 days')),
            'to' => $request->get('to', time()),
            'farmIds' => json_decode($request->get('farm_ids', '[]')),
            'plotIds' => json_decode($request->get('plot_ids', '[]')),
            'cropIds' => json_decode($request->get('crop_ids', '[]')),
            'platformIds' => json_decode($request->get('platform_ids', '[]')),
            'types' => json_decode($request->get('types', '[]')),
        ];

        $groupBy = $request->get('group_by');
        $orderBy = json_decode($request->get('order_by', '[]'), true);
        $email = $request->get('email');
        $subject = $request->get('subject', 'GeoSCAN Irrigation report');
        $message = $request->get('message', '');
        $title = trans('emailReport.irrigationReport');

        $reportFilePath = $this->irrigationReportService->generateReport($type, $organizationId, $filter, $groupBy, $orderBy);
        $customFileName = 'irrigation_report_' . date('d_m_Y', $filter['from']) . '-' . date('d_m_Y', $filter['to']) . '.' . $type;

        $this->mailService->sendFileToEmail($email, $title, $subject, $message, $reportFilePath, $customFileName);
        unlink($reportFilePath);

        return new JsonResponse('Success', 200);

    }

    /**
     * @OA\Post(
     *     path="/apigs/irrigation-events/reports/schedule/irrigation-per-day
     *     summary="Run irrigation per day reprot by organization, concret date and unit or all units",
     *     @OA\Response(
     *          response="200",
     *          description="Success"
     *     )
     * )
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function scheduleIrrigationPerDateReport(Request $request)
    {
        $requestData = $request->all();

        $validator = Validator::make($requestData, [
            'organization_id' => 'required|integer',
            'wialon_unit_id' => 'sometimes|required|integer',
            'to_date' => 'required|integer',
            'from_date' => 'required|integer',
        ]);

        $organizationId = $request->get('organization_id');
        $wialonUnitId = $request->get('wialon_unit_id', null);
        $toDate = $request->get('to_date');
        $fromDate = $request->get('from_date');

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $this->irrigationEventService->scheduleIrrigationPerDateReport($organizationId, $wialonUnitId, $toDate, $fromDate);

        return new JsonResponse('Success', 200);
    }

}
