<?php

namespace App\Http\Controllers\APIGS;

use App\Classes\CMS\ContractService;
use App\Http\Controllers\BaseController;
use App\Models\Organization;
use App\Models\Plot;
use Illuminate\Http\JsonResponse;
use Lara<PERSON>\Passport\Token;
use OpenApi\Annotations as OA;
use Request;
use App\Models\User;
use App\Models\GlobalUser;
use DB;
use Auth;
use App\Helpers\Helper;
use Validator;
use App\Exceptions\ValidationException;
use App\Exceptions\GSException;
use Response;
use Plupload;
use Image;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Hash;

class UsersController extends BaseController
{
    private $contractService;


    public function __construct(ContractService $contractService)
    {
        $this->middleware('verify-write-rights',
            ['only' => ['postEditProfile', 'postUploadProfileImage', 'postChangePassword']]);
        $this->contractService = $contractService;

    }

    /**
     * @OA\Post(
     *     path="/apigs/users/change-password",
     *     summary="Change user password",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @throws ValidationException
     */
    public function postChangePassword()
    {
        $validator = Validator::make(Request::all(), [
            'old_password' => 'required|string|max:255',
            'new_password' => 'required|string|max:255',
            'confirm_new_password' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            //throw new ValidationException('The username field value already exists.');
            throw new ValidationException($validator->errors()->first());
        }

        $userModel = User::find(Auth::user()->group_id);
        if (!$userModel) {
            return;
        }

        if (!Hash::check(Request::get('old_password'), $userModel->password)) {
            throw new ValidationException('Wrong password.');
        }

        if (Request::get('new_password') != Request::get('confirm_new_password')) {
            throw new ValidationException('Please confirm your new password correctly.');
        }

        $hashPass = Hash::make(Request::get('new_password'));
        $userModel->password = $hashPass;
        $userModel->save();

        DB::table('su_system_users')
            ->where('user_id', Auth::user()->group_id)
            ->update(['password' => Request::get('new_password')]);

        //Update Global user
        $globalUser = Auth::user()->globalUser();
        $globalUser->password = $hashPass;
        $globalUser->save();

        $this->revokeSessions();

        return Response::json(array('Response' => 'Your password was successfully changed.'));
    }

    /**
     * @OA\Post(
     *     path="/apigs/users/edit-profile",
     *     summary="Edit user profile",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @throws ValidationException
     */
    public function postEditProfile()
    {

        $validator = Validator::make(Request::all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|max:255',
            'phone' => 'string|max:255',
            'address' => 'string|max:255',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $userModel = User::find(Auth::user()->group_id);
        if (!$userModel) {
            return;
        }
        $userModel->name = Request::get('name');
        $userModel->email = Request::get('email');
        $userModel->phone = Request::get('phone');
        $userModel->address = Request::get('address');
        $userModel->save();

        return Response::json(array(
            'Response' => 'Success',
            'userId' => $userModel->id,
            'fullName' => $userModel->name
        ));
    }

    /**
     * @OA\Post(
     *     path="/apigs/users/upload-profile-image",
     *     summary="Upload user profile image",
     *     @OA\Response(
     *     response="200",
     *     description="JSON"
     * )
     * )
     *
     * @throws ValidationException
     */
    public function postUploadProfileImage()
    {
        $validator = Validator::make(Request::all(), [
            'file' => 'mimes:jpeg,gif,png',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $data = Plupload::receive('file', function ($uploadedFile) {

            $fileName = $this->generateRandomId() . "." . $uploadedFile->getClientOriginalExtension();

            //upload file
            $destinationPath = config('globals.PROFILE_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->group_id;
            $uploadedFile->move($destinationPath, $fileName);

            //resize
            $img = Image::make($destinationPath . DIRECTORY_SEPARATOR . $fileName);
            //Resize given Size to best fitting size of current size.
            $img->fit(64);
            $thumbFileName = "thumb_" . $fileName;
            $img->save($destinationPath . DIRECTORY_SEPARATOR . $thumbFileName);

            //remove orig file
            if (is_file($destinationPath . DIRECTORY_SEPARATOR . $fileName)) {
                unlink($destinationPath . DIRECTORY_SEPARATOR . $fileName);
            }

            $result = array('name' => $thumbFileName);

            return $result;
        });

        if (!$data['result']) {
            return Response::json(array('Response' => 'Not Success'));
        }

        $userModel = User::find(Auth::user()->group_id);
        if (!$userModel) {
            return;
        }

        //remove thumb file
        $destinationPath = config('globals.PROFILE_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->group_id;
        if (is_file($destinationPath . DIRECTORY_SEPARATOR . $userModel->profile_image)) {
            unlink($destinationPath . DIRECTORY_SEPARATOR . $userModel->profile_image);
        }

        $userModel->profile_image = $data['result']['name'];
        $userModel->save();

        return Response::json(array('Response' => 'Success', 'profile_image' => $userModel->profile_image));
    }

    /**
     * @OA\Get(
     *     path="/apigs/users/load-user",
     *     summary="Load user",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return mixed
     */
    public function getLoadUser()
    {

        /** @var User $userModel */
        $userModel = Auth::user();
        $userModel->serviceProvider = $userModel->globalUser()->serviceProvider->slug;
        $userModel->globalUserId = $userModel->globalUser()->id;

        return $userModel;
    }

    /**
     * @OA\Post(
     *     path="/apigs/users/farms/visibility",
     *     summary="Toggle visibility of farms",
     *     @OA\Response(
     *     response="200",
     *     description="String"
     * )
     * )
     *
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function farmVisibility(\Illuminate\Http\Request $request)
    {
        $farms = $request->get('farms');

        $validator = Validator::make(Request::all(), [
            'farms' => 'array|required',
            'farms.*.id' => 'required|integer',
            'farms.*.is_visible' => 'required|boolean'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        /** @var User $user */
        $user = Auth::user();
        foreach ($farms as $farm) {
            $user->farms()->updateExistingPivot($farm['id'], ['is_visible' => $farm['is_visible']]);
        }

        return new JsonResponse('Success');
    }

    /**
     * @OA\Get(
     *     path="/apigs/users/farms",
     *     summary="List farms for logged in user",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function farms()
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->farms()->get();
    }

    /**
     * OA\Get(
     *     path="/apigs/users/farms-by-organization/{organization?}",
     *     summary="List farms for given organization or last chosen organization of current logged in user",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization|null $organization
     *
     * @return mixed
     */
    public function getFarmsByOrganization(?Organization $organization = null)
    {
        /** @var User $user */
        $user = Auth::user();

        if (!$organization) {
            /** @var Organization $organization */
            $organization = $user->lastChosenOrganization;
        }

        $farms = $user->farmsByOrganization($organization);

        return $farms;
    }

    /**
     * @OA\Get(
     *     path="/apigs/users/abilities-and-roles",
     *     summary="Get User's abilities and roles",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return mixed
     */
    public function getAbilitiesAndRoles()
    {
        /** @var GlobalUser $globalUser */
        $globalUser = \Illuminate\Support\Facades\Auth::user()->globalUser();

        $data = [];
        $data['roles'] = $globalUser->getRoles();
        $data['abilities'] = $globalUser->getAbilities();
        $data['forbidden_abilities'] = $globalUser->getForbiddenAbilities();

        return $data;
    }

    public function getUsersByRole(\Illuminate\Http\Request $request)
    {
        $role = $request->get('role');
        $searchText = $request->get('searchText');

        return GlobalUser::getUsersByRole($role, $searchText);
    }

    /**
     * Revoke all sessions without current
     */
    private function revokeSessions()
    {
        /** @var GlobalUser $globalUser */
        $globalUser = \Illuminate\Support\Facades\Auth::user()->globalUser();
        /** @var Token[] $tokens */
        $tokens = $globalUser->tokens;
        $currentToken = $globalUser->token();

        foreach ($tokens as $token) {
            if ($token === $currentToken) {
                continue;
            }
            $token->revoke();
        }
    }

    /**
     * Generate Random Id
     * @return string
     */
    private function generateRandomId()
    {
        return base_convert(time() . '000', 10, 36) . substr(base_convert(mt_rand(1000000000,
                    mt_getrandmax()) . mt_rand(1000000000, mt_getrandmax()), 10, 36), 0, 12);
    }


    /**
     * @return JsonResponse
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function countOrganizationsContractsAndPlotsByUser()
    {
        $organizations = Auth::user()->organizations()->where('active', true)->whereNotNull('identity_number')->get()->toArray();
        $plotQuery = Plot::getPlotsByUser();
        $contractsCnt = $this->contractService->countContracts($organizations);

        $response = [
            'countOrganizations' => Auth::user()->organizations()->where('active', true)->whereNotNull('identity_number')->count(),
            'countPlots' => $plotQuery->count(),
            'countContracts' => $contractsCnt['countContracts']
        ];

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }
}
