<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Http\Controllers\APIGS;

use App;
use App\Classes\CMS\ContractService;
use App\Exceptions\ValidationException;
use App\Models\Country;
use App\Models\GlobalUser;
use App\Models\Organization;
use App\Models\Plot;
use App\Models\User;
use App\Services\Organization\OrganizationService;
use Auth;
use Config;
use DB;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Request as RequestFacade;

/**
 * Class OrganizationController
 * @package App\Http\Controllers\APIGS
 */
class OrganizationController extends App\Http\Controllers\BaseController
{
    private $organizationService;
    private $contractService;

    public function __construct(OrganizationService $organizationService, ContractService $contractService)
    {
        $this->organizationService = $organizationService;
        $this->contractService = $contractService;

    }

    /**
     * @OA\Get(
     *     path="/apigs/organizations",
     *     summary="Get organizations",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return Array
     */
    public function index()
    {
        $result =  Organization::where('active', true)->whereNotNull('identity_number')->where('service_provider_id',
            Auth::user()->globalUser()->serviceProvider->id)->paginate(RequestFacade::get('limit'));

        return [
            'total' => $result->total(),
            'rows' => $result->items()
        ];
    }

    /**
     * @OA\Get(
     *     path="/apigs/organizations/list",
     *     summary="List logged user's organizations",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @return Array
     */
    public function list(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'identityNumber' => 'string',
            'organizationManagerId' => 'integer',
            'serviceManagerId' => 'integer',
            'withPagination' => 'boolean',
            'page' => 'integer',
            'limit' => 'integer',
            'include' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $identityNumber = $request->get('identityNumber');
        $organizationManagerId = $request->get('organizationManagerId');
        $serviceManagerId = $request->get('serviceManagerId');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 5);
        $user = Auth::user();
        $include = $request->get('include');
        $withPagination = $request->get('withPagination', true);

        $organizations = $this->organizationService->getList($identityNumber, $organizationManagerId, $serviceManagerId, $user, $include, $withPagination, $page, $limit);

        return new JsonResponse($organizations, 200);
    }

    /**
     * @OA\Post(
     *     path="/apigs/organizations",
     *     summary="Create organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request)
    {
        $data = json_decode($request->get('data'), true);

        // Check if there is organization with this identity number
        $duplicateFields = ['identity_number'];
        $organizationIsDuplicated = $this->checkOrganizationDuplicatesByFields($data, $duplicateFields);

        if($organizationIsDuplicated) {
            return new JsonResponse('This identity number is already taken by another organization.', 409);
        }

        /** @var User $currentUser */
        $currentUser = \Illuminate\Support\Facades\Auth::user();
        $currentGlobalUser = $currentUser->globalUser();

        $organization = DB::transaction(function () use ($data, $currentUser, $currentGlobalUser) {
            $organization = new Organization();
            $organization->fill($data);
            $country = Country::find($currentGlobalUser->country);
            $organization->iso_alpha_2_code = $country->iso_alpha_2_code;
            $organization->service_provider_id = $currentGlobalUser->serviceProvider->id;
            $organization->save();

            $farms = $data['farms'];
            $farmsObj = [];
            if (empty($farms)) {
                $farms = [['name' => 'Main farm']];
            }
            foreach ($farms as $farm) {
                $farmObj = new App\Models\Farm();
                $farmObj->fill($farm);
                $farmObj->organization()->associate($organization);
                $farmObj->save();

                $farmsObj[] = $farmObj;
            }

            $contactPersons = $data['contact_persons'];
            if (!empty($contactPersons)) {
                foreach ($contactPersons as $contactPerson) {
                    $contactPersonObj = new Organization\ContactPerson();
                    $contactPersonObj->fill($contactPerson);
                    $contactPersonObj->organization()->associate($organization);
                    $contactPersonObj->save();
                }
            }

            $addresses = $data['addresses'];
            if (!empty($addresses)) {
                foreach ($addresses as $address) {
                    $addressObj = new Organization\Address();
                    $addressObj->fill($address);
                    $addressObj->organization()->associate($organization);
                    $addressObj->save();
                }
            }

            if (!empty($data['users_assigned'])) {
                $users = $data['users_assigned'];

                foreach ($users as $user) {
                    $userObj = null;
                    if (array_key_exists('id', $user)) {
                        $globalUserObj = App\Models\GlobalUser::find($user['id']);
                        /** @var App\Models\User $userObj */
                        $userObj = $globalUserObj ? User::find($globalUserObj->old_id) : null;
                    }
                    if (!$userObj) {
                        $userObj = new App\Models\User();
                        $userObj->username = $user['username'];
                        $userObj->password = Hash::make($user['password']);
                        $userObj->name = $user['name'];
                        $userObj->email = $user['email'];
                        $userObj->level = App\Models\User::LEVEL_CLIENT;
                        $userObj->save();
                        $userObj->group_id = $userObj->id;
                        $userObj->save();
                        foreach ($farmsObj as $item) {
                            $userObj->farms()->attach($item->id);
                        }

                        $globalUserObj = new App\Models\GlobalUser();
                        $globalUserObj->username = $userObj->username;
                        $globalUserObj->password = $userObj->password;
                        $globalUserObj->email = $userObj->email;
                        $globalUserObj->name = $userObj->name;
                        $globalUserObj->country = $currentGlobalUser->country;
                        $globalUserObj->old_id = $userObj->id;
                        $globalUserObj->old_group_id = $userObj->group_id;
                        $globalUserObj->parent()->associate($currentGlobalUser->id);
                        $globalUserObj->save();
                        $globalUserObj->group_id = $globalUserObj->id;
                        $globalUserObj->service_provider_id = $currentGlobalUser->serviceProvider->id;
                        $globalUserObj->save();
                        $globalUserObj->assign($user['role']['name']);

                        DB::table('su_system_users')->insert(
                            [
                                'user_id' => $userObj->id,
                                'username' => $userObj->username,
                                'password' => $user['password']
                            ]
                        );
                    }

                    $organization->users()->attach($userObj->id);

                }
                GlobalUser::fixTree();
            }
            return $organization;
        });

        return new JsonResponse($organization, 201);
    }

    /**
     * @OA\Get(
     *     path="/apigs/organizations/:organizationId",
     *     summary="Get organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization $organization
     * @return Organization
     */
    public function show(Organization $organization)
    {
        return $organization;
    }

    /**
     * @OA\Post(
     *     path="/apigs/organizations/update/:organizationId",
     *     summary="Update organization data",
     *     @OA\Response(
     *     response="200"
     * )
     * )
     *
     * @param Organization $organization
     * @param Request $request
     * @throws ValidationException
     */
    public function updateOrganization(Organization $organization, Request $request)
    {
        $requestData = $request->all();
        $validator = Validator::make($requestData, [
            'name' => 'required|string',
            'email' => 'sometimes|email',
            'phone' => 'string',
            'address' => 'string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        // Check if there is organization with this identity number and different id.
        $duplicateFields = ['identity_number', 'id'];
        $organizationIsDuplicated = $this->checkOrganizationDuplicatesByFields($requestData, $duplicateFields);

        if($organizationIsDuplicated) {
            return new JsonResponse('This identity number is already taken by another organization.', 409);
        }

        $organization->update($request->all());
        return $organization;
    }

    /**
     * @OA\Put(
     *     path="/apigs/organizations/:organizationId",
     *     summary="Update organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     * @param Organization $organization
     * @return Organization
     */
    public function update(Organization $organization, Request $request)
    {
        $data = json_decode($request->get('data'), true);

        // Check if there is organization with this identity number and different id.
        $duplicateFields = ['identity_number', 'id'];
        $organizationIsDuplicated = $this->checkOrganizationDuplicatesByFields($data, $duplicateFields);

        if($organizationIsDuplicated) {
            return new JsonResponse('This identity number is already taken by another organization.', 409);
        }

        $organization->update($data);
        return $organization;
    }

    /**
     * @OA\Delete(
     *     path="/apigs/organizations/:organizationId",
     *     summary="Delete organization",
     *     @OA\Response(
     *     response="204"
     * )
     * )
     *
     * @param Organization $organization
     * @return JsonResponse
     * @throws Exception
     */
    public function destroy(Organization $organization)
    {
        $organization->delete();
        return new JsonResponse(null, 204);
    }

    /**
     * @OA\Post(
     *     path="/apigs/organizations/choose/:organizationId",
     *     summary="Set last chosen organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization $organization
     * @return array
     */
    public function choose(Organization $organization)
    {
        $user = Auth::user();
        $country = $user->globalUser()->country()->first();

        /** @var ConfigParamValue[] $configParams */
        $country->configParams->loadMissing('configParam');
        $configParams = $country->configParams;
        foreach ($configParams as $each) {
            Config::set($each->configParam->domain . '.' . $each->configParam->name, $each->value);
        }

        App::useStoragePath(Config::get('globals.STORAGE_PATH'));

        Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));

        $user->lastChosenOrganization()->associate($organization);
        $user->save();

        $returnData = [];

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $returnData['available_demo_area'] = max(Config::get('globals.ALLOWED_DEMO_AREA') - (float)$user->ordered_area,
                0) * $areaCoef;

        $returnData['GEOSCAN_MAP_SERVER'] = Config::get('globals.GEOSCAN_MAP_SERVER');
        $returnData['WMS_SERVER'] = Config::get('globals.WMS_SERVER');
        $returnData['SERVER_MAP_PATH'] = Config::get('globals.SERVER_MAP_PATH');
        $returnData['IMAGES_SERVER'] = Config::get('globals.IMAGES_SERVER');
        $returnData['SERVER_PROFILE_IMG_PATH'] = Config::get('globals.SERVER_PROFILE_IMG_PATH');
        $returnData['API_METEO_ENDPOINT'] = Config::get('globals.API_METEO_ENDPOINT');
        $returnData['METEO_IMAGES'] = Config::get('globals.METEO_IMAGES');
        $returnData['PLOT_REPORTS'] = Config::get('globals.PLOT_REPORTS');
        $returnData['EPSG_PROJ'] = Config::get('globals.EPSG_PROJ');
        $returnData['PROJ_4JS_STRING'] = Config::get('globals.PROJ_4JS_STRING');
        $returnData['DEFAULT_EXTENT'] = Config::get('globals.DEFAULT_EXTENT');
        $returnData['ORGANIZATION_EXTENT'] = Plot::getExtent($organization->id, $user->id);
        $returnData['PRICES'] = [
            'index' => Config::get('globals.PRICE_PER_DKA_INDEX') * $areaCoef,
            'soil' => Config::get('globals.PRICE_PER_DKA_SOIL') * $areaCoef,
            'meteo' => Config::get('globals.PRICE_PER_DKA_METEO') * $areaCoef
        ];
        $returnData['AREA_COEF'] = $areaCoef;
        $returnData['AREA_UNIT'] = Config::get('globals.AREA_UNIT');
        $returnData['PRICE_UNIT'] = Config::get('globals.PRICE_UNIT');
        $returnData['DEFAULT_LANG'] = Config::get('globals.DEFAULT_LANG');
        $returnData['farms'] = $user->farmsByOrganization($organization);
        $returnData['machine'] = Config::get('globals.MACHINE');

        return $returnData;
    }

    /**
     * @OA\Get(
     *     path="/apigs/organizations/:organizationId/farms",
     *     summary="Get farms in organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization $organization
     * @return Collection
     */
    public function farms(Organization $organization)
    {
        return $organization->farms()->get();
    }

    /**
     * @OA\Get(
     *     path="/apigs/organizations/:organizationId/users",
     *     summary="Get users in organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param Organization $organization
     * @return Collection
     */
    public function users(Organization $organization)
    {
        $users = $organization->users()->get();

        $users->map(function (User $user) use ($organization) {
            $user->role = $user->globalUser()->role;
            $user->farms = $user->allFarmsByOrganization($organization);
        });

        return $users;
    }

    /**
     * @OA\Post(
     *     path="/apigs/organizations/:organizationId/add-farm",
     *     summary="Create farm and assign to organization",
     *     @OA\Response(
     *     response="201",
     *     description="Array"
     * )
     * )
     *
     * @param Request $request
     * @param Organization $organization
     * @return JsonResponse
     */
    public function addFarm(Request $request, Organization $organization)
    {
        $dataRequest = json_decode($request->get('data'), true);
        if (!$dataRequest) {
            $dataRequest = $request->all();
        }
        $farm = $organization->farms()->create($dataRequest);

        return new JsonResponse($farm, 201);
    }

    public function search(Request $request)
    {
        $query = $request->get('q');
        $limit = $request->get('limit', 10);

        $result = Organization::search($query, $limit, Auth::user()->id);
        return $result;
    }

    public function getNameByIdentityNumbers(Request $request)
    {
        $identityNumbers = $request->get('identity_numbers');
        $organizations = Organization::select('identity_number as identityNumber', 'name')->whereIn('identity_number',
            $identityNumbers)->where('service_provider_id', Auth::user()->globalUser()->serviceProvider->id)->get();

        return $organizations;
    }

    /**
     * @OA\Get(
     *     path="/apigs/organization/packages/{type?}",
     *     summary="List of packages by given filter for organization",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     *    )
     * )
     *
     * @param Request $request
     * @param string|null $type
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function listPackagesByOrganization(Request $request, string $type = null): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'filter' => 'sometimes|required|array'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }
        $queryParams = $request->all();

        if (!isset($queryParams['filter']['customer_identification'])) {
            $queryParams['filter']['customer_identification'] = json_encode([
                Auth::user()->lastChosenOrganization->identity_number
            ]);
        }

        if ($type) {
            $response = $this->contractService->getCmsDataForPackagesByContractIdAndFilter($type, $queryParams);
            return new JsonResponse($response, 200, ['Content-Type: application/json']);
        }

        $response = $this->contractService->getAllServiceOrSubscriptionPackagesByContractId(null, $queryParams);

        return new JsonResponse($response, 200, ['Content-Type: application/json']);
    }

    /**
     * This method checks if organization already exists by $fields
     * and values specified in $data
     *
     * @param Array $data
     * @param Array $fields
     *
     * @return Boolean
     */
    private function checkOrganizationDuplicatesByFields(array $data, array $fields) {
        $organizations = Organization::select('id');

        foreach($fields as $field) {
            if(isset($data[$field]) && $data[$field] != null) {
                if($field === 'id') {
                    $organizations->where($field, '<>', $data[$field]);
                    continue;
                }

                $organizations->where($field, '=', $data[$field]);
            }
        }
       
        $duplicates = $organizations->count();
        if($duplicates > 0) {
            return true;
        }

        return false;
    }
}
