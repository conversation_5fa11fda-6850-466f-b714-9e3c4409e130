<?php

namespace App\Http\Controllers\APIGS;

use App\Exceptions\ValidationException;
use App\Http\Controllers\BaseController;
use App\Models\Plot;
use Auth;
use Config;
use DB;
use Plupload;
use Request;
use Response;
use Validator;

class OrderPlotsController extends BaseController
{
    /**
     * @OA\Get(
     *     path="/apigs/order/:orderId/plots",
     *     summary="Get plots in order",
     *     @OA\Response(
     *     response="200",
     *     description="Array"
     * )
     * )
     *
     * @param $orderId
     * @return array
     * @throws ValidationException
     */
    public function getPlotsByOrderId($orderId)
    {

        $validator = Validator::make([
            'order_id' => $orderId,
            'sort' => Request::get('sort'),
            'limit' => Request::get('limit'),
            'page' => Request::get('page'),
            'name' => Request::get('name'),
            'farms' => Request::get('farms'),
            'crop_id' => Request::get('crop_id'),
            'area_from' => Request::get('area_from'),
            'area_to' => Request::get('area_to'),
            'order_type' => Request::get('order_type')
        ], [
            'order_id' => 'required|numeric',
            'sort' => 'required|string',
            'limit' => 'required|string',
            'page' => 'required|integer',
            'name' => 'nullable|string',
            'farms' => 'nullable|json',
            'crop_id' => 'nullable|integer',
            'area_from' => 'nullable|integer',
            'area_to' => 'nullable|integer',
            'order_type' => 'nullable|json'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $sort = Request::get('sort');
        $limit = Request::get('limit');

        $crop_name = Config::get('globals.CROP_NAME');

        $organizationId = Auth::user()->lastChosenOrganization->id;

        $q = Plot::join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
            ->join('su_satellite_plots_files as spf', 'spf.plot_id', '=', 'su_satellite_plots.gid')
            ->join('su_satellite_orders AS so', 'so.id', '=', 'sopr.order_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->leftJoin('su_satellite_layers_plots AS slp', function ($join) {
                $join->on('slp.plot_id', '=', 'su_satellite_plots.gid')
                    ->on('slp.type', '=', 'so.type');
            })
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'su_satellite_plots.gid')
                    ->on('spc.year', '=', 'so.year')
                    ->where('spc.is_primary', '=', true);
            })
            ->leftJoin('su_crop_codes AS c', 'c.id', '=', 'spc.crop_id')
            ->whereRaw("(CASE WHEN slp.type = 'soil' THEN slp.layer_name LIKE '%summarized%' ELSE TRUE END)")
            ->where('sopr.order_id', (int)$orderId)
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', $organizationId)
            ->where('spf.type', 'plot')
            ->groupBy('su_satellite_plots.gid', 'sopr.price', 'f.name', 'c.' . $crop_name . '', 'sopr.order_id',
                'so.type');

        if (Request::get('name') && trim(Request::get('name')) !== '') {
            $q->where('name', 'ILIKE', trim('%' . Request::get('name') . '%'));
        }
        if (Request::get('farms')) {
            $arrFarms = json_decode(Request::get('farms'), true);
            $q->whereIn('su_satellite_plots.farm_id', $arrFarms);
        }
        if (Request::get('crop_id')) {
            $q->where('spc.crop_id', Request::get('crop_id'));
        }
        if (Request::get('area_from')) {
            $q->where('su_satellite_plots.area', '>=', Request::get('area_from'));
        }
        if (Request::get('area_to')) {
            $q->where('su_satellite_plots.area', '<=', Request::get('area_to'));
        }
        if (Request::get('order_type')) {
            $q->whereIn('so.type', json_decode(Request::get('order_type')));
        }
        if ($sort) {
            $q->restOrderBy($sort, 'last');
        }

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $orderPlots = $q->paginate($limit, [
            DB::raw("MAX(slp.date) AS last_image_date"),
            DB::raw("(array_agg(slp.layer_name))[1] AS layer_name"),
            DB::raw("(array_agg(spf.web_path))[1] AS web_path"),
            'su_satellite_plots.gid',
            'sopr.price',
            'f.name as farm',
            DB::raw("round((su_satellite_plots.area*{$areaCoef})::numeric, 3) as area"),
            'so.type',
            'su_satellite_plots.name',
            'c.' . $crop_name . ' as culture',
            'sopr.order_id'
        ]);

        return [
            'total' => $orderPlots->total(),
            'rows' => $orderPlots->items()
        ];
    }
}
