<?php

namespace App\Http\Controllers\System;

use App\Exceptions\ValidationException;
use App\Http\Controllers\Controller;
use App\Models\LayerPlot;
use App\Models\LayerPlotFile;
use App\Models\Plot;
use App\Models\SoilPoints;
use App\Models\OrderPlotRel;
use App\Services\Plot\PlotService;
use App\Services\SoilPoints\SoilPointsService;
use Config;
use DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Validator;

class PlotsSoilController extends Controller
{
    private $plotService;
    private $soilPointsService;

    public function __construct(PlotService $plotService, SoilPointsService $soilPointsService)
    {
        $this->plotService = $plotService;
        $this->soilPointsService = $soilPointsService;
    }

    /**
     * @throws ValidationException
     */
    public function getGridPointsDataBySoprIds(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'soprIds' => 'required|array',
            'soprIds.*' => 'integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $soilPoints = SoilPoints::getGridPointsDataBySoprIds($request->get('soprIds'));

        return new JsonResponse($soilPoints, 200);
    }

    public function makeSoilMap(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'plot_uuid' => 'required|string',
            'country' => 'required|string',
            'service_provider' => 'required',
            'points' => 'required|array',
            'element_interpretations' => 'required|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $country = $request->get('country');
        $plotUuid = $request->get('plot_uuid');
        $arrPoints = $request->get('points');
        $serviceProvider = $request->get('service_provider');
        $elementInterpretations = json_encode($request->get('element_interpretations', '[]'));

        $plotGeoJson = $this->plotService->getPlotGeoJson($plotUuid);
        $pointsGeoJson = $this->soilPointsService->getPointsGeoJson($arrPoints);

        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');
        $shp2imgPath = Config::get('globals.WMS_MAP_PATH') . 'shp2img.map';
        $processedOrdersPath = Config::get('globals.PROCESSED_ORDERS_PATH');
        $defaultProj = Config::get('globals.DEFAULT_DB_CRS');
        $shp2imgRealPath = Config::get('globals.SHP2IMG_REAL_PATH');

        $command = "{$pythonPath} {$scriptsPath}generate_soil_plot.py {$country} '{$serviceProvider->slug}' '{$plotGeoJson}' '{$pointsGeoJson}' {$shp2imgPath} {$processedOrdersPath} -d {$defaultProj} -s {$shp2imgRealPath} -e '{$elementInterpretations}'";
        
        $output = [];
        exec($command . " 2>&1", $output);
        try {
            //checking if the output is a valid json.
            $decodedOutput = json_decode($output[0], true);
            if(is_null($decodedOutput)) {
                throw new \Exception('Error creating soil map: '. $output[0]);
            }

        } catch (Exception $e) {
            throw $e;
        }

        DB::beginTransaction();
        try {
            foreach ($decodedOutput as $layersPlot) {
                // Remove old data
                DB::connection($country)->table('su_satellite_layers_plots')
                ->where([
                    ['order_id', $layersPlot['order_id']],
                    ['plot_id', $layersPlot['plot_id']],
                    ['probe', $layersPlot['probe']],
                    ['type', 'soil'],
                    ['layer_name', $layersPlot['layer_name']]
                ])
                ->delete();

                /** @var LayerPlot $layerPlot */
                $layerPlot = new LayerPlot();
                $layerPlot->fill($layersPlot);
                $layerPlot->stats = $layersPlot['stats'];
                $layerPlot->save();

                $layerPlotFile = new LayerPlotFile();
                $layerPlotFile->fill($layersPlot);
                $layerPlotFile->type = 'PNG';
                $layerPlotFile->path = $layersPlot['png_path'];
                $layerPlotFile->web_path = $layersPlot['png_web_path'];
                $layerPlotFile->layerPlot()->associate($layerPlot);
                $layerPlotFile->save();

                $layerPlotTifFile = new LayerPlotFile();
                $layerPlotTifFile->path = $layersPlot['tif_path'];
                $layerPlotTifFile->web_path = $layersPlot['tif_web_path'];
                $layerPlotTifFile->type = 'TIFF';
                $layerPlotTifFile->layerPlot()->associate($layerPlot);
                $layerPlotTifFile->save();

                $orderPlotRel = OrderPlotRel::findByOrderPlotIds($layersPlot['order_id'], $layersPlot['plot_id']);
                $orderPlotRel->soil_sample_status = 'processed';
                $orderPlotRel->save();
            }

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }
}
