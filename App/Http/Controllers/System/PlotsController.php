<?php

namespace App\Http\Controllers\System;

use App\Exceptions\ValidationException;
use App\Http\Controllers\Controller;

use App\Models\Plot;
use Config;
use DB;
use Illuminate\Http\JsonResponse;
use Request;
use Validator;

class PlotsController extends Controller
{
    public function __construct()
    {
    }

    public function getPlotByUuid($plotUuId)
    {
        try {
            return Plot::select(['gid', 'area', 'name', 'meteo_location_id', 'station_id', 'farm_id'])->where('uuid', $plotUuId)->first();
        } catch (\Exception $e) {
            return [
                'error' => [
                    'code' => $e->getCode(),
                    'message' => $e->getMessage()
                ]
            ];
        }
    }

    public function getCrops($plotId)
    {
        $validator = Validator::make([
            'plotId' => $plotId,
            'year' => Request::get('year')
        ], [
            'plotId' => 'required|integer',
            'year' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $year = Request::get('year');
        $sort = Request::get('sort');
        $limit = Request::get('limit');

        return Plot::getPlotCrops($plotId, $year, $sort, $limit);
    }

}
