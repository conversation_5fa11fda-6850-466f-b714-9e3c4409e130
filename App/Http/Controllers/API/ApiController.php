<?php

namespace App\Http\Controllers\API;

use App\Classes\CMS\AnalysisService;
use App\Classes\CMS\ContractService;
use Response;
use Request;
use Auth;
use App;
use App\Models\API\ViewOrderedPlots;
use App\Models\API\Notifications;
use App\Models\API\Pins;
use App\Models\PinImage;
use App\Models\API\SoilSamples;
use App\Models\API\SoilSampleNumbers;
use App\Models\API\Orders;
use App\Models\API\ViewSoilOrderedPlots;
use App\Models\OrderPlotRel;
use App\Models\Order;
use App\Models\SoilPoints;
use App\Models\UserFavouritePlot;
use App\Models\Plot;
use App\Models\PinImagesFile;
use App\Events\GeneratePlotImgEvent;
use Illuminate\Support\Facades\Mail;
use App\Exceptions\ValidationException;
use App\Helpers\SoilSamplesLogger;
use App\Helpers\Helper;
use Carbon\Carbon;
use Config;
use Validator;
use DB;
use Image;
use Event;
use App\Services\Common\MailService;
use App\Services\SoilPoints\SoilPointsService;
use App\Services\System\SystemService;

class ApiController extends App\Http\Controllers\BaseController
{
    protected $soilSamplesLogger;
    private $mailService;
    private $analysisService;
    private  $contractService;
    private  $soilPointsService;
    private  $systemService;

    public function __construct(
        MailService $mailService,
        AnalysisService $analysisService,
        ContractService $contractService,
        SoilPointsService $soilPointsService,
        SystemService $systemService
    )
    {
        $this->soilSamplesLogger = new SoilSamplesLogger();
        $this->mailService = $mailService;
        $this->analysisService = $analysisService;
        $this->contractService = $contractService;
        $this->soilPointsService = $soilPointsService;
        $this->systemService = $systemService;
    }

    /**
     * GEOSCAN mobile only
     */
    public function getOrderedPlotsList()
    {
        $input = Request::all();
        $year = Request::get('year');

        $validator = Validator::make($input, [
            'year' => 'sometimes|required|integer'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->organizations->first()->id;

        if (Auth::user()->lastChosenOrganization) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $query = ViewOrderedPlots::where('user_id', '=', Auth::user()->id)->where('organization_id', '=', $organizationId);

        if ($year) {
            $query->where('year', $year);
        }

        $res = $query->get();
        $plotsList = [];

        foreach ($res as $plot) {
            $plotsList[] = [
                'year' => $plot['year'],
                'gid' => $plot['gid'],
                'name' => $plot['name'],
                'area' => $plot['area'],
                'culture' => $plot['culture'],
            ];
        }

        return $plotsList;
    }

    /**
     * GEOSCAN mobile only
     */
    public function getOrderedPlotsGeometry()
    {
        $input = Request::all();
        $year = Request::get('year');

        $validator = Validator::make($input, [
            'year' => 'sometimes|required|integer'
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->organizations->first()->id;

        if (Auth::user()->lastChosenOrganization) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $query = ViewOrderedPlots::where('user_id', '=', Auth::user()->id)->where('organization_id', '=', $organizationId);

        if ($year) {
            $query->where('year', $year);
        }

        $res = $query->get();

        $features = [];
        foreach ($res as $feature) {
            $feature->append('geom');

            $features[] = [
                'id' => $feature['gid'],
                'type' => 'Feature',
                'year' => $feature['year'],
                'geometry' => $feature['geom'],
                'properties' => [
                    'opr_id' => $feature['opr_id'],
                    'gid' => $feature['gid'],
                    'type' => $feature['type'],
                    'extent' => $feature['extent'],
                    'name' => $feature['name'],
                    'area' => $feature['area'],
                    'culture' => $feature['culture'],
                ],
            ];
        }

        $res = [
            'features' => $features,
            'type' => 'FeatureCollection'
        ];

        return $res;
    }

    /**
     * GEOSCAN mobile only
     */
    public function getOrderedPlot(Plot $plot)
    {
        $input = Request::all();
        $year = Request::get('year');

        $validator = Validator::make($input, [
            'year' => 'sometimes|required|integer',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $organizationId = Auth::user()->organizations->first()->id;

        if (Auth::user()->lastChosenOrganization) {
            $organizationId = Auth::user()->lastChosenOrganization->id;
        }

        $query = ViewOrderedPlots::where('user_id', '=', Auth::user()->id)
            ->where('organization_id', '=', $organizationId)
            ->where('gid', '=', $plot->gid);

        if ($year) {
            $query->where('year', $year);
        }

        $res = $query->get();

        $plots = [];
        foreach ($res as $plot) {
            $plot->append('images_data');
            $plot->append('opr_id');

            $plots[] = [
                'type' => $plot['type'],
                'data' => $plot
            ];
        }

        return $plots;
    }

    public function getAllOrderedPlotsImage($date)
    {
        $satelliteType = Request::get('satellite_type');
        $user = Auth::user();
        $lastChosenOrganizationId = Auth::user()->lastChosenOrganization->id;
        $defaultProjection = Config::get('globals.DEFAULT_DB_CRS');

        $plot_stats = Plot::getPlotsStatsByDate($lastChosenOrganizationId, $date, $satelliteType);
        $imageSize = Plot::getPlotsImageSizeByDate($lastChosenOrganizationId, $date, $satelliteType);

        $mapFile = 'index.map';
        $layer = 'sentinel_index_mobile';

        $png = $user->id . '_' . $date . '_' . $satelliteType . '.png';

        $extent = Helper::toOpenLayerFormat($imageSize->extent);
        $width = $imageSize->width;
        $height = $imageSize->height;

        $image = $this->createIndexPngViaMapServer($mapFile, $layer, $satelliteType, $date, $lastChosenOrganizationId, $png, $defaultProjection, $extent, $width, $height);

        return Response::json([
            'image' => base64_encode($image),
            'imageName' => $date . '__index__' . $satelliteType,
            'user_id' => $user->id,
            'server' => $user->globalUser()->country()->get()->first()->iso_alpha_2_code,
            'plot_index_values' => $plot_stats,
            'extent' => $extent,
        ]);
    }

    public function getAllOrderedPlotsImageSoil($date)
    {
        $oprId = Request::get('opr_id');
        $soilType = Request::get('soil_type');
        $userId = Auth::user()->id;

        $compound = Request::get('compound');
        $element = current($compound);
        $unit = end($compound);

        $query = DB::table('su_satellite_layers_plots as slp')
            ->selectRaw('DISTINCT slpf.path AS location')
            ->join('su_satellite_orders_plots_rel as sopr', 'sopr.plot_id', '=', 'slp.plot_id')
            ->join('su_satellite_plots as sp', function ($join) {
                $join->on('sp.gid', '=', 'slp.plot_id')
                    ->on('sopr.order_id', '=', 'slp.order_id');
            })
            ->join('su_satellite_orders as so', 'so.id', '=', 'sopr.order_id')
            ->join('su_satellite_layers_plots_files as slpf', function ($join) {
                $join->on('slpf.layer_plot_id', '=', 'slp.id')
                    ->on('slpf.type', '=', DB::raw("'TIFF'"));
            })
            ->join('su_farms_users as fu', 'fu.farm_id', '=', 'sp.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'fu.farm_id')
            ->where('fu.is_visible', true)
            ->where('fu.user_id', $userId)
            ->where(DB::RAW("slp.type::order_types_enum"), 'soil')
            ->where('so.status', '!=', 'canceled')
            ->where('sopr.id', '!=', $oprId)
            ->where('slp.element', $element)
            ->where('slp.unit', $unit)
            ->where('slp.stats_type', $soilType)
            ->where('sopr.soil_sample_status', 'processed')
            ->get()
            ->toArray();

        $mapFile = 'soil.map';
        $layer = 'soil_by_element_' . $element;

        $tmp = Config::get('globals.STORAGE_PATH') . DIRECTORY_SEPARATOR . 'tmp' . DIRECTORY_SEPARATOR . $userId;
        $inputFileList = 'source_paths.txt';

        $sourceFile = $tmp . DIRECTORY_SEPARATOR . $inputFileList;
        if (!file_exists(dirname($sourceFile))) {
            mkdir(dirname($sourceFile), 0777, true);
        }

        $sources = [];
        foreach ($query as $path) {
            $sources[] = $path->location . "\n";
        }
        file_put_contents($sourceFile, $sources);

        $vrt = $date . '_' . $soilType . '.vrt';
        $buildVrtCommand = 'cd ' . $tmp . ' && gdalbuildvrt -input_file_list ' . $inputFileList . ' ' . $vrt;
        exec($buildVrtCommand);

        $source = $tmp . DIRECTORY_SEPARATOR . $vrt;
        $png = $userId . '_' . $date . '_' . $element . '_' . $soilType . '.png';

        $extent = Helper::vrtToExtent($source);
        $size = Helper::vrtToSize($source);
        $width = $size['w'];
        $height = $size['h'];

        $image = $this->createSoilPngViaMapServer($mapFile, $layer, $source, $png, $extent, $width, $height);

        $cleanupCommand = 'rm ' . $sourceFile . ' && rm ' . $source;
        exec($cleanupCommand);

        return Response::json([
            'image' => base64_encode($image),
            'extent' => $extent,
        ]);
    }

    protected function createSoilPngViaMapServer($mapFile, $layer, $source, $fileName, $extent, $width, $height)
    {
        $map = Config::get('globals.SERVER_MAP_PATH') . $mapFile;
        $tmp = Config::get('globals.STORAGE_PATH') . DIRECTORY_SEPARATOR . 'tmp' . DIRECTORY_SEPARATOR . Auth::user()->id;
        $out = $tmp . DIRECTORY_SEPARATOR . $fileName;

        $createPngCommand = 'shp2img -m ' . $map . ' -l ' . $layer . ' -e ' . $extent . ' -s ' . $width . ' ' . $height . ' -d ' . $layer . ' ' . $source . ' -o ' . $out;

        if (!is_dir($tmp)) {
            mkdir($tmp, 755, true);
        }

        exec($createPngCommand);

        $contents = file_get_contents($out);
        unlink($out);

        return $contents;
    }

    protected function createIndexPngViaMapServer($mapFile, $layer, $satelliteType, $date, $organizationId, $fileName, $projection, $extent, $width, $height)
    {
        $map = Config::get('globals.SERVER_MAP_PATH') . $mapFile;
        $tmp = Config::get('globals.STORAGE_PATH') . DIRECTORY_SEPARATOR . 'tmp' . DIRECTORY_SEPARATOR . Auth::user()->id;
        $out = $tmp . DIRECTORY_SEPARATOR . $fileName;
        $mapservPath = Config::get("globals.MAPSERV_PATH");

        $createPngCommand = "{$mapservPath} -nh \"QUERY_STRING=FORMAT=image/png&TRANSPARENT=true&MAP={$map}&mode=map&LAYERS={$layer}&map.extent={$extent}&map.projection=EPSG:{$projection}&map.size=${width} ${height}&organization_id={$organizationId}&date={$date}&satellite_type={$satelliteType}\" > {$out}";

        if (!is_dir($tmp)) {
            mkdir($tmp, 755, true);
        }

        exec($createPngCommand);

        $contents = file_get_contents($out);
        unlink($out);

        return $contents;
    }

    public function getNotifications($userId, $date = null)
    {
        $query = Notifications::where('group_id', '=', Auth::user()->group_id);

        if ($date) {
            $query->where('date', '>=', date('Y-m-d H:i:s', $date));
        }

        $query->orderBy('date', 'ASC');
        $res = $query->get();

        return Response::json($res);
    }

    public function getPins($userId)
    {
        $groupId = Auth::user()->group_id;
        $withoutPinIds = Request::input('pinIds');

        $arrResult = Pins::getPinsWithImages($groupId, $withoutPinIds);

        return Response::json($arrResult);
    }

    public function createPins($userId)
    {
        $pins = Request::input('pins');

        if (!$pins) {
            return Response::json(array('error' => 'No pins to create'), 411);
        }

        $pinAttributes = array();
        foreach ($pins as $pin) {
            if (!isset($pin['images'])) {
                $pin['images'] = array();
            }

            if (!array_key_exists('id', $pin)) {
                $pin['id'] = Pins::generateRandomId();
            }

            $date = date('Y-m-d H:i:s', strtotime($pin['date']));

            $pinAttributes['title'] = $pin['title'];
            $pinAttributes['comment'] = $pin['comment'];
            $pinAttributes['images'] = json_encode($pin['images']);
            $pinAttributes['lon'] = $pin['lon'];
            $pinAttributes['lat'] = $pin['lat'];
            $pinAttributes['date'] = $date;

            $user = Auth::user();
            $farm = $user->farms->first();

            $pinAttributes['farm_id'] = $farm->id;

            if (array_key_exists('sopr_id', $pin)) {
                $pinAttributes['sopr_id'] = $pin['sopr_id'];
                $pinAttributes['type'] = 'soil_samples';
                $pinAttributes['farm_id'] = null;
            }

            Pins::unguard();
            Pins::updateOrCreate([
                'id' => $pin['id'],
                'group_id' => Auth::user()->group_id
            ], $pinAttributes
            );
            Pins::reguard();

            //Update Last Marker Date
            Plot::updateLastMarkerDate($user, $pin['lon'], $pin['lat'], $date);
        }

        return Response::json(array('Response' => 'Success'));
    }

    public function updatePins($userId)
    {
        $pins = Request::input('pins');

        foreach ($pins as $pin) {
            if (!isset($pin['images'])) {
                $pin['images'] = array();
            }

            //delete images
            PinImage::removeImages($pin['id']);

            Pins::unguard();
            Pins::updateOrCreate([
                'id' => $pin['id'],
                'group_id' => Auth::user()->group_id
            ], [
                    'title' => $pin['title'],
                    'comment' => $pin['comment'],
                    'images' => json_encode($pin['images']),
                    'lon' => $pin['lon'],
                    'lat' => $pin['lat'],
                    'date' => date('Y-m-d H:i:s', strtotime($pin['date']))
                ]
            );
            Pins::reguard();
        }

        return Response::json(array('Response' => 'Success'));
    }

    public function deletePins($userId)
    {
        $pinIds = Request::input('pinIds');

        if (!$pinIds || !count($pinIds)) {
            return;
        }

        Pins::where('group_id', '=', Auth::user()->group_id)
            ->whereIn('id', $pinIds)
            ->update(['isDeleted' => true]);

        //delete images    
        foreach ($pinIds as $key => $pinId) {
            PinImage::removeImages($pinId);

        }    

        return Response::json(array('Response' => 'Success'));
    }

    public function uploadImages($userId)
    {
        $file = Request::file('file');

        $groupId = Auth::user()->group_id;
        $pins = Pins::where('group_id', '=', $groupId)->sharedLock()->get();

        if (!$pins || !count($pins)) {
            return Response::json(array('error' => 'No pins to link images to'), 411);
        }

        // checking file is valid.
        if ($file->isValid()) {
            $fileName = $file->getClientOriginalName();

            $pinFound = false;
            $pinToLinkImagesTo = null;
            $pinImages = [];

            foreach ($pins as $pin) {
                $pinImages = json_decode($pin->getOriginal('images'), true);
                foreach ($pinImages as $key => $value) {
                    // We set the base path when we get the original image. When we get the thumb image we do nothing.
                    if ($value['name'] == $fileName) {
                        $pinToLinkImagesTo = $pin;
                        $pinImages[$key]['remoteImageSrcPath'] = Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->group_id . DIRECTORY_SEPARATOR;
                        $pinFound = true;
                        break;
                    }
                }

                if ($pinFound) {
                    break;
                }
            }

            if (!$pinToLinkImagesTo) {
                return Response::json(array('error' => 'No pin to link images to found'), 200);
            }

            $pinToLinkImagesTo->images = json_encode($pinImages);
            $pinToLinkImagesTo->save();

            $arrPinImagesFile = [];

            //create image record
            $image = new PinImage();
            $image->pin_id = $pinToLinkImagesTo->id;
            $image->image_name = $fileName;
            $image->save();

            // upload path
            $destinationWebPath = Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->group_id . DIRECTORY_SEPARATOR . 'pins';
            $destinationPath = Config::get('globals.PIN_BASE_PATH') . $destinationWebPath;
            $file->move($destinationPath, $fileName); // uploading file to given path

            //original path
            $originalPath = $destinationPath . DIRECTORY_SEPARATOR . $fileName;
            $originalWebPath = $destinationWebPath . DIRECTORY_SEPARATOR . $fileName;
            $arrPinImagesFile[] = PinImagesFile::arrInsertResult($image->id, 'original', $originalPath, $originalWebPath);

            //thumb path
            $thumbPath = $destinationPath . DIRECTORY_SEPARATOR . "thumb_" . $fileName;
            $thumbWebPath = $destinationWebPath . DIRECTORY_SEPARATOR . "thumb_" . $fileName;
            $arrPinImagesFile[] = PinImagesFile::arrInsertResult($image->id, 'thumb', $thumbPath, $thumbWebPath);

            //cover path
            $coverPath = $destinationPath . DIRECTORY_SEPARATOR . "cover_" . $fileName;
            $coverWebPath = $destinationWebPath . DIRECTORY_SEPARATOR . "cover_" . $fileName;
            $arrPinImagesFile[] = PinImagesFile::arrInsertResult($image->id, 'cover', $coverPath, $coverWebPath);

            //Resize images
            PinImagesFile::resizeImages($originalPath, $thumbPath, $coverPath);

            //insert in PinImagesFile
            PinImagesFile::insert($arrPinImagesFile);

            return Response::json(array('Response' => 'Success'));
        } else {
            return Response::json(array('error' => 'File is invalid'), 415);
        }
    }

    public function saveSoilSamplesData()
    {
        $userId = Auth::user()->id;
        $orders = Request::input('sampled_orders');
        $orders = json_decode($orders);

        return $this->updateSoilSamplesData($orders, $userId);
    }


    public function getSoilSamplesData()
    {
        $userId = Auth::user()->id;
        $orders_ids = Request::input('orders_ids');

        $orders_ids = Orders::where('status', '=', 'waiting_payment')
            ->where('type', '=', 'soil')
            ->select('id')
            ->orderBy('id', 'DESC')
            ->get();

        $query = ViewSoilOrderedPlots::whereIn('id', $orders_ids)
            ->where('sampler_id', '=', $userId)
            ->select('id', 'order_name', 'plots', 'order_area')
            ->orderBy('id', 'DESC')
            ->get();

        $samplePins = Pins::where('su_users_pins.type', '=', 'soil_samples')
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.id', '=', 'su_users_pins.sopr_id')
            ->join('su_satellite_orders AS so', 'so.id', '=', 'sopr.order_id')
            ->where('su_users_pins.isDeleted', false)
            ->where('sopr.is_soil_sample_completed', false)
            ->where('sopr.sampler_id', '=', $userId)
            ->get([
                'su_users_pins.id',
                'su_users_pins.title',
                DB::raw("COALESCE(su_users_pins.comment, '') AS comment"),
                'su_users_pins.lon',
                'su_users_pins.lat',
                'su_users_pins.group_id',
                'su_users_pins.sopr_id',
                DB::raw("(CASE WHEN su_users_pins.group_id = {$userId} then true else false end) AS is_my_pin")
            ]);

        $logContext = array(
            'user_id' => $userId,
            'response_orders' => $query,
            'sample_pins' => $samplePins
        );

        $this->soilSamplesLogger->addRecord("INFO", "getSoilSamplesData", $logContext);


        $arrSoilOrderedPlots = array_column($query->toArray(), 'plots');
        if(!$arrSoilOrderedPlots) {
            return Response::json(array('Response' => 'Success', 'orders' => $query, 'sample_pins' => $samplePins));
        }

        try {
            //Update states: (WF4 and WF6) - The plot is synced to the mobile app
            $arrSoilOrderedPlots = call_user_func_array('array_merge', $arrSoilOrderedPlots);

            $arrSoprIds = array_column($arrSoilOrderedPlots, 'sopr_id');
            $arrOrderUuids = OrderPlotRel::whereIn('id', $arrSoprIds)->select(DB::raw("DISTINCT order_uuid"))->get()->pluck('order_uuid')->toArray();

            $arrPlots = array_column($arrSoilOrderedPlots, 'plot_gid');
            $arrPlotUuids = Plot::whereIn('gid', $arrPlots)->select('uuid')->get()->pluck('uuid')->toArray();

            // return data to CMS
            $this->analysisService->updatePlotPointsState($arrPlotUuids);
            $arrData = ['plotUuids' => $arrPlotUuids, 'orderUuids' => $arrOrderUuids];
            $this->contractService->updateSubscriptionPackageFieldState('subscription', $arrData);

            return Response::json(array('Response' => 'Success', 'orders' => $query, 'sample_pins' => $samplePins));

        } catch (\Exception $e) {
            return Response::json(array('Response' => 'Success', 'orders' => $query, 'sample_pins' => $samplePins));
        }
    }

    public function uploadAgroBalanceImages()
    {
        $file = Request::file('file');
        $userId = Auth::user()->id;
        // checking file is valid.
        if ($file->isValid()) {
            $destinationPath = Config::get('globals.SOIL_SAMPLES_PATH') . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . Auth::user()->group_id; // upload path
            $fileName = $file->getClientOriginalName(); // renameing image
            $file->move($destinationPath, $fileName); // uploading file to given path
            $this->soilSamplesLogger->addRecord("INFO", "uploadAgroBalanceImages Success",
                array('user_id' => $userId, 'fileName' => $fileName));

            return Response::json(array('Response' => 'Success'));
        } else {
            $fileName = $file->getClientOriginalName();
            $this->soilSamplesLogger->addRecord("Error", "uploadAgroBalanceImages Error",
                array('user_id' => $userId, 'fileName' => $fileName));
            return Response::json(array('error' => 'Uploading faild'), 500);
        }
    }

    public function getIndexColors()
    {
        return Config::get('globals.INDEX_COLORS');
    }

    public function updateSoilSamplesData($orders, $userId = null)
    {
        $completePlots = array();
        $soilSamplesData = array();

        $this->soilSamplesLogger->addRecord("INFO", "saveSoilSamplesData",
            array('user_id' => $userId, 'orders_data' => $orders));

        DB::beginTransaction();

        try {

            foreach ($orders as $plot) {
                foreach ($plot->sample_data as $sample_key => &$sample) {
                    $completePlots[] = $plot->sopr_id;
                    $images = array();

                    $sampleRecordExists = false;

                    $pointExists = SoilSamples::where('gid', '=', $sample->gid)
                        ->where('sopr_id', '=', $plot->sopr_id)
                        ->exists();

                    if (!$pointExists) {
                        // if the point asociated with this sample is not found in the database (probably the points shape was loaded again )
                        // search for grid cells that intersect with the track and take the new point gid
                        $newSamplePointGID = SoilSamples::from('su_satellite_soil_points as ssp')->select('ssp.gid')
                            ->join('su_satellite_soil_grid AS ssg', 'ssg.sample_id', '=', 'ssp.sample_id')
                            ->where('ssg.sopr_id', $plot->sopr_id)
                            ->where('ssp.sopr_id', $plot->sopr_id)
                            ->where(DB::raw('st_intersects(ST_Transform(ST_GeomFromGeoJSON(\'' . json_encode($sample->track) . '\'), ' . Config::get("globals.DEFAULT_DB_CRS") . '), ssg.geom)'),
                                '=', true)
                            ->get();

                        if ($newSamplePointGID->first()) {
                            $sample->gid = $newSamplePointGID->first()->gid;
                        } else {
                            $this->soilSamplesLogger->addRecord("INFO", "saveSoilSamplesDataError",
                                array('user_id' => $userId, 'sample_data' => $sample));
                            throw new ValidationException('Error Saving sample data - no grid cell for the given track');
                        }
                    }

                    foreach ($sample->samples as $sampleNumberData) {
                        $images = array_merge($images, $sampleNumberData->images);

                        // if there is record for the given sample point in the database - it wont add another
                        $sampleRecordExists = SoilSampleNumbers::where('gid', '=', $sample->gid)
                            ->where('treatment_type', '=', $sampleNumberData->treatment_type)
                            ->exists();

                        if (!$sampleRecordExists) {
                            $soilSamplesData[] = array(
                                'gid' => $sample->gid,
                                'sample_number' => $sampleNumberData->sample_number,
                                'treatment_type' => $sampleNumberData->treatment_type
                            );
                        }
                    }

                    if (property_exists($sample, 'track') && !$sampleRecordExists) {
                        SoilSamples::where('gid', '=', $sample->gid)->update([
                                'track' => DB::raw('ST_Transform(ST_GeomFromGeoJSON(\'' . json_encode($sample->track) . '\'), ' . Config::get("globals.DEFAULT_DB_CRS") . ')'),
                                'images' => json_encode($images)
                            ]
                        );
                    }
                }
            }
            DB::table(SoilSampleNumbers::getTableName())->insert($soilSamplesData);
            OrderPlotRel::whereIn('id', $completePlots)->update([
                'is_soil_sample_completed' => true,
                'sync_date' => Carbon::now()
            ]);

            DB::commit();

            $barcodesData = $this->soilPointsService->getPointsAndBarcodes($soilSamplesData);
            // return data to CMS
            if($userId) {
                $this->analysisService->saveBarcodes($barcodesData);
            } else {
                $this->systemService->saveBarcodes($barcodesData);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }


        return Response::json(array('Response' => 'Success'));
    }

    public function saveFavouritePlot()
    {
        $userId = Auth::user()->group_id;

        $elgIdent = Request::input('elgIdent');
        $sowingDate = Request::input('sowingDate');
        $cropCodeId = Request::input('cropCodeId');

        if ($cropCodeId != null) {
            $cropCodeId = (int)$cropCodeId;
        }

        $response = new \stdClass();
        $response->code = 200;

        $favPlot = UserFavouritePlot::findByUserAndPlot($elgIdent, $userId);

        if (!$favPlot) {
            $favPlot = new UserFavouritePlot();
            $favPlot->plot_id = $elgIdent;
            $favPlot->group_id = $userId;
            $response->code = 200;
        }

        $favPlot->crop_code_id = $cropCodeId;
        $favPlot->sowing_date = $sowingDate;
        $favPlot->save();

        $response->data = $favPlot;

        $this->generateAllowablePlotPreviewImage(array(
            'elg_ident' => $elgIdent,
            'extent' => $favPlot->plot->extent()->get()->first()->extent
        ));

        return Response::json($response->data, $response->code);
    }

    public function deleteFavouritePlot()
    {
        $results = UserFavouritePlot::deleteFavouritePlot(Request::input('elgIdent'));
        return Response::json($results);
    }

    public function getFavouritePlots($elgIdent = '')
    {
        $query = UserFavouritePlot::getFavouritePlotsList(Auth::user()->group_id);

        if ($elgIdent) {
            $query = $query->where('su_users_favourite_plots.plot_id', $elgIdent);
        }
        $results = $elgIdent != '' ? $query->first() : $query->get()->toArray();

        return Response::json($results);
    }

    public function getFavouritePlot($elgIdent)
    {
        $result = UserFavouritePlot::getFavouritePlot($elgIdent, Auth::user()->group_id);

        if ($result) {
            $result = $result->toArray();
        }

        return Response::json($result);
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function sendEmailReport()
    {
        $validator = Validator::make(Request::all(), [
            'order_id' => 'required|integer',
            'sync_date' => 'required|date',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        $orderId = Request::input('order_id');
        $syncDate = Request::input('sync_date');

        $mail = $this->mailService->sendSoilSamplesEmailReport($orderId, $syncDate);

        return Response::json(array('Response' => $mail));
    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function sendEmailVerify()
    {
        $mailProperties = array(
            'subject' => trans('emailReport.samplingReport'),
            'fromMail' => '<EMAIL>',
            'fromName' => 'Agro Service',
            'toMail' => Request::input('toMail'),
        );

        $mailValidator = Validator::make($mailProperties, [
            'fromMail' => 'required|email',
            'toMail' => 'required|email',
        ]);
            
        if ($mailValidator->fails()) {
            throw new ValidationException($mailValidator->errors()->first());
        }

        $mail = Mail::send('emails.verify', [],
            function ($message) use($mailProperties) {
                $message->from($mailProperties['fromMail'], $mailProperties['fromName']);
                $message->to($mailProperties['toMail']);
                $message->subject($mailProperties['subject']);
        });

        if(Mail::failures()){
           throw new \Exception("Failed to send mail!");
        }

        return Response::json(array('Response' => $mail));
    }    



    private function generateAllowablePlotPreviewImage($item)
    {
        $mapFile = Config::get('globals.WMS_MAP_PATH') . 'plot_preview.map';
        $outImage = storage_path('allowable_plots' . DIRECTORY_SEPARATOR . $item['elg_ident'] . 'plot_preview.png');

        if (file_exists($outImage)) {
            return;
        }

        $width = 320;
        $height = 240;
        $layers = array(
            'plot_preview' => $parcelsQuery = "geom FROM (SELECT elg_ident, geom FROM layer_allowable_final WHERE elg_ident='" . $item['elg_ident'] . "') AS subquery USING UNIQUE elg_ident",
        );

        event(new GeneratePlotImgEvent(
            $mapFile,
            $layers,
            Helper::parseExtentToOpenLayer($item['extent'], true),
            $outImage,
            $width,
            $height
        ));
    }
}
