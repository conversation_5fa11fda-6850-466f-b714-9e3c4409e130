<?php

namespace App\Console\Commands;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Console\Command;
use DB;
use Config;
use Auth;
use App\Models\LayerPlot;
use App\Models\API\Notifications;
use App\Models\Order;
use App\Models\LayerPlotFile;
use File;

class ProcessExistingOrder extends ProcessOrder
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:process-existing-order 
                        {server : The server name} 
                        {--dates= : The dates to be processed - comma separated dates - example: --dates=2018-04-22,2018-04-23}
                        {--order_id= : The ID of order to be processed}
                        {--organization_id= : The ID of organization\'s orders to be processed}
                        {--no-notifications : Do not send notifications}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Processing the client order.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->serverName = strtoupper($this->argument('server'));
        $dates = array_filter(explode(",", $this->option('dates')));
        $this->orderId = (int)$this->option('order_id');
        $this->organizationId = (int)$this->option('organization_id');
        $this->noNotifications = $this->option('no-notifications');

        $this->loadCountrySpecificConfigs(strtoupper($this->serverName));

        $this->info("Getting tiles");

        $tiles = $this->getTiles($dates);

        if (count($tiles) === 0) {
            $this->info("No tiles found");
            return;
        }

        $this->MAP_FILE_PATH = Config::get('globals.WMS_MAP_PATH') . $this->serverName . DIRECTORY_SEPARATOR;

        $noties = [];
        for ($i = 0; $i < count($tiles); $i++) {

            $tile = $tiles[$i];

            $this->ORDER_QUEUE_PATH = Config::get('globals.ORDER_QUEUE_PATH') . $this->serverName . DIRECTORY_SEPARATOR . $tile->order_id . DIRECTORY_SEPARATOR;
            $this->PROCESSED_ORDERS_PATH = Config::get('globals.PROCESSED_ORDERS_PATH') . $this->serverName . DIRECTORY_SEPARATOR . DIRECTORY_SEPARATOR;

            $this->createSystemDirs();

            $sourceImg = $this->getSourceImg($tile, $tile->order_id);

            $shpFile = $this->execPlotShapeFile($tile->order_id, $tile->date);
            if (!$shpFile) {
                continue;
            }
            $clippedImg = $this->execGdalWarpCommand($sourceImg, $shpFile, $tile->type . '_' . $tile->date);

            $this->info("Calculating");

            if ($tile->type === 'sentinel' || $tile->type === 'landsat') {
                $sourceImg = $this->execGdalCalcNDVI($clippedImg);

                $sourceImgWater = "";
                if ($tile->date >= Config::get('globals.WATER_INDEX_DATE')) {
                    $sourceImgWater = $this->execGdalCalcNDWI($clippedImg);
                }
            } else {
                $sourceImg = $this->execGdalCalcRedEdge($clippedImg);
            }

            $plotsIds = explode(',', trim($tile->plots, '{}'));
            $plots = DB::connection($this->serverName)->table('su_satellite_plots as sp')
                ->select(
                    DB::raw('ST_Extent(sp.geom) AS extent'),
                    DB::raw('ST_Extent(ST_Transform(sp.geom, 900913)) AS extent_900913'),
                    DB::raw('ST_AsText(ST_Simplify(sp.geom, 2)) AS geom'),
                    DB::raw('(ST_XMax(ST_Extent(ST_Transform(sp.geom, 900913))) - ST_XMin(ST_Extent(ST_Transform(sp.geom, 900913)))) AS extent_width_900913'),
                    DB::raw('(ST_YMax(ST_Extent(ST_Transform(sp.geom, 900913))) - ST_YMin(ST_Extent(ST_Transform(sp.geom, 900913)))) AS extent_height_900913'),
                    'sp.gid'
                )
                ->whereIn('sp.gid', $plotsIds)
                ->groupBy('sp.gid')
                ->groupBy('sp.geom')
                ->get();

            $plotsCount = count($plots);

            $this->info("Making PNGs: Plots: {$plotsCount}");
            $layersPlots = [];
            for ($j = 0; $j < count($plots); $j++) {

                $plot = $plots[$j];

                if (!File::exists($sourceImg) || !$plot->geom) {
                    continue;
                }

                $cloudPercent = $this->getCloudsPercentage($plot->gid, $tile->sat_ids);
                $this->info('Cloud Percentage: ' . $cloudPercent);

                $hasWaterPounds = $this->getHasWaterPounds($tile->date, $plot->gid);
                $this->info('Has Water pounds: ' . $hasWaterPounds);

                //Make Plot Png and Make Plot Stats
                $layersPlots[] = $this->indexActions($plot, $tile->order_id, $tile, $sourceImg, $cloudPercent,
                    $hasWaterPounds);

                if ($sourceImgWater) {
                    $layersPlots[] = $this->indexWaterActions($plot, $tile->order_id, $tile, $sourceImgWater,
                        $cloudPercent, $hasWaterPounds);
                }
            }

            if (File::exists($sourceImg)) {

                $this->info("Moving");
                $arrMovedImg = $this->moveOrderToFinished($sourceImg, $sourceImgWater, $tile->order_id, $tile->date,
                    $tile->type);
            }

            try {
                $plotIds = array_map(function ($plot) {
                    return $plot['plot_id'];
                }, $layersPlots);

                if (count($plotIds)) {
                    $this->info("Deleting old layers plots: " . count($plotIds));
                    $this->setModelConn(new LayerPlot)
                        ->where('order_id', $tile->order_id)
                        ->whereIn('plot_id', $plotIds)
                        ->where('satellite_type', $tile->type)
                        ->where('date', $tile->date)
                        ->delete();
                }

                \Illuminate\Support\Facades\DB::beginTransaction();
                try {
                    foreach ($layersPlots as $layersPlot) {
                        /** @var LayerPlot $layerPlot */
                        $layerPlot = new LayerPlot();
                        $layerPlot->fill($layersPlot);
                        $layerPlot->stats = json_decode($layersPlot['stats']);
                        $layerPlot->save();

                        $layerPlotFile = new LayerPlotFile();
                        $layerPlotFile->fill($layersPlot);
                        $layerPlotFile->type = 'PNG';
                        $layerPlotFile->layerPlot()->associate($layerPlot);
                        $layerPlotFile->save();
                    }

                    \Illuminate\Support\Facades\DB::commit();
                } catch (\Exception $exception) {
                    \Illuminate\Support\Facades\DB::rollBack();
                    throw $exception;
                }

                $satIds = array_values(
                    array_filter(
                        array_unique(
                            explode(',', trim($tile->sat_ids, '{}')))));

                $ordersTiles = [];
                for ($k = 0; $k < count($satIds); $k++) {
                    $sId = $satIds[$k];

                    $ordersTiles[] = [
                        'order_id' => $tile->order_id,
                        'tile_id' => $sId,
                        'tile_date' => $tile->date
                    ];
                }

                if (count($satIds)) {
                    $this->info("Deleting old orders tiles: " . count($satIds));
                    $this->setDBConn()->table('su_satellite_orderes_tiles')
                        ->whereIn('tile_id', $satIds)
                        ->where('tile_date', $tile->date)
                        ->where('order_id', $tile->order_id)
                        ->delete();
                }

                $this->info("Inserting the new orders tiles: " . count($satIds));
                $this->setDBConn()->table('su_satellite_orderes_tiles')->insert($ordersTiles);

                $order = $this->setModelConn(new Order)
                    ->find($tile->order_id);

                $order->status = 'processed';
                $order->save();
            } catch (\Exception $e) {
                $this->error("Error in try2");
                echo $e;
            }

            $this->removeOrderFromQueue();

            try {
                /** @var Organization $organization */
                $organization = Organization::find($tile->organization_id);
                /** @var User[] $users */
                $users = $organization->users;

                foreach ($users as $user) {
                    if (!isset($noties[$user->id]['data'])) {
                        $noties[$user->id]['data'] = ['plots' => []];
                    }
                    $noties[$user->id]['data']['plots'] = array_merge($noties[$user->id]['data']['plots'],
                        explode(',', trim($tile->plots, '{}')));
                    $noties[$user->id]['data']['year'] = $order->year;
                    $noties[$user->id]['data']['type'] = $order->type;
                }
            } catch (\Exception $e) {
                $this->error("Error preparing notifications array");
                echo $e;
            }

            $this->info("DONE");
        }

        try {
            $this->saveNotifications($noties);
        } catch (\Exception $e) {
            $this->error("Error saving notifications");
            echo $e;
        }
    }

    public function getTiles($dates)
    {
        $tilesQuery = DB::connection($this->serverName)->table('su_satellite AS s')
            ->select(
                DB::raw("array_agg(DISTINCT s.id) AS sat_ids"),
                's.type',
                DB::raw('array_to_string("array_agg"(s.path), \',\') AS sat_image_paths'),
                DB::raw("s.date::date AS date"), DB::raw("MAX(s.date) AS date_time"), "so.organization_id",
                "so.id AS order_id",
                DB::raw("array_agg(DISTINCT sp.gid ORDER BY sp.gid ASC) AS plots")
            )
            ->join('su_satellite_plots AS sp',
                DB::raw('ST_Intersects(ST_Transform(s.extent, ' . Config::get('globals.DEFAULT_DB_CRS') . '), sp.geom)'),
                '=', DB::raw('true'))
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'sp.gid')
            ->join('su_satellite_orders AS so', 'so.id', '=', 'sopr.order_id')
            ->leftJoin('su_satellite_layers_plots AS slp', function ($join) {
                $join->on('slp.plot_id', '=', 'sp.gid')
                    ->on('slp.order_id', '=', 'so.id')
                    ->on('slp.date', '=', DB::raw("s.date::date"))
                    ->on('slp.satellite_type', '=', DB::raw("s.type"));
            })
            ->whereNull('slp.id')
            ->where('so.type', 'index')
            ->where('s.type', '=', 'sentinel')
            ->whereIn('so.status', ['processed', 'no_tile'])
            ->where(DB::raw("s.date::date"), '>=', DB::raw('so.from_date'))
            ->where(DB::raw("s.date::date"), '<=', DB::raw('so.to_date'))
            ->groupBy("so.id")
            ->groupBy(DB::raw("s.date::date, s.type"))
            ->orderBy(DB::raw("s.date::date"));

        if ($this->orderId) {
            $tilesQuery->where('so.id', $this->orderId);
        }

        if ($this->organizationId) {
            $tilesQuery->where('so.organization_id', $this->organizationId);
        }

        if (count($dates)) {
            $tilesQuery->whereIn(DB::raw("s.date::date"), $dates);
        }

        return $tilesQuery->get();
    }

    /**
     * Save notifications for users with new images.
     * @param  array $noties Notifications to save.
     *
     */
    protected function saveNotifications($noties)
    {
        if ($this->option('no-notifications')) {
            return;
        }

        foreach ($noties as $group_id => $noty) {
            $notyData = new Notifications();
            $notyData->setConnection($this->serverName);
            $notyData->group_id = $group_id;
            $notyData->title = trans('notifications.new_tiles', [], Config::get('app.locale'));
            $notyData->message = trans('notifications.new_tiles_details', [], Config::get('app.locale'));
            $notyData->data = json_encode($noty['data']);
            $notyData->type = 'new_tiles';
            $notyData->is_pushed = false;
            $notyData->save();
        }
    }

    public function deleteDatesInLayerPlots($tiles)
    {

        $arrDates = array_column($tiles, 'date');

        $arrResult = array_values(array_unique($arrDates));

        LayerPlot::whereIn('date', $arrResult)->delete();
    }

}
