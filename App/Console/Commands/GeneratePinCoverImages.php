<?php

namespace App\Console\Commands;

use Config;
use Illuminate\Console\Command;
use DB;
use Image;

class GeneratePinCoverImages extends AbstractBaseCommand {
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:generate-pin-cover-images
                            {server : The server name}
                            {--group_id= : The ID of user\'s pin images to be generate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate pin cover images for every ор definite user.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $server = $this->argument('server');

        $this->loadCountrySpecificConfigs(strtoupper($server));

        $this->userId = (int) $this->option('group_id');

        $query = DB::table('su_users_pins');

        if($this->userId) {
            $query->where('group_id', $this->userId);
        }

        $pins = $query->get();

        if (!$pins) {
            return;
        }

        foreach ($pins as $pin) {

            $group_id = $pin->group_id;

            // upload path
            $destinationPath = config('globals.PROCESSED_ORDERS_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . $group_id . DIRECTORY_SEPARATOR . 'pins';

            $images = json_decode($pin->images, true);
            foreach ($images as $image) {
                $fileName = $image['name'];
                $coverImageAbsPath = $destinationPath . DIRECTORY_SEPARATOR . "cover_" . $fileName;
                $pinImageAbsPath = $destinationPath . DIRECTORY_SEPARATOR . $fileName;

                if (file_exists($coverImageAbsPath)) {
                    $this->info('User with GROUP ID: ' . $group_id . ' had already cover image. Filename -> ' . $fileName);
                    continue;
                }
                if (!file_exists($pinImageAbsPath)) {
                    $this->info('User with GROUP ID: ' . $group_id . ' hadn\'t pin image(don\'t exist) which is saved in DB. Filename -> ' . $fileName);
                    continue;
                }

                // resize cover
                $imgCover = Image::make($pinImageAbsPath);

                // resize the image to a width of 360 and constrain aspect ratio (auto height)
                $imgCover->resize(320, null, function ($constraint) {
                    $constraint->aspectRatio();
                });

                // save resized image
                $imgCover->save($coverImageAbsPath);
            }
        }

        $this->info('Successfully completed!');
    }
}
