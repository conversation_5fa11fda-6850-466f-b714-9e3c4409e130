<?php

namespace App\Console\Commands;

use Config;
use Illuminate\Console\Command;
use Illuminate\Foundation\Inspiring;
use DB;


class DeleteReportFiles extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:report-files
                            {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Report Files';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->serverName = $this->argument('server');

        $this->loadCountrySpecificConfigs(strtoupper($this->serverName));

        $this->removeDirectory(config('reports.REPORT_PATH') . config('globals.MACHINE'));
    }

    /**
     * A simple method for recursively deleting a directory and all its content
     */
    private function removeDirectory($path) {
        $files = glob($path . '/*');
        foreach ($files as $file) {
            is_dir($file) ? $this->removeDirectory($file) : unlink($file);
        }

        if(is_dir($path)) {
            rmdir($path);
        }
        
        return;
    }
}
