<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Config;
use App\Models\User;
use App\Models\Layer;
use App\Models\WorkLayer;
use App\Models\OrderPlot;
use App\Models\LayerPlot;
use DB;
use Event;
use App\Events\GenerateWorkLayerMapFileEvent;
use App\Events\GenerateIndexLayersMapFileEvent;
use App\Events\GenerateUserMapFileEvent;
use App\Events\GenerateAllPlotsLayerMapFileEvent;

class GenerateMapFiles extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:generate-map-files 
                            {server : The server name}
                            {userId? : The ID of the user}
                            {--run=all : Which map files to generate (all|LayerAllPlotsMapFile)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generates user map files.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $userId = $this->argument('userId');

        $server = $this->argument('server');

        $this->loadCountrySpecificConfigs(strtoupper($server));

        $run = $this->option('run');
        
        if($userId) {
            $users = User::where('id', $userId)->get();
        }
        else {
            $users = User::get();
        }

        switch ($run) {
            case "all":
                $users->each(function ($user) {
                    event(new GenerateUserMapFileEvent());
                    event(new GenerateWorkLayerMapFileEvent());
                    event(new GenerateAllPlotsLayerMapFileEvent());
                });
                break;
            case "LayerAllPlotsMapFile":
                event(new GenerateAllPlotsLayerMapFileEvent());
                break;
        }    

        
    }
}
