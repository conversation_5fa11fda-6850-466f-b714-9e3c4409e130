<?php

namespace App\Console\Commands;

use App\Events\GenerateStaticMapFilesEvent;
use Illuminate\Console\Command;
use Config;
use View;
use Event;

class GenerateStaticMapFiles extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:generate-static-map-files {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generates all static map files.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $server = $this->argument('server');

        $this->loadCountrySpecificConfigs(strtoupper($server));

        event(new GenerateStaticMapFilesEvent());
    }
}
