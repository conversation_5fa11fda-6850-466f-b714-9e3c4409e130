<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Foundation\Inspiring;

use App\Models\File;
use DB;


class DropForgottenTmpTables extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'drop:forgotten-tmp-tables
                            {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Drop forgotten tmp tables';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->serverName = $this->argument('server');

        $this->loadCountrySpecificConfigs(strtoupper($this->serverName));

        $resultTmpTables = DB::select("SELECT C .relname AS table_name
                                FROM
                                    pg_catalog.pg_class C
                                JOIN pg_namespace n ON (
                                    C .relnamespace = n.oid
                                    AND n.nspname NOT IN (
                                        'information_schema',
                                        'pg_catalog'
                                    )
                                    AND C .relkind = 'r'
                                )
                                WHERE C .relname ilike '%tmp_%'");

        if (count($resultTmpTables))
        {
            foreach ($resultTmpTables as $key => $value) 
            {    
                $pieces = explode("_", $value->table_name);
                $fileId = $pieces[count($pieces) - 1];
                
                if (!is_numeric($fileId))
                {
                    continue;
                }

                $file = File::select('id')
                            ->where('id','=', $fileId)
                            ->where('date_uploaded','<', 'now()')
                            ->first();

                if (isset($file))
                {
                    //Drop tmp_satellite table    
                    $sql = "DROP TABLE IF EXISTS " . $value->table_name;
                    DB::statement($sql);
                }            
            }
        }
    }
}
