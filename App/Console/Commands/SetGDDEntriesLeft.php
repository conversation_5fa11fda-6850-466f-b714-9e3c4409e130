<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use \App\Models\User;
use \App\Models\UserStation;
use DB;

class SetGDDEntriesLeft extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meteo:set-gdd-entries-left {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sets GDD entries left';

    const VIRTUAL_METEOSTATIONS = 10;
    const REAL_METEOSTATIONS = 25;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $server = $this->argument('server');

        $this->loadCountrySpecificConfigs(strtoupper($server));

        $stations = UserStation::where('type', '!=', 'Virtual')->get();
        $this->info("Processing...");       

        $users = $stations->pluck('user_id')->all();
    
        // updates the gdd entries for the real meteostations
        User::whereIn('id', $users)
        ->update([
            'gdd_entries_left' => SetGDDEntriesLeft::REAL_METEOSTATIONS,
        ]);

        // updates the gdd entries for the virtual meteostations
        User::whereNotIn('id', $users)
        ->update([
            'gdd_entries_left' => SetGDDEntriesLeft::VIRTUAL_METEOSTATIONS,
        ]);

        $this->info("Done.");       
    }


}
