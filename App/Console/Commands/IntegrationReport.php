<?php

namespace App\Console\Commands;

use App\Services\Wialon\ReportService;
use Config;
use File;
use DB;
use Artisan;

class IntegrationReport extends AbstractBaseCommand
{
    private $reportService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'integration:report 
                                {server : The server name}
                                {--report_id= : id from su_integrations_reports}
                                {--params= : json value from column params in su_integration_reports_types for the report request}
                                {--url= : url value from column url in su_integration_reports_types for the report request}
                                {--token= : token value from token column in su_integration needed for the report request}
                                ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Integration Reports - get report data from Wialon api and insert the data in tmp table. Also insert log data in log table - farm_track_reports_log';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(ReportService $reportService)
    {
        parent::__construct();
        $this->reportService = $reportService;
    }

    /**
     * Execute the console command.
     *
     * @throws \Exception
     */
    public function handle()
    {
        $serverName = $this->argument('server');
        $reportId = $this->option('report_id');
        $params = $this->option('params');
        $url = $this->option('url');
        $token = $this->option('token');

        $this->loadCountrySpecificConfigs(strtoupper($serverName));

        $arrParams = json_decode($params, true);
        $this->reportService->integrationReport($url, $arrParams, $reportId, $token);
    }

}
