<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 30/04/2018
 * Time: 14:04
 */

namespace App\Console\Commands;


use App\Models\GlobalUser;
use Illuminate\Console\Command;

class FixUserTree extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:fix-tree';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix user tree.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        GlobalUser::fixTree();
    }
}