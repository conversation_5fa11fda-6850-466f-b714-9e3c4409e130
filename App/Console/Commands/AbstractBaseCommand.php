<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 02/05/2018
 * Time: 12:57
 */

namespace App\Console\Commands;


use App\Models\ConfigParamValue;
use App\Models\Country;
use Illuminate\Console\Command;
use Config;
use Illuminate\Database\Eloquent\Model;
use App;

class AbstractBaseCommand extends Command
{
    protected function loadCountrySpecificConfigs($countryIsoAlpha2Code)
    {
        $country = Country::where('iso_alpha_2_code', strtoupper($countryIsoAlpha2Code))->first();

        $this->loadConfigParams($country);
        $this->setDBConnection($country);
    }

    /**
     * @param null $connection
     * @return \Doctrine\ORM\EntityManager
     */
    protected function getEntityManager($connection = null)
    {
        if (is_null($connection)) {
            $country = Config::get('globals.MACHINE');
            $connection = strtoupper($country);
        }
        $managerRegistry = app('registry');
        return $managerRegistry->getManager($connection);
    }

    private function loadConfigParams($country)
    {
        /** @var ConfigParamValue[] $configParams */
        $country->configParams->loadMissing('configParam');
        $configParams = $country->configParams;
        foreach ($configParams as $each) {
            Config::set($each->configParam->domain. '.' . $each->configParam->name, $each->value);
        }

        App::useStoragePath(Config::get('globals.STORAGE_PATH'));
    }

    private function setDBConnection($country)
    {
        Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));
    }

}
