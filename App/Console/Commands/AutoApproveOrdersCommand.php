<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Console\Commands;


use App\Models\Country;
use App\Models\Order;

class AutoApproveOrdersCommand extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:auto-approve
                                {type : Order type}
                                {--server : Server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Auto approve of orders.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $ordersType = $this->argument('type');
        $server = $this->option('server');

        $countriesQuery = Country::where('active', true);
        if ($server) {
            $countriesQuery->where('iso_alpha_2_code', strtoupper($server));
        }

        /** @var Country[] $countries */
        $countries = $countriesQuery->get();
        foreach ($countries as $country) {
            $this->loadCountrySpecificConfigs($country->iso_alpha_2_code);

            /** @var Order[] $ordersToApprove */
            $ordersToApprove = Order::where('type', $ordersType)->get();
            foreach ($ordersToApprove as $order) {
                $order->status = 'paid';
                if ($order->type == 'vra') {
                    $order->status = 'processed';
                }

                $order->save();
            }
        }


    }
}
