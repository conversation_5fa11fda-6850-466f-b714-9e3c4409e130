<?php

namespace App\Console\Commands;

use App\Services\Plot\PlotService;
use App\Services\System\SystemService;
use Config;
use DB;


class GenerateSoilMap extends AbstractBaseCommand
{
    private $plotService;
    private $systemService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:soil-map
                            {server : The server name}
                            {--plot_uuid= : The UUID of the plot}}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generates soil map based on the plot uuid.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(PlotService $plotService, SystemService $systemService)
    {
        parent::__construct();
        $this->plotService = $plotService;
        $this->systemService = $systemService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $server = $this->argument('server');
        $plotUuid = $this->option('plot_uuid');
        $this->loadCountrySpecificConfigs(strtoupper($server));

        $data = ['plot_uuid' => $plotUuid, 'country' => $server];
        $this->systemService->generateSoilMap($data);

        $this->info("Done");
    }
}
