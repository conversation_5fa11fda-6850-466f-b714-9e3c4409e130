<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use DB;
use Config;
use App\Models\Order;
use App\Models\Plot;
use App\Models\User;
use App\Models\Role;

class ProcessMeteoOrder extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meteo:process-meteo-order
                        {server : The server name}
                        {order_id : The ID of order to be processed}
                        {--no-notifications : Do not send notifications}
                        {--reprocess : Reprocess order}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Processing the client meteo order.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->serverName = $this->argument('server');
        $this->orderId = (int) $this->argument('order_id');

        $this->loadCountrySpecificConfigs(strtoupper($this->serverName));

        $plotsQuery = $this->setModelConn(new Order)
            ->select('su_satellite_orders.id', 'su_satellite_orders.year', 'su_satellite_orders_plots_rel.plot_id')
            ->join('su_satellite_orders_plots_rel', 'su_satellite_orders.id', '=', 'su_satellite_orders_plots_rel.order_id')
            ->where('su_satellite_orders.type', 'meteo');

        if(!$this->option('reprocess')) {
            $plotsQuery->where('su_satellite_orders.status', 'paid');
        }

        if($this->orderId) {
            $plotsQuery->where('su_satellite_orders.id', $this->orderId);
        }

        $plotsResult = $plotsQuery->get();
        
        if (!count($plotsResult)) {
            return;
        }

        $order = $this->setModelConn(new Order)->find($this->orderId);
        $order->status = 'processing';
        $order->save();
        $this->info("Processing Order " . $order->id);

        //Set Meteo Locations for the Plots
        for ($i = 0; $i < count($plotsResult); $i++) {

            $meteoLocation = $this->getMeteoLocation($plotsResult[$i]->plot_id);

            if(isset($meteoLocation['gid']) && $meteoLocation['gid']){

                $plot = $this->setModelConn(new Plot)->find($plotsResult[$i]->plot_id);

                $plot->meteo_location_id = $meteoLocation['gid'];
                $plot->save();
            }
        }

        $order->status = 'processed';
        $order->save();

        $this->info("DONE");
    }

    private function getMeteoLocation($gid) {

        $result = Plot::select([
            DB::raw('layer_meteo_locations.gid'),
            DB::raw('ST_X(ST_Transform (ST_Centroid(layer_meteo_locations.geom), 4326)) AS longitude'),
            DB::raw('ST_Y(ST_Transform (ST_Centroid(layer_meteo_locations.geom), 4326)) AS latitude'),
            DB::raw('ST_Distance(layer_meteo_locations.geom, su_satellite_plots.geom) as distance')
        ])
        ->join('layer_meteo_locations', function ($join) {
            $join->where(DB::raw('ST_Distance(layer_meteo_locations.geom, su_satellite_plots.geom)'), '<=', config('meteo.METEO_MAX_DIST'));
        })
        ->where('su_satellite_plots.gid', $gid)
        ->orderBy('distance', 'asc')
        ->first();

        return $result;
    }

    protected function setModelConn($model) {
        return $model::on($this->serverName);
    }
}
