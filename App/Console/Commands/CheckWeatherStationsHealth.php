<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\StationsGS\Station;
use Illuminate\Support\Facades\Mail;
use App\Exceptions\ValidationException;
use Validator;
use Config;
use Exception;

class CheckWeatherStationsHealth extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stations:check-weather-stations-health';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'It checks the last communication with the station and sends an email with the damaged stations (stations with last communication time > DAMAGED_STATION_HOURS).';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $mailProperties = array(
            'subject' => 'GeoSCAN Неработещи станции',
            'fromMail' => env('MAIL_USERNAME'),
            'fromName' => 'No-Reply GeoSCAN',
            'toMail' => env('MAIL_OFFICE')
        );

        $mailValidator = Validator::make($mailProperties, [
            'fromMail' => 'required|email',
            'toMail' => 'required|email',
            ]);
            
        if ($mailValidator->fails()) {
            throw new ValidationException($mailValidator->errors()->first());
        }
        
        $damagedStationHours = Config::get('globals.DAMAGED_STATION_HOURS');
        $damagedStations = Station::getStationsByLastCommunication($damagedStationHours)->get();

        if(count($damagedStations) == 0){
            echo "There is no damaged stations!\n";
            return;
        }

        $mail = Mail::send('emails.stations', ['title' => 'Неработещи станции', 'stations' => $damagedStations],
            function ($message) use($mailProperties) {
                $message->from($mailProperties['fromMail'], $mailProperties['fromName']);
                $message->to($mailProperties['toMail']);
                $message->subject($mailProperties['subject']);
        });

       if(Mail::failures()){
           throw new \Exception("Failed to send mail!");
       }
    }

}
