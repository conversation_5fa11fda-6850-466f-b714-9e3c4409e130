<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Console\Commands;

use App\Models\ApiEndpoint;
use Doctrine\Common\Annotations\AnnotationReader;
use OpenApi\Annotations\AbstractAnnotation;
use Symfony\Component\Finder\Finder;
use hanneskod\classtools\Iterator\ClassIterator;

class UpdateApiEndpointsList extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'api:update-endpoints-list';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update list of api endpoints.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $finder = new Finder();
        $iter = new ClassIterator($finder->in(app_path() . '/Http/Controllers'));
        $arr = [];
        $reader = new AnnotationReader();
        foreach ($iter->getClassMap() as $className => $splFileInfo) {
            $class = new \ReflectionClass($className);

            $methods = $class->getMethods(\ReflectionMethod::IS_PUBLIC);
            $arr[$className] = [];
            foreach ($methods as $method) {
                if ($method->class == $class->getName() && !str_contains($method->getName(), '__')) {
                    $arr[$method->class]['methods'][] = [
                        'name' => $method->getName(),
                        'annotations' => $reader->getMethodAnnotations($method)
                    ];

                    foreach ($reader->getMethodAnnotations($method) as $methodAnnotation) {
                        if ($methodAnnotation instanceof AbstractAnnotation) {
                            $apiEndpoint = ApiEndpoint::where('class', $method->class)->where('method',
                                $method->getName())->first();

                            if (!$apiEndpoint) {
                                $apiEndpoint = new ApiEndpoint();
                            }
                            $apiEndpoint->class = $method->class;
                            $apiEndpoint->method = $method->getName();
                            $apiEndpoint->description = $methodAnnotation->summary;

                            $apiEndpoint->save();
                        }
                    }
                }
            }
        }
    }
}
