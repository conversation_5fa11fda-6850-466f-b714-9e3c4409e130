<?php

namespace App\Console\Commands;

use App\Models\Organization;
use Illuminate\Console\Command;
use Config;
use App\Models\User;
use DB;
use Event;
use App\Events\GeneratePlotImgPreviewEvent;

class GeneratePlotsPreviewImg extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:generate-plots-preview-img
                            {server : The server name} 
                            {organizationId?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generates user plots preview images.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $server = $this->argument('server');
        $organizationId = $this->argument('organizationId');

        $this->loadCountrySpecificConfigs(strtoupper($server));

        if($organizationId) {
            $organizations = Organization::where('id', $organizationId)->get();
        }
        else {
            $organizations = Organization::get();
        }
        
        $organizations->each(function($organization) {
            event(new GeneratePlotImgPreviewEvent($organization));
        });
    }
}
