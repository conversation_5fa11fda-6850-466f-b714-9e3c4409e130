<?php

namespace App\Console\Commands;

use App\Models\Analyzes;
use App\Models\User;
use App\Models\AnalyzesDataStage;
use Config;
use View;
use Event;
use DB;

class AddSoilAnalyzesDataFromCms extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:soil-analyzes-data-from-cms {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Soil Analyzes Data From CMS.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $server = $this->argument('server');
        $this->loadCountrySpecificConfigs(strtoupper($server));

        DB::beginTransaction();
        try {
            $analyze = $this->createAnalyze();
            $mainDB = Config::get('database.connections.main');
            $connectionString = "host={$mainDB['host']}  port=5432 user={$mainDB['username']} dbname=geoscan_cms password={$mainDB['password']}";

            $arrData = AnalyzesDataStage::selectRaw("
                ".$analyze->id." as analyzes_id, sopr.order_id, ssp.sopr_id, cms.lab_number, cms.state_updated_at AS lab_date, so.name as client_name,
                cms.sample_id, cms.barcode, cms.data, '".date('Y-m-d')."' as created_at, '".date('Y-m-d')."' as updated_at  
            ")
                ->rightJoin(
                    DB::raw("dblink('" . $connectionString . "','
                SELECT
                pgp.lab_number, SPLIT_PART(pgp.lab_number, ''-'', 2) as sample_id, pgp.barcode, pgp.state_updated_at,
                json_object_agg (ler.element::TEXT, ler.value::TEXT) AS data
                FROM package_grid_points pgp
                INNER JOIN lab_elements_results ler on ler.package_grid_points_id = pgp.id
                INNER JOIN lab_elements_results_raw lerr on lerr.id = ler.lab_elements_results_raw_id
                INNER JOIN lab_analysis_uploads lau on lau.id = lerr.lab_analisys_uploads_id
                AND ler.value is not NULL
                AND ler.state = ''Approved'' 
                AND pgp.state = ''ReceivedInLab''
                AND ler.element IN (''K2O'', ''P2O5'', ''TMN'', ''pH'', ''OM'')
                GROUP BY pgp.barcode, pgp.lab_number, pgp.state_updated_at
                ORDER BY pgp.lab_number
            ') as cms(lab_number varchar, sample_id int4, barcode varchar, state_updated_at timestamp,data json)"), 'su_analyzes_data_stage.barcode', '=', 'cms.barcode')
                ->join('su_satellite_soil_sample_numbers AS sssn', 'sssn.sample_number', '=', 'cms.barcode')
                ->join('su_satellite_soil_points AS ssp', 'ssp.gid', '=', 'sssn.gid')
                ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.id', '=', 'ssp.sopr_id')
                ->join('su_satellite_orders AS sso', 'sso.id', '=', 'sopr.order_id')
                ->join('su_organizations AS so', 'so.id', '=', 'sso.organization_id')
                ->whereNull('su_analyzes_data_stage.barcode')
                ->get()->toArray();

            if(count($arrData) == 0) {
                throw new \Exception("No data to insert!");
            }

            DB::table('su_analyzes_data_stage')->insert($arrData);

            $analyze->analyzes_count = count($arrData);
            $analyze->save();

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }

        DB::commit();
        $this->info("Done");
    }

    private function createAnalyze()
    {
        $user = User::first();

        DB::statement("SELECT SETVAL('su_analyzes_id_seq', COALESCE(MAX(id), 1) ) FROM su_analyzes");
        $analyze = new Analyzes();
        $analyze->user_id = $user->id;
        $analyze->original_name = 'CMS sync - Soil Analyzes Data';
        $analyze->filename = '00000.csv';
        $analyze->analyzes_count = 0;
        $analyze->missing_barcodes = 0;
        $analyze->missing_analyzes = 0;
        $analyze->comment = '';
        $analyze->path = '';
        $analyze->web_path = '';
        $analyze->save();

        return $analyze;
    }
}
