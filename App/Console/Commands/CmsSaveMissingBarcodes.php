<?php

namespace App\Console\Commands;

use App\Exceptions\ValidationException;
use App\Models\API\SoilSampleNumbers;
use Illuminate\Database\Eloquent\Model;
use Request;
use Route;
use App;
use Config;
use File;
use DB;
use Artisan;
use Validator;

class CmsSaveMissingBarcodes extends AbstractBaseCommand
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cms:save-missing-barcodes 
                                {server : The server name}
                                {id : id from request_logs table}
                                ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send request to cms to save missing barcodes with status 500 from request_logs table.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $server = $this->argument('server');
        $id = $this->argument('id');

        Model::getConnectionResolver()->setDefaultConnection('main');

        $this->validate($id);

        $results = DB::table('request_logs')->select('request')
            ->where('id', $id)
            ->where('status', '500')
            ->where('url', 'ILIKE', '%/soil-samples%')
            ->get()
            ->toArray();

        if(!$results) {
            $this->error("No result found!");
            return false;
        }

        $this->loadCountrySpecificConfigs(strtoupper($server));

        DB::beginTransaction();
        try {

            $this->deleteBarcodes($results);

            $this->saveMissingBarcodesToCms($results);

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }

        DB::commit();
        $this->info("Done");
    }

    private function deleteBarcodes(array $results)
    {
        $arrBarcodes = [];
        foreach ($results as $data) {

            $arrData = json_decode($data->request);
            if (!$arrData) {
                continue;
            }

            $orders = json_decode($arrData->sampled_orders);

            if (!$orders) {
                continue;
            }

            foreach ($orders as $order) {
                foreach ($order->sample_data as $sample_data) {
                    $sample = current($sample_data->samples);
                    $arrBarcodes[] = $sample->sample_number;
                }
            }
        }

        if (count($arrBarcodes) > 0) {
            SoilSampleNumbers::whereIn('sample_number', $arrBarcodes)->delete();
        }

        return true;
    }

    private function saveMissingBarcodesToCms(array $results)
    {
        foreach ($results as $data) {

            $arrData = json_decode($data->request);
            if (!$arrData) {
                continue;
            }

            $orders = json_decode($arrData->sampled_orders);

            if (!$orders) {
                continue;
            }

            App::make('App\Http\Controllers\API\ApiController')->updateSoilSamplesData($orders);
        }

        return true;
    }

    private function validate($id)
    {
        $data = ["id" => $id];
        $validator = Validator::make($data, [
            'id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            throw new ValidationException($validator->errors()->first());
        }

        return true;
    }
}
