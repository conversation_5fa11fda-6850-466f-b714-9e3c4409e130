<?php

namespace App\Console\Commands;

use App\Classes\CMS\AnalysisService;
use App\Models\SoilPoints;
use App\Services\System\SystemService;
use function Clue\StreamFilter\fun;
use function GuzzleHttp\Psr7\str;
use Illuminate\Console\Command;
use DB;

class FillMissingGridPointsInCMS extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix-bug:fill-missing-grind-points-in-cms
                            {server : The server name}
                            {--order_uu_id= : The uudi of the order}
                            {--plot_uuid= : The uudi of the plot}}
                            ';

    /**
     * The console command description.
     *
     * When order is created, subscription_package_field data is set but package_grid_point data is missing.
     *
     * @var string
     */
    protected $description = 'Fix BUG. Fill missing package grid points in CMS';

    private $systemService;

    /**
     * Create a new command instance.
     *
     * FillMissingGridPointsInCMS constructor.
     * @param AnalysisService $analysisService
     */
    public function __construct(SystemService $systemService)
    {
        parent::__construct();
        $this->systemService = $systemService;
    }

    /**
     * Execute the console command.
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function handle()
    {
        $serverName = $this->argument('server');
        $orderUuId = (string)$this->option('order_uu_id');
        $plotUuId = (string)$this->option('plot_uuid');

        if (!$serverName || !$orderUuId || !$plotUuId) {
            if (!$this->error('You have not filled in all the required properties. Type "-h" to see available options.')) {
                return;
            }
        }

        $this->loadCountrySpecificConfigs(strtoupper($serverName));

        $this->info('Get data from DB.');
        $pointsData = SoilPoints::select(
            "su_satellite_soil_points.gid as point_uuid",
            "su_satellite_soil_points.sample_id",
            "ssopr.plot_uuid",
            "ssopr.order_uuid",
            "sssgp.type as grid_type"
        )
            ->join('su_satellite_orders_plots_rel as ssopr', 'ssopr.id', '=', 'su_satellite_soil_points.sopr_id')
            ->join('su_satellite_orders as sso', 'sso.uuid', '=', 'ssopr.order_uuid')
            ->join('su_satellite_soil_grid_params as sssgp', 'sssgp.order_id', '=', 'sso.id')
            ->where('ssopr.plot_uuid', $plotUuId)
            ->where('ssopr.order_uuid', $orderUuId)
            ->get()->toArray();

        $this->info('Send data to CMS.');
        try {
            $this->systemService->sendGridPintDataToCMS($pointsData);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->info('Done');
    }
}
