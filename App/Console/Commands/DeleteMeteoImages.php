<?php

namespace App\Console\Commands;

use Config;
use Illuminate\Console\Command;
use Illuminate\Foundation\Inspiring;
use DB;
use File;

class DeleteMeteoImages extends AbstractBaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:meteo-images
                            {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Meteo Images';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->serverName = $this->argument('server');

        $this->loadCountrySpecificConfigs(strtoupper($this->serverName));

        File::deleteDirectory(config('meteo.METEO_PATH') . config('globals.MACHINE'));
    }
}
