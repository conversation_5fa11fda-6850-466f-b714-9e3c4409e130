<?php

namespace App\Console\Commands;

use App\Helpers\Helper;
use App\Models\LayerPlot;
use App\Models\Satellite;
use Config;
use DB;
use File;
use Illuminate\Console\Command;
use \DOMDocument;

class SyncSentinel extends AbstractBaseCommand
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:sync-sentinel
                                {server : The server name}
                                {--all : Sync all images including downloaded ones.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retrieves Sentinel Images.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $server = $this->argument('server');

        $this->loadCountrySpecificConfigs(strtoupper($server));

        $this->esaSync();
    }

    /**
     * Syncs Sentinel images using Copernicus Open Access Hub API
     */
    private function esaSync()
    {
        $curl = curl_init();

        $apiUrl = Config::get('globals.SENTINEL_ESA_API_URL');
        $userName = Config::get('globals.SENTINEL_ESA_USERNAME');
        $passWord = Config::get('globals.SENTINEL_ESA_PASSWORD');

        $url = $apiUrl . 'search?format=json';

        $url .= "&q=" . curl_escape($curl, Config::get('globals.SENTINEL_ESA_SEARCH_QUERY'));

        curl_setopt($curl, CURLOPT_USERPWD, $userName . ":" . $passWord);
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        $this->commonSync($curl, $url, $apiUrl, $userName, $passWord);
    }

    /**
     * Get Coverage Polygon
     * @param $curl
     * @param $url
     * @return string
     */
    private function getCoveragePoly($curl, $url)
    {

        curl_setopt($curl, CURLOPT_URL, $url);

        $doc = new DOMDocument();
        $doc->loadXML(curl_exec($curl));

        $searchNode = $doc->getElementsByTagNameNS('http://schemas.microsoft.com/ado/2007/08/dataservices', '*');
        foreach ($searchNode as $item) {
            if ($item->nodeName == 'd:ContentGeometry') {
                $geted = $item->nodeValue;
            }
        }

        $pattern = '/<gml:coordinates>(?<gml_coordinates>(.*?))<\/gml:coordinates>/s';
        preg_match($pattern, $geted, $matches, PREG_OFFSET_CAPTURE, 0);

        $gmlCoordinates = $matches['gml_coordinates'][0];
        $arrGmlCoordinates = explode(" ", $gmlCoordinates);

        $arrCorrectCoordinates = array_map(function ($point) {

            $arr = explode(",", $point);
            return $arr[1] . " " . $arr[0];

        }, $arrGmlCoordinates);

        $correctCoordinates = implode(",", $arrCorrectCoordinates);

        $wktCoordinates = "POLYGON ((" . $correctCoordinates . "))";

        return $wktCoordinates;
    }

    private function getFeatureDoubleProps($props)
    {
        $arr = array();

        if (is_string(array_keys($props)[0])) {
            $arr[$props["name"]] = floatval($props["content"]);
        } else {
            foreach ($props as $property) {
                $arr[$property["name"]] = floatval($property["content"]);
            }
        }
        return $arr;
    }

    /**
     * Common Sync Process
     * @param $curl
     * @param $url
     * @return void
     */
    private function commonSync($curl, $url, $apiUrl, $userName, $passWord)
    {
        //Getting available tiles count
        curl_setopt($curl, CURLOPT_URL, $url . '&rows=0');

        $countData = json_decode(curl_exec($curl), true);

        if (!$countData) {
            $this->error('Invalid JSON response');
            return;
        }

        $maxRows = 100;
        $count = $countData['feed']['opensearch:totalResults'];
        $pages = ceil($count / $maxRows);

        $existingTiles = Satellite::where('type', 'sentinel')
            ->pluck('catid');

        for ($page = 1; $page <= $pages; $page++) {

            $start = $maxRows * ($page - 1);

            curl_setopt($curl, CURLOPT_URL, $url . '&rows=' . $maxRows . '&start=' . $start);
            $resultData = json_decode(curl_exec($curl), true);

            if (!$resultData) {
                $this->error('Invalid JSON response');
                return;
            }

            $features = $resultData['feed']['entry'];

            $countFeatures = count($features);

            if (isset($features['id'])) {
                $countFeatures = 1;
            }

            for ($i = 0; $i < $countFeatures; $i++) {

                if (!isset($features['id']) && isset($features[$i])) {
                    $feature = $features[$i];
                } else {
                    $feature = $features;
                }

                if (!isset($feature['title'])) {
                    continue;
                }

                $doubleData = $this->getFeatureDoubleProps($feature['double']);

                $dateData = [];
                for ($j = 0; $j < count($feature['date']); $j++) {
                    $param = $feature['date'][$j];
                    $dateData[$param['name']] = $param['content'];
                }
                $dateTime = strtotime($dateData['beginposition']);

                $data = [];
                for ($j = 0; $j < count($feature['str']); $j++) {
                    $param = $feature['str'][$j];
                    $data[$param['name']] = $param['content'];
                }

                $sceneId = $feature['title'];
                $tileId = $data['tileid'];
                $catId = $sceneId;

                $pattern = '/(?<utm_zone>[\d]{2})(?<lat_band>[\w])(?<cell>[\w]{2})/';
                preg_match_all($pattern, $tileId, $matches, PREG_SET_ORDER, 0);

                $DEFAULT_DB_CRS = Config::get('globals.DEFAULT_DB_CRS');
                $utmZone = $matches[0]['utm_zone'];
                $latitudeBand = $matches[0]['lat_band'];
                $gridSquare = $matches[0]['cell'];

                if (!$this->option('all') && $existingTiles && $existingTiles->contains($catId)) {
                    continue;
                }

                $date = $dateData['beginposition'];
                $dateSimple = strftime("%Y-%m-%d", $dateTime);
                $name = $sceneId;

                $coveragePoly = '';
                if (isset($data['footprint']) && $data['footprint']) {
                    //Data from S2B
                    $coveragePoly = $data['footprint'];
                } else {
                    //Data from S2A
                    $coveragePoly = $this->getCoveragePoly($curl, $apiUrl . "odata/v1/Products('" . $feature['id'] . "')/");
                }

                $tilesDir = Config::get('globals.SENTINEL_PATH') . $tileId . DIRECTORY_SEPARATOR . $dateSimple . DIRECTORY_SEPARATOR . $catId . DIRECTORY_SEPARATOR;

                $this->info("Downloading Tile: $catId");
                $downloaded = $this->esaDownload($feature['id'], $name, $tilesDir, $apiUrl, $userName, $passWord);

                if (!$downloaded) {
                    $this->info("Failed to download Tile: $catId");
                    File::deleteDirectory(Config::get('globals.SENTINEL_PATH') . $tileId . DIRECTORY_SEPARATOR . $dateSimple);
                    $this->deleteLayersPlots($dateSimple, $coveragePoly);
                    continue;
                }

                $this->info("Cloud cover percentage: " . $doubleData["cloudcoverpercentage"]);

                $this->buildVrts($tilesDir);

                $satData = Satellite::firstOrNew([
                    'tileid' => $tileId,
                    'catid' => $catId,
                ]);

                $satData->tileid = $tileId;
                $satData->date = $date;
                $satData->catid = $catId;
                $satData->name = $name;
                $satData->extent = DB::raw("ST_Force_2d(ST_GeomFromText('{$coveragePoly}', 4326))");
                $satData->src_srs = 'EPSG:' . substr($DEFAULT_DB_CRS, 0, 3) . $utmZone;
                $satData->type = 'sentinel';
                $satData->path = $tilesDir;
                $satData->save();

                $tileDBId = $satData->id;
                $this->setCloudsToDatabase($tileDBId, $tilesDir, $satData->src_srs);
            }
        }
    }

    private function deleteLayersPlots($date, $coveragePoly)
    {

        $arrLayers = LayerPlot::select(['su_satellite_layers_plots.id'])
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->where('su_satellite_layers_plots.date', '=', $date)
            ->where('su_satellite_layers_plots.type', '=', 'index')
            ->where(DB::raw('ST_Intersects(ST_Transform(ST_GeomFromText(\'' . $coveragePoly . '\', 4326), ' . Config::get("globals.DEFAULT_DB_CRS") . '), sp.geom)'), '=', true)
            ->get()->toArray();

        LayerPlot::whereIn('id', $arrLayers)->delete();
    }

    /**
     * Downloads necessary Sentinel tiles
     * @param $productId
     * @param $fileName
     * @param $tilesDir Download directory
     * @return bool True on success
     */
    private function esaDownload($productId, $fileName, $tilesDir, $apiUrl, $userName, $passWord)
    {
        $user = $userName;
        $pass = $passWord;

        if (!File::exists($tilesDir)) {
            File::makeDirectory($tilesDir, 755, true);
        }

        $baseUrl = $apiUrl . "odata/v1/Products('{$productId}')/Nodes('{$fileName}.SAFE')/";

        $command = "wget --no-check-certificate --continue --user={$user} --password={$pass} \"{$baseUrl}Nodes('manifest.safe')/\\\$value\" -O {$tilesDir}manifest.safe";
        $this->info($command);
        system($command);

        try {
            $xml = simplexml_load_file("{$tilesDir}manifest.safe");
        } catch (\Exception $e) {
            $xml = false;
        }

        if (!$xml) {
            //return false to download from the alternative server
            return false;
        }

        foreach ($xml->dataObjectSection->dataObject as $dataObject) {
            $atrr = $dataObject->byteStream->fileLocation->attributes();

            //downloading only needed files
            if (!str_is('*_B02.jp2', $atrr['href'])
                && !str_is('*_B03.jp2', $atrr['href'])
                && !str_is('*_B04.jp2', $atrr['href'])
                && !str_is('*_B05.jp2', $atrr['href'])
                && !str_is('*_B08.jp2', $atrr['href'])
                && !str_is('*_B11.jp2', $atrr['href'])
                && !str_is('*MSK_CLOUDS_B00.gml', $atrr['href'])
            ) {
                continue;
            }

            $nodes = explode('/', trim($atrr['href'], './'));
            $nodes = array_map(function ($node) {
                return "Nodes('{$node}')";
            }, $nodes);

            $url = '"' . $baseUrl . implode('/', $nodes) . '/\$value"';

            $name = substr($atrr['href'], -7);
            if (str_is('*MSK_CLOUDS_B00.gml', $atrr['href'])) {
                $name = 'MSK_CLOUDS_B00.gml';
            }

            $command = "wget --no-check-certificate --continue --user={$user} --password={$pass} {$url} -O {$tilesDir}{$name}";

            if (!$this->wget($command, $tilesDir . $name)) {
                return false;
            }
        }

        if (File::exists($tilesDir . 'B02.jp2')
            && filesize($tilesDir . 'B02.jp2') > 0
            && File::exists($tilesDir . 'B03.jp2')
            && filesize($tilesDir . 'B03.jp2') > 0
            && File::exists($tilesDir . 'B04.jp2')
            && filesize($tilesDir . 'B04.jp2') > 0
            && File::exists($tilesDir . 'B05.jp2')
            && filesize($tilesDir . 'B05.jp2') > 0
            && File::exists($tilesDir . 'B08.jp2')
            && filesize($tilesDir . 'B08.jp2') > 0
            && File::exists($tilesDir . 'B11.jp2')
            && filesize($tilesDir . 'B11.jp2') > 0
            && File::exists($tilesDir . 'MSK_CLOUDS_B00.gml')
        ) {
            return true;
        }

        return false;
    }

    private function wget($command, $outFile, $retries = 0)
    {
        if ($retries >= 2) {
            return false;
        }

        $this->info($command);
        system($command);
        if (filesize($outFile) === 0) {
            $retries = $retries + 1;
            $this->wget($command, $outFile, $retries);
        }

        return true;
    }

    private function awsDownload($productId, $tilesDir)
    {
        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');

        $command = "{$pythonPath} {$scriptsPath}sentinel_downloader.py -d " . $tilesDir . " -p " . $productId;
        echo ($command);
        system($command);

        if (File::exists($tilesDir . 'B02.jp2')
            && filesize($tilesDir . 'B02.jp2') > 0
            && File::exists($tilesDir . 'B03.jp2')
            && filesize($tilesDir . 'B03.jp2') > 0
            && File::exists($tilesDir . 'B04.jp2')
            && filesize($tilesDir . 'B04.jp2') > 0
            && File::exists($tilesDir . 'B05.jp2')
            && filesize($tilesDir . 'B05.jp2') > 0
            && File::exists($tilesDir . 'B08.jp2')
            && filesize($tilesDir . 'B08.jp2') > 0
            && File::exists($tilesDir . 'B11.jp2')
            && filesize($tilesDir . 'B11.jp2') > 0
            && File::exists($tilesDir . 'MSK_CLOUDS_B00.gml')
        ) {
            return true;
        }

        return false;
    }

    /**
     * @param $tileId
     * @param $tilesDir
     * @param $srcSrs
     */
    protected function setCloudsToDatabase($tileId, $tilesDir, $srcSrs)
    {
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $currentDatabase = Config::get('database.connections.' . Config::get('database.default'));
        $database = "'PG:host={$currentDatabase['host']} user={$currentDatabase['username']} dbname={$currentDatabase['database']} password={$currentDatabase['password']} port={$currentDatabase['port']}'";
        $maskGml = $tilesDir . 'MSK_CLOUDS_B00.gml';

        $defaultProj = Config::get('globals.DEFAULT_DB_CRS');

        if (!Helper::gsHasChildNodes($maskGml, "eop:maskMembers")) {
            return;
        }

        $command = "{$gdalBinPath}ogr2ogr -append -s_srs '{$srcSrs}' -t_srs 'EPSG:{$defaultProj}' -f 'PostgreSQL' {$database} {$maskGml} -nln su_satellite_clouds -sql 'SELECT {$tileId} AS tile_id, OGR_GEOMETRY, OGR_GEOM_AREA AS area FROM MaskFeature'";
        $this->info($command);
        system($command);

        DB::table('su_satellite_clouds')
            ->where('tile_id', $tileId)
            ->where(DB::raw('ST_IsValid(geom)'), false)
            ->update(['geom' => DB::raw('ST_MakeValid(geom)')]);

        DB::table('su_satellite_clouds')
            ->where('tile_id', $tileId)
            ->where(DB::raw('ST_IsValid(geom)'), false)
            ->delete();
    }
    /**
     * Builds necessary vrt files
     * @param $tilesDir
     * @return bool True on success
     */
    private function buildVrts($tilesDir)
    {
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');

        $command = "{$gdalBinPath}gdalbuildvrt -separate {$tilesDir}RGBNIR.vrt {$tilesDir}B02.jp2 {$tilesDir}B03.jp2 {$tilesDir}B04.jp2 {$tilesDir}B08.jp2 {$tilesDir}B11.jp2";
        $this->info($command);
        system($command);

        $command = "{$gdalBinPath}gdalbuildvrt -separate {$tilesDir}ALLBANDS.vrt {$tilesDir}B02.jp2 {$tilesDir}B03.jp2 {$tilesDir}B04.jp2 {$tilesDir}B05.jp2 {$tilesDir}B08.jp2 {$tilesDir}B11.jp2";
        $this->info($command);
        system($command);

        $command = "{$pythonPath} {$scriptsPath}trans_rgb_sentinel.py {$tilesDir}RGBNIR.vrt {$tilesDir}RGB.vrt  --gdal_bin_path={$gdalBinPath}";
        $this->info($command);
        system($command);

        if (File::exists($tilesDir . 'RGBNIR.vrt')
            && File::exists($tilesDir . 'ALLBANDS.vrt')
            && File::exists($tilesDir . 'RGB.vrt')
        ) {
            return true;
        }

        return false;
    }
}
