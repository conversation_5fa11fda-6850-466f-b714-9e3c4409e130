<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 30/04/2018
 * Time: 14:04
 */

namespace App\Console\Commands;

use App\Models\GlobalUser;
use Illuminate\Console\Command;
use DB;
use Illuminate\Database\Eloquent\Model;

class MigrateUsersFromLocalDBsToMainDB extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:migrate {userCountry}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate users to main DB.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Model::getConnectionResolver()->setDefaultConnection('main');

        $userCountry = strtoupper($this->argument('userCountry'));

        $country = DB::table('countries')->where('iso_alpha_2_code', $userCountry)->get()->first();

        $countryConnection = DB::connection($country->iso_alpha_2_code);

        $alreadyMigrated = DB::table('su_users')->select('old_id')->where('country', $country->id)->get();

        $ids = [];
        foreach ($alreadyMigrated as $each) {
            $ids[] = $each->old_id;
        }

        $where = '';
        if (!empty($ids)) {
            $where = ' WHERE id NOT IN(' . implode(',', $ids) . ')';
        }
        $users = $countryConnection->select('SELECT * FROM su_users' . $where);

        $this->info("Found " . count($users) . " users.");

        $parentUsers = [];
        $cnt = 0;
        foreach ($users as $user) {
            $userRoles = $countryConnection->select('SELECT * FROM su_role_user where user_id=' . $user->id);

            $newUser = [
                'username' => $user->username,
                'password' => $user->password,
                'name' => $user->email,
                'email' => $user->email,
                'old_id' => $user->id,
                'old_group_id' => $user->group_id,
                'country' => $country->id,
                'is_superadmin' => $user->is_superadmin,
                'active' => $user->active,
                'group_id' => $user->group_id
            ];
            $newUserId = GlobalUser::insertGetId($newUser);

            $roles = [];
            foreach ($userRoles as $userRole) {
                $roles[] = ['user_id' => $newUserId, 'role_id' => $userRole->role_id];
            }
            DB::table('su_role_user')->insert($roles);

            $cnt++;

            $parentUsers[] = ['newUserId' => $newUserId, 'parent_old_id' => $user->parent_id];
        }

        GlobalUser::fixTree();
        foreach ($parentUsers as $each) {
            $globalUser = GlobalUser::find($each['newUserId']);
            $parentUser = GlobalUser::where('old_id', $each['parent_old_id'])->where('country', $country->id)->first();


            if ($parentUser) {
                $globalUser->parent_id = $parentUser->id;

                $globalUser->save();
            }
        }

        GlobalUser::fixTree();

        $this->info('Done. Migrated ' . $cnt . ' users.');
    }
}
