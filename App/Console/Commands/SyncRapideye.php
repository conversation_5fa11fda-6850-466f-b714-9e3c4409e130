<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use ZipArchive;
use App\Models\Satellite;
use Config;
use File;
use DB;
use Artisan;

class SyncRap<PERSON>ye extends AbstractBaseCommand {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'satellite:sync-rapideye {server : The server name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retrieves Rapidye Images.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        $server = $this->argument('server');

        $this->loadCountrySpecificConfigs(strtoupper($server));

        $url = Config::get('globals.RAPIDEYE_API_URL');
        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');

        $url .= "&toiStart=" . Config::get('globals.RAPIDEYE_TOI_START') . "&toiEnd=" . Config::get('globals.RAPIDEYE_TOI_END');
        $url .= "&blkFill=" . Config::get('globals.RAPIDEYE_BLK_FILL') . "&cc=" . Config::get('globals.RAPIDEYE_CC');
        $url .= "&imgType=" . Config::get('globals.RAPIDEYE_IMGTYPE') . "&aoiType=pol&country=" . Config::get('globals.RAPIDEYE_COUNTRY');

        $curl = curl_init($url);

        curl_setopt($curl, CURLOPT_USERPWD, Config::get('globals.RAPIDEYE_USERNAME') . ":" . Config::get('globals.RAPIDEYE_PASSWORD'));
        curl_setopt($curl, CURLOPT_FAILONERROR, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        $results = json_decode(curl_exec($curl));
        $count = $results->returnedFeatures;
        $success = $results->success;
        $features = array_reverse($results->features);
        $data = null;
        
        if(!$success) {
            return;
        }
		
        $tileDates = [];
		$this->doMapcacheSeedFlag = false;

        for ($i = 0; $i < $count; $i++) {
            $feature = $features[$i];
			
            $catId = $feature->catId;
            $tileId = $feature->tileId;
            $date = $feature->acqTime;
            $date_simple = strftime("%Y-%m-%d", strtotime($date));
            $prodUrl = $feature->prodUrl;
            $name = $feature->name;
            $coveragePoly = $feature->coveragePoly;
            $utmZone = $feature->utmZone;

            $data = Satellite::where('tileid', '=', $tileId)
                ->where('date', '=', $date)
                ->where('catid', '=', $catId)
                ->first();
            
            if ($data) {
                continue;
            }

            $dir = Config::get('globals.RAPIDEYE_PATH') . $tileId . DIRECTORY_SEPARATOR . $date_simple;
            if (!is_dir(Config::get('globals.RAPIDEYE_PATH') . $tileId)) {
                mkdir(Config::get('globals.RAPIDEYE_PATH') . $tileId);
            }
            if (!is_dir($dir)) {
                mkdir($dir);
            }
            $zipFile = $dir . DIRECTORY_SEPARATOR . $name . '.zip';
            $this->info("Processing Tile: $zipFile");

            exec('curl -u ' . Config::get('globals.RAPIDEYE_USERNAME') . ':' . Config::get('globals.RAPIDEYE_PASSWORD') . ' -o ' . $zipFile . ' "' . $prodUrl . '"');
            $isExtracted = false;

            $zip = new ZipArchive;
            if ($zip->open($zipFile) === TRUE) {
                $zip->extractTo($dir);
                $zip->close();
                File::copyDirectory($dir.DIRECTORY_SEPARATOR.$name, $dir);
                File::deleteDirectory($dir.DIRECTORY_SEPARATOR.$name);
                $isExtracted = true;
            } else {
                $this->error('Extraction failed!');
                continue;
            }

            system("{$pythonPath} {$scriptsPath}geotoa.py {$dir}/{$name}_metadata.xml {$dir}/{$name}.tif");
            rename("{$dir}/{$name}_refToa.tif", "{$dir}/{$name}.tif");
            unlink("{$dir}/{$name}_refToa.aux");
            
            $tileRGBName = $dir . '/rgb_' . $name . '.vrt';
            $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');
            system("{$pythonPath} {$scriptsPath}trans_rgb_rapideye.py {$dir}/{$name}.tif {$tileRGBName} --gdal_bin_path={$gdalBinPath}");

            if ($isExtracted) {
                unlink($zipFile);

                $satData = new Satellite();
                $satData->tileid = $tileId;
                $satData->date = $date;
                $satData->catid = $catId;
                $satData->name = $name;
                $satData->src_srs = 'EPSG:326' . $utmZone;
                $satData->path = $dir . DIRECTORY_SEPARATOR;
                $satData->save();

                $id = $satData->id;

                DB::table('su_satellite')
                    ->where('id', '=', $id)
                    ->update(array('extent' => DB::raw("ST_Force_2d(ST_GeomFromText('{$coveragePoly}', 4326))")));
					
                $tileDates[] = $date_simple;
				
				if($feature->blkFill <= 10) {
					$this->doMapcacheSeedFlag = true;
					$this->replaceTile($tileRGBName, $tileId);
				}
            }
        }
        
        $newDates = array_values(array_unique($tileDates));
        
		if($this->doMapcacheSeedFlag) {
			$this->mapcacheSeed($newDates);
		}
    }

    /**
     * Moves tiles in tile_index folder.
     * @param  string $newTile The full path of the new thile.
     * @param  int $tileId  The id of the new tile.
     */
    private function replaceTile($newTile, $tileId) {
        $handle = opendir(Config::get('globals.TILE_INDEX_DIR'));
        while (false !== ($oldTile = readdir($handle))) {
            if (substr_count($oldTile, $tileId)) {
                unlink(Config::get('globals.TILE_INDEX_DIR') . $oldTile);
            }
        }
		
		$fileName = Config::get('globals.TILE_INDEX_DIR') . basename($newTile);
		system("ln -s {$newTile} {$fileName}");
        
        $shapePath = Config::get('globals.TILE_INDEX_SHP');
		$tileIndexPath = Config::get('globals.TILE_INDEX_DIR');
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');

        system("rm {$shapePath}/index.*");
		system("{$gdalBinPath}gdaltindex -t_srs EPSG:4326 -src_srs_name src_srs -write_absolute_path {$shapePath}index.shp {$tileIndexPath}*.vrt");
    }
	
	private function mapcacheSeed($newDates) {
		$mapcacheXmlFile = Config::get('globals.MAPCACHE_XML_FILE');
        $extent = Config::get('globals.MAPCACHE_DEFAULT_EXTENT');
        
        $newTilesExtent = Satellite::select(DB::raw("ST_Extent(ST_Transform(extent, 900913)) AS extent"))
                ->whereIn(DB::raw("date::DATE"), $newDates)
                ->value('extent');
        
        if($newTilesExtent) {
            $extent = str_replace(' ', ',', trim($newTilesExtent, 'BOX()'));
        }
		
		if(app()->environment() === 'production') {
		    system("/usr/local/bin/mapcache_seed -c {$mapcacheXmlFile} -z 13,18 -n 4 -t geo_scan -g g -l geo_scan -e {$extent}");
		}
	}

}
