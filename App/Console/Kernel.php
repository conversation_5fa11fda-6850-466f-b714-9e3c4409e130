<?php

namespace App\Console;

use App\Console\Commands\AddRasterLayers;
use App\Console\Commands\AddSoilAnalyzesDataFromCms;
use App\Console\Commands\AutoApproveOrdersCommand;
use App\Console\Commands\CalculateCloudPercentage;
use App\Console\Commands\CalculatePestDiseaseRisks;
use App\Console\Commands\CheckWeatherStationsHealth;
use App\Console\Commands\CmsSaveMissingBarcodes;
use App\Console\Commands\DeleteMeteoImages;
use App\Console\Commands\DeleteReportFiles;
use App\Console\Commands\DropForgottenTmpTables;
use App\Console\Commands\FarmAndOrganizations;
use App\Console\Commands\FillMissingGridPointsInCMS;
use App\Console\Commands\FixUserTree;
use App\Console\Commands\GenerateMapFiles;
use App\Console\Commands\GeneratePinCoverImages;
use App\Console\Commands\GeneratePlotsPreviewImg;
use App\Console\Commands\GenerateSoilMap;
use App\Console\Commands\GenerateStaticMapFiles;
use App\Console\Commands\ImportAgrobalanceSamples;
use App\Console\Commands\IntegrationReport;
use App\Console\Commands\MigrateUsersFromLocalDBsToMainDB;
use App\Console\Commands\NewCountry;
use App\Console\Commands\ProcessExistingOrder;
use App\Console\Commands\ProcessMeteoOrder;
use App\Console\Commands\ProcessOrder;
use App\Console\Commands\RemakeVRTFiles;
use App\Console\Commands\ReprocessClouds;
use App\Console\Commands\ReprocessOrders;
use App\Console\Commands\SendPushNotifications;
use App\Console\Commands\SetGDDEntriesLeft;
use App\Console\Commands\SyncLandsat;
use App\Console\Commands\SyncRapideye;
use App\Console\Commands\SyncSatellite;
use App\Console\Commands\SyncSentinel;
use App\Console\Commands\UpdateApiEndpointsList;
use App\Console\Commands\UpdateBarcodes;
use App\Models\Country;
use App\Models\IntegrationReportsTypes;
use App\Services\Irrigation\IrrigationDataRawService;
use App\Services\Irrigation\IrrigationUnitService;
use App\Services\Machine\MachineEventService;
use App\Services\Machine\MachineUnitService;
use Config;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Schema;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        SyncRapideye::class,
        ProcessOrder::class,
        NewCountry::class,
        ProcessMeteoOrder::class,
        ProcessExistingOrder::class,
        AddRasterLayers::class,
        SendPushNotifications::class,
        GenerateMapFiles::class,
        GeneratePlotsPreviewImg::class,
        GenerateStaticMapFiles::class,
        DropForgottenTmpTables::class,
        DeleteMeteoImages::class,
        SyncSentinel::class,
        SyncLandsat::class,
        SyncSatellite::class,
        RemakeVRTFiles::class,
        ReprocessOrders::class,
        DeleteReportFiles::class,
        GeneratePinCoverImages::class,
        ReprocessClouds::class,
        CalculateCloudPercentage::class,
        ImportAgrobalanceSamples::class,
        SetGDDEntriesLeft::class,
        UpdateBarcodes::class,
        FarmAndOrganizations::class,
        CheckWeatherStationsHealth::class,
        IntegrationReport::class,
        AddSoilAnalyzesDataFromCms::class,
        GenerateSoilMap::class,
        CmsSaveMissingBarcodes::class,
        FillMissingGridPointsInCMS::class,
        FixUserTree::class,
        MigrateUsersFromLocalDBsToMainDB::class,
        AutoApproveOrdersCommand::class,
        UpdateApiEndpointsList::class,
        CalculatePestDiseaseRisks::class
    ];

    public function __construct(
        Application              $app,
        Dispatcher               $events,
        IrrigationDataRawService $irrigationDataRawService,
        IrrigationUnitService    $irrigationUnitService,
        MachineUnitService       $machineUnitService,
        MachineEventService      $machineEventService
    )
    {
        parent::__construct($app, $events);

        $this->irrigationDataRawService = $irrigationDataRawService;
        $this->irrigationUnitService = $irrigationUnitService;
        $this->machineUnitService = $machineUnitService;
        $this->machineEventService = $machineEventService;
    }

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        if (Schema::hasTable('countries') && Schema::hasColumn('countries', 'active')) {
            // $availableCountries = Country::where('active', true)->get();
            // $this->integrationReportsTasks($schedule, $availableCountries);
            // $this->systemTasks($schedule, $availableCountries);
        }
    }

    private function integrationReportsTasks(Schedule $schedule, $availableCountries)
    {
        foreach ($availableCountries as $country) {
            Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));
            $reports = IntegrationReportsTypes::getIntegrationReportsQuery()->get()->toArray();

            foreach ($reports as $report) {

                $schedule->command('integration:report', [
                    $country->iso_alpha_2_code,
                    '--report_id' => $report['id'],
                    '--params' => $report['params'],
                    '--url' => $report['url'],
                    '--token' => $report['token'],
                ])
                    ->withoutOverlapping()
                    ->cron($report['period'])
                    ->after(function () use ($report, $country) {

                        Config::set('database.default', strtoupper($country->iso_alpha_2_code));

                        if ($report['name'] === "irrigation_per_day") {
                            $this->irrigationDataRawService->addContent($report['id'], $report['organization_id']);
                            $newIrrigationEventIds = $this->irrigationDataRawService->addIrrigationEvents();
                            $this->irrigationDataRawService->addContentEventsPlots($newIrrigationEventIds);
                        }
                        if ($report['name'] === "irrigation_units") {
                            $this->irrigationUnitService->storeFormattedIrrigationUnitReport($report['id'], $report['organization_id']);
                        }
                        if ($report['name'] === "machines_current") {
                            $this->machineUnitService->storeCurrentMachineReport($report['id'], $report['organization_id']);
                        }
                        if ($report['name'] === "machine_events") {
                            $this->machineEventService->storeMachineEventsReport($report['id'], $report['organization_id']);
                        }
                    });
            }
        }
    }

    private function systemTasks(Schedule $schedule, $availableCountries)
    {
        foreach ($availableCountries as $country) {
            $logsPath = storage_path() . DIRECTORY_SEPARATOR . 'logs' . DIRECTORY_SEPARATOR . $country->iso_alpha_2_code . DIRECTORY_SEPARATOR;

            $schedule->command('satellite:sync-satellite', [
                $country->iso_alpha_2_code
            ])
                ->withoutOverlapping()
                ->appendOutputTo($logsPath . 'sync-satellite.log')
                ->twiceDaily(1, 13);

            $schedule->command('satellite:process-order', [
                $country->iso_alpha_2_code
            ])
                ->appendOutputTo($logsPath . 'process-order.log')
                ->everyMinute();

            $schedule->command('satellite:send-push-notifications', [
                $country->iso_alpha_2_code
            ])
                ->withoutOverlapping()
                ->appendOutputTo($logsPath . 'send-push-notifications.log')
                ->dailyAt('09:00');

            $schedule->command('drop:forgotten-tmp-tables', [
                $country->iso_alpha_2_code
            ])
                ->appendOutputTo($logsPath . 'droped-forgotten-tmp-tables.log')
                ->dailyAt('03:00');

            $schedule->command('meteo:set-gdd-entries-left', [
                $country->iso_alpha_2_code
            ])
                ->appendOutputTo($logsPath . 'updated-gdd-entries-left.log')
                ->dailyAt('00:00');

            $schedule->command('delete:meteo-images', [
                $country->iso_alpha_2_code
            ])
                ->withoutOverlapping()
                ->appendOutputTo($logsPath . 'deleted-meteo-images.log')
                ->dailyAt('10:00');

            $schedule->command('delete:report-files', [
                $country->iso_alpha_2_code
            ])
                ->withoutOverlapping()
                ->appendOutputTo($logsPath . 'deleted-report-files.log')
                ->hourly();

            $schedule->command('stations:check-weather-stations-health')
                ->withoutOverlapping()
                ->appendOutputTo($logsPath . 'check-weather-stations-health.log')
                ->dailyAt('06:00');

            $schedule->command('add:soil-analyzes-data-from-cms', [
                $country->iso_alpha_2_code
            ])
                ->withoutOverlapping()
                ->appendOutputTo($logsPath . 'soil-analyzes-data-from-cms.log')
                ->dailyAt('04:00');
        }
    }

}
