<?php

namespace App\Models;

use App\Helpers\Helper;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;

class OrderPlotRel extends BaseModel
{

    protected $table      = 'su_satellite_orders_plots_rel';
    public    $timestamps = false;

    public function order()
    {
        return $this->belongsTo('App\Models\Order', 'order_id', 'id');
    }

    public function plot()
    {
        return $this->belongsTo('App\Models\Plot', 'plot_id', 'gid');
    }

    protected $casts = [
        'array_type' => 'array',
        'array_custom_cell_area' => 'array',
        'geom_json' => 'object',
    ];

    /**
     * @param array $plotsIds
     * @return mixed
     */
    public static function findExistingOrdersByPlots(array $plotsIds)
    {
        $query  = OrderPlotRel::select('su_satellite_orders.id', DB::raw('to_char(su_satellite_orders."date", \'YYYY-MM-DD\') as date'),
            DB::raw('json_agg(distinct coalesce (sgp.type, \'custom\')) as array_type'), DB::raw('json_agg(distinct coalesce (sgp.custom_cell_area, 0)) as array_custom_cell_area'))
            ->join('su_satellite_orders', 'su_satellite_orders_plots_rel.order_uuid', '=', 'su_satellite_orders.uuid')
            ->join('su_satellite_plots as p', 'p.uuid', '=', 'su_satellite_orders_plots_rel.plot_uuid')
            ->leftJoin('su_satellite_soil_grid_params as sgp', 'sgp.order_id', '=', 'su_satellite_orders.id')
            ->where('su_satellite_orders.type', 'soil')
            ->groupBy('su_satellite_orders.id')
            ->having(DB::raw('array_agg(distinct p.gid)'), '@>', DB::raw('array[' . implode(', ', $plotsIds) . ']::integer[]'));

        return $query->get();
    }

    /**
     * Add 'where' statements in the query Builder object
     * @param object $query query Builder object
     * @param array $params
     * @return query Builder object
     */
    public function scopeFilter($query, array $params)
    {

        if (isset($params['name']) && trim($params['name']) !== '') {
            $query->where('name', 'ILIKE', trim('%' . $params['name'] . '%'));
        }

        if (isset($params['culture']) && trim($params['culture']) !== '') {
            $query->where('culture', '=', trim($params['culture']));
        }

        return $query;
    }

    public function removeDuplicateGeoms($order_id = null)
    {
        $table = $this->table;
        $pkey = $this->primaryKey;

        $this->whereIn($pkey, function ($query) use ($table, $pkey, $order_id) {
            $query->select("b.{$pkey}")
                ->from(DB::raw("{$table} AS a, {$table} AS b"))
                ->whereRaw("a.{$pkey} > b.{$pkey}")
                ->whereRaw("a.order_id = b.order_id")
                ->where("a.order_id", $order_id)
                ->whereRaw("ST_Equals(a.geom, b.geom)");
        })->delete();
    }

    public function getOrdersPeriodReport()
    {
        $request = Request::all();
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $arrSamples = OrderPlotRel::from('su_satellite_orders_plots_rel as sopr')
            ->selectRaw("sp.name as plot_name,
                    sp.area,
                    round((sp.area*{$areaCoef})::numeric, 3) AS area,
                    sopr.sync_date,
                    sopr.sampler_id,
                    so.id AS order_id,
                    so.company_name AS company_name,
                    u.NAME AS sampler,
                    COUNT (ssp.track) AS samples_with_data,
                    COUNT (ssp.gid) AS total_samples")
            ->join('su_satellite_orders as so', 'so.id', '=', 'sopr.order_id')
            ->join('su_satellite_plots as sp', 'sp.gid', '=', 'sopr.plot_id')
            ->join('su_satellite_soil_points as ssp', 'ssp.sopr_id', '=', 'sopr.id')
            ->leftJoin('su_users as u', 'u.id', '=', 'sopr.sampler_id')
            ->where('type', 'soil')
            ->whereNotNull('sync_date')
            ->groupBy('sopr.id', 'sp.gid', 'so.id', 'u.id');


        $sort = Helper::getSortRequest('-order_id');
        $arrSamples = $this->scopeRestOrderBy($arrSamples, $sort);

        if (isset($request['sampler']) && $request['sampler']) {
            $arrSamples = $arrSamples->where('sampler_id', '=', $request['sampler']);
        }
        if (isset($request['from_date']) && $request['from_date']) {
            $arrSamples = $arrSamples->where('sync_date', '>=', $request['from_date']);
        }
        if (isset($request['to_date']) && $request['to_date']) {
            $arrSamples = $arrSamples->where('sync_date', '<=', $request['to_date']);
        }
        if (isset($request['company']) && $request['company']) {
            $arrSamples = $arrSamples->where('company_name', 'ILIKE', trim('%' . $request['company'] . '%'));
        }
        if (isset($request['order']) && $request['order']) {
            $arrSamples = $arrSamples->where('order_id', $request['order']);
        }
        if (isset($request['plot']) && $request['plot']) {
            $arrSamples = $arrSamples->where('sp.name', 'ILIKE', trim('%' . $request['plot'] . '%'));
        }

        return $arrSamples;
    }

    public function getPlotsForMailReport($orderId, $syncDate)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $sampledPlots =
            DB::table('su_satellite_orders_plots_rel as sopr')
                ->join('su_satellite_orders as so', 'so.id', '=', 'sopr.order_id')
                ->join('su_satellite_plots as sp', 'sp.gid', '=', 'sopr.plot_id')
                ->join('su_satellite_soil_points as ssp', 'ssp.sopr_id', '=', 'sopr.id')
                ->join('su_satellite_soil_sample_numbers as ssn', 'ssn.gid', '=', 'ssp.gid')
                ->groupBy(
                    'sopr.order_id',
                    'sopr.sync_date',
                    'sp.name',
                    'sp.area',
                    'sp.gid',
                    'ssp.gid',
                    'ssn.sample_number',
                    'ssn.treatment_type'
                )
                ->orderBy('ssn.treatment_type', 'ssp.gid')
                ->select(
                    'sopr.order_id',
                    'sopr.sync_date AS sampling_date',
                    'sp.name AS plot_name',
                    DB::raw("round((sp.area*{$areaCoef})::numeric, 3) AS plot_area"),
                    'sp.gid AS plot_gid',
                    DB::raw('ST_Extent(ST_Buffer(sp.geom, 75)) AS extent'),
                    'ssp.gid as cell_gid',
                    'ssp.sample_id as cell_number',
                    'ssn.sample_number',
                    'ssn.treatment_type'
                )->where('sopr.order_id', '=', $orderId)
                ->whereRaw("sopr.sync_date::date = '{$syncDate}'");

        return $sampledPlots;
    }

    public static function usersByRelIds($processedRelIds)
    {

        $result = self::select([
            DB::raw("DISTINCT ON (so.user_id) so.user_id")
        ])
            ->join('su_satellite_orders as so', 'so.id', '=', 'su_satellite_orders_plots_rel.order_id')
            ->whereIn('su_satellite_orders_plots_rel.id', $processedRelIds)
            ->get()->toArray();

        return $result;
    }

    public static function changeSoilSampleStatus($soprIds, $status)
    {
        self::whereIn('su_satellite_orders_plots_rel.id', $soprIds)
            ->update(array('soil_sample_status' => $status));
    }

    public static function getTrearmentTypes($soprId)
    {
        $arrSoilTreatments = Config::get('globals.SOIL_TREATMENT');
        $arrSoilTreatments = array_keys($arrSoilTreatments);

        $result = self::select([
            DB::raw('DISTINCT ssn.treatment_type'),
            DB::raw('a.created_at::DATE as date')
        ])
            ->join('su_satellite_soil_points as ssp', 'ssp.sopr_id', '=', 'su_satellite_orders_plots_rel.id')
            ->join('su_satellite_soil_sample_numbers as ssn', 'ssn.gid', '=', 'ssp.gid')
            ->join('su_analyzes_data_stage as ad', 'ad.barcode', '=', 'ssn.sample_number')
            ->join('su_analyzes as a', 'a.id', '=', 'ad.analyzes_id')
            ->where('su_satellite_orders_plots_rel.id', '=', $soprId)
            ->whereIn('ssn.treatment_type', $arrSoilTreatments)
            ->get()
            ->toArray();

        return $result;
    }

    public static function existsElement($soprId, $soilElement, $treatmentType)
    {
        $result = self::select([
            'ad.id'
        ])
            ->join('su_satellite_soil_points as ssp', 'ssp.sopr_id', '=', 'su_satellite_orders_plots_rel.id')
            ->join('su_satellite_soil_sample_numbers as ssn', 'ssn.gid', '=', 'ssp.gid')
            ->join('su_analyzes_data_stage as ad', 'ad.barcode', '=', 'ssn.sample_number')
            ->where('su_satellite_orders_plots_rel.id', '=', $soprId)
            ->where('ssn.treatment_type', '=', '' . $treatmentType . '')
            ->groupBy('ad.id')
            ->havingRaw("count(ad.data::json->>'" . $soilElement . "') > 0")
            ->get()
            ->toArray();

        return $result;
    }

    /**
     * @param int $soprId
     *
     * @return array
     */
    public static function soilElementValues(int $soprId): array
    {
        $elementsQ = self::join('su_satellite_soil_points AS ssp', 'ssp.sopr_id', '=', 'su_satellite_orders_plots_rel.id')
            ->join('su_satellite_plots AS sp', 'sp.uuid', '=', 'su_satellite_orders_plots_rel.plot_uuid')
            ->join('su_satellite_soil_sample_numbers AS ssn', 'ssn.gid', '=', 'ssp.gid')
            ->join('su_analyzes_data_stage AS ad', 'ad.barcode', '=', 'ssn.sample_number')
            ->crossJoin(DB::raw('json_each_text(ad."data"::json) as elements'))
            ->where('ad.sopr_id', $soprId)
            ->selectRaw("
                jsonb_build_object(
                    'date', ad.lab_date ,
                    'lab_id', ad.lab_number,
                    'cell_id', ad.sample_id,
                    'elements', json_object_agg(
                        elements.key, jsonb_build_object(
                            'value', ROUND((elements.value)::numeric, 2)
                        )
                    ),
                    'plot_uuid', su_satellite_orders_plots_rel.plot_uuid,
                    'plot_name', sp.name,
                    'soil_layer_cm', MULTIPLE_REPLACE(su_satellite_orders_plots_rel.treatment_id , ARRAY['0','1','2'], ARRAY['0-30','30-60','leaf']),
                    'geom_json', ST_AsGeoJSON(ST_MakePoint(ST_X(ST_Transform(ST_Centroid(ssp.geom), 4326))::numeric, ST_Y(ST_Transform(ST_Centroid(ssp.geom), 4326))::numeric))::json
                )::json samples 
            ")
            ->groupBy('ad.lab_date', 'ad.lab_number', 'su_satellite_orders_plots_rel.plot_uuid', 'ssp.geom', 'ad.sample_id', 'sp.name', 'su_satellite_orders_plots_rel.treatment_id')
            ->orderBy('ad.sample_id');

        $chartQuery = DB::table('elements_data')
            ->withExpression('elements_data', $elementsQ)
            ->selectRaw("
                jsonb_agg(elements_data.samples) as elements_data_agg");

        return json_decode($chartQuery->pluck('elements_data_agg')->first(), true);
    }

    public static function getPlotsForProtocol(Order $order)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $res =
            DB::table('su_satellite_orders_plots_rel as sopr')
                ->join('su_satellite_orders as so', 'so.id', '=', 'sopr.order_id')
                ->join('su_organizations as o', 'o.id', '=', 'so.organization_id')
                ->join('su_satellite_plots as sp', 'sp.gid', '=', 'sopr.plot_id')
                ->join('su_satellite_plots_files as spf', 'spf.plot_id', '=', 'sp.gid')
                ->select(
                    'sp.name AS name',
                    DB::raw("round((sp.area*{$areaCoef})::numeric, 3) AS area"),
                    'sp.gid AS gid',
                    DB::raw('st_x(st_transform(st_centroid(sp.geom), 4326)) as x'),
                    DB::raw('st_y(st_transform(st_centroid(sp.geom), 4326)) as y'),
                    DB::raw('(array_agg(spf.web_path))[1] AS web_path'),
                    DB::raw('(array_agg(spf.path))[1] AS path')

                )
                ->where('sopr.order_id', '=', $order->id)
                ->groupBy('sp.name', 'sp.area', 'sp.gid')
                ->get();

        return $res;
    }

    public static function getPlotsProtocolData(array $orderUuids = [], array $plotUuids = [], string $customerIdentification)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $res =
            DB::table('su_satellite_orders_plots_rel as sopr')
                ->join('su_satellite_orders as so', 'so.id', '=', 'sopr.order_id')
                ->join('su_organizations as o', 'o.id', '=', 'so.organization_id')
                ->join('su_satellite_plots as sp', 'sp.gid', '=', 'sopr.plot_id')
                ->leftJoin('su_satellite_soil_grid as ssg', 'ssg.sopr_id', '=', 'sopr.id')
                ->select(
                    'sp.name AS name',
                    DB::raw("round((sp.area*{$areaCoef})::numeric, 3) AS area"),
                    'sp.gid AS gid',
                    DB::raw('max(sopr.id) AS sopr_id'),
                    DB::raw('st_x(st_transform(st_centroid(sp.geom), 4326)) as x'),
                    DB::raw('st_y(st_transform(st_centroid(sp.geom), 4326)) as y'),
                    DB::raw("
                        CASE WHEN
                            count(distinct ssg.sample_id) > 0
                        THEN
                            gs_svg_thumbnail(st_collect(ssg.geom), 'rgb(30, 86, 51)', 'rgb(227, 237, 223)', 300, 165, 1)
                        ELSE
                            gs_svg_thumbnail(sp.geom, 'rgb(30, 86, 51)', 'rgb(227, 237, 223)', 300, 165, 1)
                        END AS svg
                    "),
                    DB::raw('count(distinct ssg.sample_id) as grid_cells_num')

                )
                ->whereIn('sopr.order_uuid', $orderUuids)
                ->whereIn('sopr.plot_uuid', $plotUuids)
                ->where('o.identity_number', $customerIdentification)
                ->groupBy('sp.name', 'sp.area', 'sp.gid', 'o.name')
                ->get();

        return $res;
    }

    public static function findByOrderPlotIds($orderId, $plotId) {
        return self::where([
            ['order_id', $orderId],
            ['plot_id', $plotId]
        ])->first();
    }

    /**
     * @return mixed
     */
    public static function getFilesAndTypeForSamples()
    {
        $query = OrderPlotRel::select(
            "su_satellite_orders_plots_rel.id",
            DB::raw("'db' as type")
        )
            ->join('su_satellite_orders AS so', 'so.id', '=', 'su_satellite_orders_plots_rel.order_id')
            ->join('su_analyzes_data_stage AS ads', 'ads.sopr_id', '=', 'su_satellite_orders_plots_rel.id')
            ->join('su_analyzes AS a', 'a.id', '=', 'ads.analyzes_id')
            ->join('su_satellite_soil_sample_numbers AS ssn', 'ssn.sample_number', '=', 'ads.barcode')
            ->where('so.type', '=', 'soil')
            ->distinct();

        return $query;
    }

    /**
     * Get the sampled area by years
     * 
     * @param int $organizationId
     * @param array $filters
     * 
     * @return array
     */
    public static function getSampledAreaByYears(int $organizationId, array $filters = [])
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = OrderPlotRel::from('su_satellite_orders_plots_rel AS ssopr')
            ->join('su_satellite_orders AS sso', 'sso.uuid', '=', 'ssopr.order_uuid')
            ->join('su_satellite_plots AS ssp', 'ssp.uuid', '=', 'ssopr.plot_uuid')
            ->where('sso.type', Order::TYPE_SOIL)
            ->where([
                ['sso.organization_id', $organizationId],
                ['sso.status', '!=', 'canceled']
            ])
            ->select(
                'sso.year',
                DB::raw("SUM(ROUND((ssp.area * {$areaCoef})::numeric, 3)) FILTER (WHERE ssopr.soil_sample_status = 'processed') AS area")
            )
            ->groupBy('sso.year');


        if (isset($filters['plot_uuid']) && strlen($filters['plot_uuid']) > 0)
        {
            $query->where('ssp.uuid', $filters['plot_uuid']);
        }

        return $query->get()->toArray();
    }
}
