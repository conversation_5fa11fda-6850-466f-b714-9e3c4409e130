<?php

namespace App\Models;

use phpseclib\Math\BigInteger;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Staudenmeir\LaravelCte\Query\Builder as CteBuilder;
use Config;

class MachineProduct extends BaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_machine_products';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    public function events()
    {
        return $this->belongsToMany(MachineEvent::class, 'su_machine_event_products', 'product_id', 'event_id')->withTimestamps();
    }

}
