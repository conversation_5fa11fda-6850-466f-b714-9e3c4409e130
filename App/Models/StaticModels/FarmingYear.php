<?php

namespace App\Models\StaticModels;

use App\Models\Order;
use App\Models\Organization;
use Config;
use DateTime;
use function getdate;

class FarmingYear implements StaticModel
{
    public static function getAll()
    {
        $month = (int)date('m');
        $curentYear = (int)date('Y');

        if ($month > 9) {
            $curentYear++;
        }

        $startYear = 2015;

        $farmingYears = [];
        for ($y = $startYear; $y <= $curentYear + Config::get('globals.FARMING_YEARS_AFTER_CURRENT'); $y++) {
            $farmingYears[] = [
                'title' => $y,
                'id' => $y,
                'default' => ($y == $curentYear),
                'year' => $y,
                'from_date' => ($y - 1) . '-10-01',
                'to_date' => $y . '-09-30',
                'farming_year' => ($y - 1) . '/' . $y
            ];
        }

        return collect($farmingYears);
    }

    public static function byOrganization(Organization $organization)
    {
        $month = (int)date('m');
        $curentYear = (int)date('Y');

        if ($month > 9) {
            $curentYear++;
        }

        $years = range(2015, $curentYear + Config::get('globals.FARMING_YEARS_AFTER_CURRENT'));
        $farmingYears = [];
        $FarmingYearsRangeResults = Order::findFarmingYearsByRange($organization, $years);

        foreach ($years as $year) {
            $fromDate = ($year - 1) . '-10-01';     
            $toDate = $year . '-09-30';

            $key = array_search($year, array_column($FarmingYearsRangeResults, 'year'));
            if ($key) {
                $fromDate = (new DateTime($FarmingYearsRangeResults[$key]['from_date']))->format('Y-m-d');
                $toDate = (new DateTime($FarmingYearsRangeResults[$key]['to_date']))->format('Y-m-d');
            }

            $farmingYears[] = [
                'title' => $year,
                'id' => $year,
                'default' => ($year === $curentYear),
                'year' => $year,
                'from_date' => $fromDate,
                'to_date' => $toDate,
                'farming_year' => ($year - 1) . '/' . $year
            ];
        }

        return collect($farmingYears);
    }

    public static function getYearbyDate(string $date)
    {
        $farmYear = (int)date("Y", strtotime($date));
        $month = (int)date("n", strtotime($date));

        if ($month > 9) {
            $farmYear = $farmYear + 1;
        }

        return $farmYear;
    }

    /**
     * Get farming years starting with $startDate and ending with $endDate
     *
     * @param string $startDate
     * @param string $endDate
     *
     * @return array
     */
    public static function getFarmingYearsByRange(string $startDate, string $endDate): array
    {
        $start = getdate(strtotime($startDate));
        $end = getdate(strtotime($endDate));
        $current = getdate();

        $startYear = $start['mon'] > 9 ? $start['year'] + 1 : $start['year'];
        $endYear = $end['mon'] > 9 ? $end['year'] + 1 : $end['year'];
        $currentYear = $current['mon'] > 9 ? $current['year'] + 1 : $current['year'];

        $farmingYears = [];
        for ($year = $startYear; $year <= $endYear; $year++) {
            $farmingYears[] = [
                'id' => $year,
                'title' => $year,
                'default' => ($year === $currentYear),
                'year' => $year,
                'from_date' => $year === $startYear ? $startDate : strval($startYear) . "-10-01",
                'to_date' => $year === $endYear ? $endDate : strval($year) . "-09-30",
                'farming_year' => ($year - 1) . '/' . $year
            ];

            if ($year !== $startYear) {
                $startYear++;
            }
        }

        return $farmingYears;
    }
}
