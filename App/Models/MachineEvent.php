<?php

namespace App\Models;

use Config;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use phpseclib\Math\BigInteger;
use Staudenmeir\LaravelCte\Query\Builder as CteBuilder;

class MachineEvent extends BaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_machine_events';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'geojson_cultivated' => 'object',
        'geojson_track' => 'object',
        'report_json' => 'object',
        'implement' => 'object',
        'crop' => 'object',
        'products' => 'array'
    ];

    public function products()
    {
        return $this->belongsToMany(MachineProduct::class, 'su_machine_event_products', 'event_id', 'product_id')->withTimestamps();
    }

    public static function getList(int $organizationId, array $filter, string $lang): LengthAwarePaginator
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = self::getListQuery($organizationId, $filter);
        $query->leftJoin('su_machine_event_products as smep', 'smep.event_id', '=', 'su_machine_events.id')
            ->leftJoin('su_machine_products as smp', function ($join) use ($organizationId) {
                $join->on('smep.product_id', '=', 'smp.id')
                    ->where([
                        ['smp.organization_id', $organizationId],
                        ['smp.state', 'Active']
                    ]);
            })
            ->select(
                "su_machine_events.id",
                "sp.name as plot_name",
                "mu.name as machine_name",
                "mu.type as machine_type",
                DB::raw("
                    CASE WHEN mi.id NOTNULL THEN
                        JSON_BUILD_OBJECT(
                            'id' , mi.id,
                            'name', mi.name,
                            'width', mi.width,
                            'work_operation', CASE WHEN swot.id NOTNULL THEN 
                                JSON_BUILD_OBJECT (
                                    'id', swot.id,
                                    'name', swot.name
                                )
                            END
                        )
                    END as implement
                "),
                "su_machine_events.date",
                DB::raw("su_machine_events.start_date::time as start_time"),
                DB::raw("su_machine_events.end_date::time as end_time"),
                DB::raw("CASE WHEN NOT ST_IsEmpty(su_machine_events.geom_cultivated) then ST_AsGeoJSON(ST_Transform(su_machine_events.geom_cultivated, 3857)) END as geojson_cultivated"),
                DB::raw("ST_AsGeoJSON(ST_Transform(su_machine_events.geom_track, 3857)) as geojson_track"),
                DB::raw("round(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric, 3) AS cultivated_area"),
                DB::raw("round((sp.area*{$areaCoef})::numeric, 3) as plot_area"),
                "su_machine_events.max_speed",
                "su_machine_events.avg_speed",
                DB::raw("round((su_machine_events.length_track::numeric/1000), 1) as length_track"),
                DB::raw("su_machine_events.fuel_consumed_driving::int"),
                DB::raw("su_machine_events.fuel_consumed_stay::int"),
                "su_machine_events.type",
                "su_machine_events.duration",
                "su_machine_events.duration_stay",
                "su_machine_events.stage",
                "su_machine_events.driver",
                DB::raw("
                    CASE WHEN spc.id NOTNULL THEN
                        JSON_BUILD_OBJECT(
                            'id' , scc.id,
                            'name', scc.crop_name_{$lang}
                        )
                    END as crop
                "),
                DB::raw("
                    COALESCE(
                        JSONB_AGG(
                            JSONB_BUILD_OBJECT(
                                'id', smep.id,
                                'product', JSONB_BUILD_OBJECT(
                                    'id', smp.id,
                                    'name', smp.name,
                                    'type', smp.type,
                                    'unit', smp.unit,
                                    'unit_per_area', smp.unit_per_area
                                ),
                                'rate', smep.rate,
                                'value', smep.value,
                                'pest_name', smep.pest_name,
                                'pest_application', smep.pest_application,
                                'pest_quarantine', smep.pest_quarantine
                            )
                        ) FILTER (WHERE smp.id NOTNULL),
                        '[]'
                    ) as products
                ")
            )
            ->groupBy(
                "su_machine_events.id",
                "mu.id",
                "mi.id",
                "sp.gid",
                "spc.id",
                "scc.id",
                "swot.id"
            )
            ->orderBy(DB::raw('start_date::date'), 'DESC')
            ->orderBy(DB::raw('mu.id'), 'DESC')
            ->orderBy(DB::raw('start_date::time'), 'ASC');

        return $query->paginate($filter['limit']);
    }

    /**
     * Get machine events group by types
     * @param int $organizationId The id of organization.
     * @param array $filter Use this array to filter the data
     *
     * @return Builder
     */
    public static function getDataByTypeQuery(int $organizationId, array $filter): Builder
    {
        $query = self::getListQuery($organizationId, $filter);

        $query->select(
            "su_machine_events.type",
            DB::raw("count(su_machine_events.type) as value")
        );

        $query->groupBy("su_machine_events.type");

        return $query;
    }

    /**
     * Get work operations from machine events
     * @param int $organizationId The id of organization.
     * @param array $filter Use this array to filter the data
     *
     * @return Builder
     */
    public static function getEventWorkOperationsQuery(int $organizationId, array $filter): Builder
    {
        $query = self::getListQuery($organizationId, $filter);

        $query->whereNotNull('swot.id')
            ->select(
                "swot.name as work_operation_name",
                DB::raw("count(mi.name) as value")
            );

        $query->groupBy("swot.name");

        return $query;
    }

    /**
     * Get Machine Events data for Tasks BarChart
     * @param int $organizationId The id of organization.
     * @param array $filter Use this array to filter the data
     *
     * @return Builder
     */
    public static function getDataTasks(int $organizationId, array $filter): array
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $queryMain = self::getListQuery($organizationId, $filter);
        $queryMain->select(
            DB::raw("coalesce(sum(su_machine_events.length_track), 0) as distance"),
            DB::raw("coalesce(sum(round((su_machine_events.fuel_consumed_driving + su_machine_events.fuel_consumed_stay)::numeric, 3)), 0) as fuel"),
            DB::raw("coalesce(sum(round(((ST_Area(su_machine_events.geom_cultivated)/1000)*{$areaCoef})::numeric, 3)), 0) AS area"),
            "su_machine_events.date",
            "su_machine_events.stage"
        );
        $queryMain->groupBy("su_machine_events.date", "su_machine_events.stage");

        $queryCase = DB::table('data_main')
            ->select(
                DB::raw("MAX(CASE WHEN data_main.stage = 'Approved' THEN area else 0 END) as area_approved"),
                DB::raw("MAX(CASE WHEN data_main.stage = 'Proposed' THEN area else 0 END) as area_proposed"),
                DB::raw("MAX(CASE WHEN data_main.stage = 'Approved' THEN fuel else 0 END) as fuel_approved"),
                DB::raw("MAX(CASE WHEN data_main.stage = 'Proposed' THEN fuel else 0 END) as fuel_proposed"),
                DB::raw("MAX(CASE WHEN data_main.stage = 'Approved' THEN distance else 0 END) as distance_approved"),
                DB::raw("MAX(CASE WHEN data_main.stage = 'Proposed' THEN distance else 0 END) as distance_proposed"),
                DB::raw("SUM(distance) as distance_by_date"),
                DB::raw("SUM(fuel) as fuel_by_date"),
                DB::raw("SUM(area) as area_by_date"),
                DB::raw("EXTRACT(epoch FROM data_main.date) AS date")
            )
            ->groupBy("data_main.date")
            ->orderBy("data_main.date");

        $queryResult = DB::table('case_data')
            ->withExpression('data_main', $queryMain)
            ->withExpression('case_data', $queryCase)
            ->select(DB::raw("
                json_build_object(
                    'data', json_build_object(
                        'series', json_build_array(
                            json_build_object(
								'name', 'Area (approved)',
								'type', 'bar',
								'stack', 'area',
								'data', coalesce(json_agg(case_data.area_approved), '[]'::json),
								'itemStyle', json_build_object('color', '#4C9115')
                            ),
                            json_build_object(
                                    'name', 'Area (proposed)',
                                    'type', 'bar',
                                    'stack', 'area',
                                    'data', coalesce(json_agg(case_data.area_proposed), '[]'::json),
                                    'itemStyle', json_build_object('color', '#72C62F', 'barBorderRadius', '[8, 8, 0, 0]'::json)
                            ),
                            json_build_object(
                                    'name', 'Fuel (approved)',
                                    'type', 'bar',
                                    'stack', 'fuel',
                                    'data', coalesce(json_agg(case_data.fuel_approved), '[]'::json),
                                    'itemStyle', json_build_object('color', '#E3C524')
                            ),
                            json_build_object(
                                    'name', 'Fuel (proposed)',
                                    'type', 'bar',
                                    'stack', 'fuel',
                                    'data', coalesce(json_agg(case_data.fuel_proposed), '[]'::json),
                                    'itemStyle', json_build_object('color', '#E3C524', 'barBorderRadius', '[8, 8, 0, 0]'::json)
                            ),
                            json_build_object(
                                    'name', 'Distance (approved)',
                                    'type', 'bar',
                                    'stack', 'distance',
                                    'data', coalesce(json_agg((case_data.distance_approved::float/1000)::int), '[]'::json),
                                    'itemStyle', json_build_object('color', '#212A5E')
                            ),
                            json_build_object(
                                    'name', 'Distance (proposed)',
                                    'type', 'bar',
                                    'stack', 'distance',
                                    'data', coalesce(json_agg((case_data.distance_proposed::float/1000)::int), '[]'::json),
                                    'itemStyle', json_build_object('color', '#5769CC', 'barBorderRadius', '[8, 8, 0, 0]'::json)
                            )
                        ),
                        'xAxis', json_build_array(
                            json_build_object(
                                    'type', 'category',
                                    'data', coalesce(json_agg(case_data.date), '[]'::json)
                            )
                        )
                    ),
                    'totalDistance', coalesce((sum(case_data.distance_by_date)/1000)::int, 0),
		            'totalFuel', coalesce(sum(case_data.fuel_by_date)::int, 0),
		            'totalArea', coalesce(sum(case_data.area_by_date)::int, 0)
                ) as echart_data
            "));

        $echartData = $queryResult->pluck('echart_data')->first();
        $echartData = json_decode($echartData, true);
        return $echartData;
    }

    /**
     * Get machine events
     * @param int $organizationId The id of organization.
     * @param array $filter Use this array to filter the data
     *
     * @return Builder
     */
    public static function getListQuery(int $organizationId, array $filter): Builder
    {
        $query = self::join('su_machine_units AS mu', 'mu.id', '=', 'su_machine_events.machine_id')
            ->leftJoin('su_satellite_plots AS sp', 'sp.gid', '=', 'su_machine_events.plot_id')
            ->leftJoin('su_machines_implements AS mi', 'mi.id', '=', 'su_machine_events.machine_implement_id')
            ->leftJoin('su_work_operations_types AS swot', function ($join) {
                $join->on('swot.implement_id', '=', 'mi.id');

                if (isset($filter['types']) && count($filter['types']) > 0 && in_array(WorkOperation::APPROVED, $filter['types'])) {
                    $join->on('swot.id', '=', 'su_machine_events.work_operation_id')
                        ->where('su_machine_events.stage', '=', WorkOperation::APPROVED);
                }
            })
            ->leftJoin('su_farms AS f', 'f.id', '=', 'sp.farm_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) use ($filter) {
                $join->on('spc.plot_id', '=', 'su_machine_events.plot_id')
                    ->where(DB::raw('su_machine_events.date::DATE'), '>=', DB::raw('spc.from_date::DATE'))
                    ->where(DB::raw('su_machine_events.date::DATE'), '<=', DB::raw('spc.to_date::DATE'))
                    ->where('spc.year', '=', DB::raw("EXTRACT('year' FROM su_machine_events.date)"));
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'spc.crop_id')
            ->where('mu.organization_id', $organizationId);

        if (isset($filter['from']) && isset($filter['to'])) {
            $query->whereRaw('su_machine_events.date BETWEEN to_timestamp(?)::date AND to_timestamp(?)::date', [$filter['from'], $filter['to']]);
        }

        if (isset($filter['farmIds']) && count($filter['farmIds']) > 0) {
            $query->where(function ($query) use ($filter) {
                $query->whereIn('sp.farm_id', $filter['farmIds'])
                    ->orWhere('su_machine_events.type', 'Transportation');
            });
        }

        if (isset($filter['plotIds']) && count($filter['plotIds']) > 0) {
            $query->whereIn('sp.gid', $filter['plotIds']);
        }

        if (isset($filter['machineIds']) && count($filter['machineIds']) > 0) {
            $query->whereIn('su_machine_events.machine_id', $filter['machineIds']);
        }

        if (isset($filter['eventIds']) && count($filter['eventIds']) > 0) {
            $query->whereIn('su_machine_events.id', $filter['eventIds']);
        }

        if (isset($filter['implements']) && count($filter['implements']) > 0) {
            $query->whereIn('mi.name', $filter['implements']);
        }

        if (isset($filter['work_operations']) && count($filter['work_operations']) > 0) {
            $query->whereIn('swot.name', $filter['work_operations']);
        }

        if (isset($filter['types']) && count($filter['types']) > 0) {
            $query->whereIn('su_machine_events.type', $filter['types']);
        }

        if (isset($filter['stages']) && count($filter['stages']) > 0) {
            $query->whereIn('su_machine_events.stage', $filter['stages']);
        }

        if (isset($filter['driver'])) {
            $query->where(DB::raw("COALESCE(su_machine_events.driver, '')"), 'ilike', trim('%' . $filter['driver'] . '%'));
        }

        if (isset($filter['cropIds']) && count($filter['cropIds']) > 0) {
            $query->whereIn('spc.crop_id', $filter['cropIds']);
        }

        return $query;
    }

    public static function insertMachineEventData(CteBuilder $query): void
    {
        self::insertUsing([
            'plot_id',
            'machine_id',
            'machine_implement_id',
            'date',
            'start_date',
            'end_date',
            'max_speed',
            'avg_speed',
            'length_track',
            'fuel_consumed_driving',
            'fuel_consumed_stay',
            'type',
            'duration',
            'geom_cultivated',
            'geom_track',
            'duration_stay',
            'stage',
            'driver'
        ], $query);
    }


    /**
     * @param int $organizationId
     * @param string $date
     * @param int|null $wialonUnitId
     */
    public static function deleteEvents(int $organizationId, string $date, ?int $wialonUnitId)
    {
        $query = self::join('su_machine_units as smu', 'smu.id', '=', 'su_machine_events.machine_id')
            ->where([
                ['su_machine_events.date', $date],
                ['smu.organization_id', $organizationId]
            ]);

        if ($wialonUnitId) {
            $query->where('smu.wialon_unit_id', '=', $wialonUnitId);
        }

        $query->delete();
    }

    /**
     * Get all drivers from events
     *
     * @param int $organizationId
     * @param string|null $name
     *
     * @return array
     */
    public static function getDrivers(int $organizationId, ?string $name): array
    {
        $query = self::join('su_machine_units AS mu', 'mu.id', '=', 'su_machine_events.machine_id')
            ->where('mu.organization_id', $organizationId)
            ->whereNotNull('su_machine_events.driver')
            ->selectRaw('DISTINCT su_machine_events.driver');

        if (isset($name)) {
            $query->where("su_machine_events.driver", "ilike", "%{$name}%");
        }


        return $query->pluck('driver')->toArray();
    }

    /**
     * Get all event types (e.g. Work, Transportation, ... etc.)
     *
     * @return array
     */
    public static function getTypes(): array
    {
        $query = self::selectRaw("DISTINCT UNNEST(ENUM_RANGE(NULL::proposed_events_types_enum)) as event_type");

        return $query->pluck('event_type')->toArray();

    }

    /**
     * Calculate the cultivated geom by custom implement width
     *
     * @param int $organizationId
     * @param int $eventId
     * @param float $implementWidth
     *
     * @return array
     */
    public static function getCultivatedGeomCustom(int $organizationId, int $eventId, float $implementWidth): array
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $recalculatedCultivatedGeomQuery = self::join('su_machine_units AS smu', 'smu.id', '=', 'su_machine_events.machine_id')
            ->where([
                ['smu.organization_id', $organizationId],
                ['su_machine_events.id', $eventId]
            ])
            ->select(
                DB::raw("ST_Buffer(su_machine_events.geom_track, ({$implementWidth} / 2), 'endcap=flat join=round') as geom")
            );

        $query = self::from('recalculated_cultivated_geom')
            ->withExpression('recalculated_cultivated_geom', $recalculatedCultivatedGeomQuery)
            ->select(
                DB::raw("
                    ROUND(
                        ((ST_Area(recalculated_cultivated_geom.geom)/1000)*{$areaCoef})::numeric,
                        3
                    ) AS area
                "),
                DB::raw("
                    CASE WHEN NOT ST_IsEmpty(recalculated_cultivated_geom.geom) THEN
                        ST_AsGeoJSON(
                            ST_Transform(
                                ST_MakeValid(
                                    recalculated_cultivated_geom.geom
                                ),
                                3857
                            )
                        ) 
                    END AS geojson_cultivated
                ")
            );

        return $query->first()->toArray();
    }
}
