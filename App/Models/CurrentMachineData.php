<?php

namespace App\Models;

use DB;
use Carbon\Carbon;
use App\Models\StaticModels\FarmingYear;

/**
 * Class CurrentMachineData
 * @package App
 *
 * @property integer $organization_id
 * @property string $geojson
 */
class CurrentMachineData extends BaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_current_machines_data';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var integer
     */
    protected $primaryKey = 'organization_id';

    /** @var array */
    protected $fillable = ['organization_id', 'geojson'];

    protected $casts = [
        'geojson' => 'array',
        'echart' => 'array'
    ];

    public static function queryCurrentGeoJson(string $tmpTable, int $organizationId)
    {
        $machines = CurrentMachineData::machinesQuery($tmpTable, $organizationId);
        $plots = CurrentMachineData::plotsQuery($organizationId);
        $machinesPlots = CurrentMachineData::machinesPlotsQuery();

        return self::from('machines_plots')
            ->withExpression('machines', $machines)
            ->withExpression('plots', $plots)
            ->withExpression('machines_plots', $machinesPlots)
            ->select(
                DB::raw("json_build_object(
                    'type', 'FeatureCollection',
                    'features', json_agg(
                        json_build_object(
                            'type', 'Feature',
                            'geometry', ST_AsGeoJSON(st_transform(coordinates, 3857) )::json,
                            'properties', json_build_object(
                                'name', name,
                                'unit_id', unit_id,
                                'wialon_unit_imei', wialon_unit_imei,
                                'status', status,
                                'speed', speed,
                                'driver', driver,
                                'trailer', CASE WHEN trailer NOTNULL AND trailer <> '' AND trailer_status = 'Active'
                                    THEN trailer
                                    ELSE 'No trailer'
                                END,
                                'last_communication', time,
                                'plot_name', plot_name,
                                'plot_id', plot_id,
                                'farm_id', farm_id,
                                'unit_type', type
                            )
                        )
                    )
                ) as geojson")
            );
    }

    public static function machinesQuery(string $tmpTable, int $organizationId)
    {
        // Machines
        return self::from('su_machine_units as smu')
            ->select(
                DB::raw('distinct on (smu.wialon_unit_imei) CASE WHEN tmp.wialon_unit_imei ISNULL THEN smu.wialon_unit_imei ELSE tmp.wialon_unit_imei::bigint END as wialon_unit_imei'),
                'smu.id as unit_id',
                'smu.name',
                DB::raw("
                    CASE 
                        WHEN tmp.time ISNULL THEN to_char(smu.last_communication, 'YYYY-MM-DD HH24:MI:SS')
                        ELSE to_char(tmp.time, 'YYYY-MM-DD HH24:MI:SS') 
                    END as time
                "),
                'tmp.speed',
                'tmp.value as ignition',
                DB::raw("
                CASE WHEN tmp.value = '1.00' AND TO_NUMBER(tmp.speed, '999') > 0 THEN 'Moving'
                WHEN tmp.value = '0.00' THEN 'Off'
                WHEN tmp.value = '1.00' AND TO_NUMBER(tmp.speed, '999') = 0 THEN 'Idle' 
                WHEN tmp.value ISNULL THEN 'Offline' END as status"),
                DB::raw('CASE WHEN tmp.coordinates ISNULL THEN smu.last_position ELSE tmp.coordinates END as coordinates'),
                'tmp.driver',
                'tmp.trailer',
                'smu.type',
                'smi.status as trailer_status'
            )
            ->leftJoin($tmpTable . ' as tmp', DB::raw('tmp.wialon_unit_imei::bigint'), '=', 'smu.wialon_unit_imei')
            // TODO: Change this to join by id when it is available
            ->leftJoin('su_machines_implements as smi', 'smi.name', '=', 'tmp.trailer')
            ->where('smu.organization_id', '=', $organizationId)
            ->where(function($query) {
                $query->where(DB::raw('LOWER(tmp.sensor)'), '=', 'ignition');
                $query->orWhereNull('tmp.sensor');
            })
            ->orderBy('smu.wialon_unit_imei')
            ->orderBy('time', 'desc');
    }

    public static function plotsQuery(int $organizationId)
    {
        $currentFarmYear = FarmingYear::getAll()->where('default', true)->pluck('year')->first();
        return self::from('su_satellite_plots AS ssp')
            ->select(
                'ssp.gid AS plot_id',
                'ssp.name AS plot_name',
                'ssp.geom',
                'ssp.farm_id',
                'f.organization_id'
            )
            ->join('su_farms AS f', function ($join) use ($organizationId) {
                $join->on('f.id', '=', 'ssp.farm_id')
                    ->where('f.organization_id', '=', $organizationId);
            })
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'ssp.gid')
            ->join('su_satellite_orders as so', function ($join) use ($currentFarmYear) {
                $join->on('so.id', '=', 'sopr.order_id')
                    ->where('so.year', '=', $currentFarmYear)
                    ->where('so.type', '=', 'index')
                    ->whereIn('so.status', ['processed', 'processing', 'paid', 'no_tile']);
            });  
    }

    public static function machinesPlotsQuery()
    {

        // Machines Plots
        return self::from('machines')
            ->select(
                DB::raw('distinct on (machines.wialon_unit_imei) wialon_unit_imei'),
                'machines.unit_id',
                'machines.name',
                'machines.time',
                'machines.speed',
                'machines.ignition',
                'machines.status',
                'machines.coordinates',
                'machines.driver',
                'machines.trailer',
                'machines.type',
                'machines.trailer_status',
                'plots.plot_id',
                'plots.plot_name',
                'plots.geom',
                'plots.farm_id',
                DB::raw('ST_Distance(machines.coordinates, plots.geom) as dist')
            )
            ->leftJoin('plots', function ($join) {
                $join->on(DB::raw('ST_DWithin(machines.coordinates, plots.geom, 100)'), DB::raw('true::boolean'));
            })
            ->orderBy('machines.wialon_unit_imei', 'desc')
            ->orderBy('dist', 'asc');
    }

    public static function getEchartTrailers(int $organizationId, array $filters = []): array
    {
        $currentDate = Carbon::today()->format('Y-m-d');
        $geojsonFeaturesQuery = self::selectRaw(
                "json_array_elements(geojson->'features') AS feature"
            )
            ->where('organization_id', $organizationId);


        $currentDataQuery = self::from('geojson_features')
            ->select(
                DB::raw("
                    CASE
                        WHEN (feature->'properties'->>'trailer') = '' 
                        OR (feature->'properties'->>'trailer') ISNULL 
                    THEN 
                        'No trailer' 
                    ELSE 
                        (feature->'properties'->>'trailer')
                    END AS trailer
                "),
                DB::raw("(feature->'properties'->>'unit_id')::int AS unit_id"),
                DB::raw("(feature->'properties'->>'plot_id')::int AS gid"),
                DB::raw("(feature->'properties'->>'farm_id')::int AS farm_id")
            );

        $trailerDataQuery = self::from('current_data')
            ->select(
                DB::raw("btrim(regexp_matches(trailer, '^[a-zA-Z\u0400-\u04FF]+\s?[a-zA-Z\u0400-\u04FF]+')::TEXT, '{ }\"') as trailer_name"),
                'unit_id',
                'gid',
                'farm_id'
            );

        $trailerEchartDataQuery = self::from('trailer_data')
            ->withExpression('geojson_features', $geojsonFeaturesQuery)
            ->withExpression('current_data', $currentDataQuery)
            ->withExpression('trailer_data', $trailerDataQuery)
            ->leftJoin('su_satellite_plots_crops AS sspc', function ($join) use ($currentDate) {
                $join->on('sspc.plot_id', '=', 'gid')
                    ->where('sspc.from_date', '<=', $currentDate)
                    ->where('sspc.to_date', '>=', $currentDate)
                    ->where('sspc.is_primary', '=', DB::raw("true"));
            })
            ->selectRaw("
                json_build_object(
                    'name', trailer_name,
                    'value', COALESCE(count(unit_id), 0)
                ) as echart
            ");

        if (isset($filters['plot_ids']) && count($filters['plot_ids']) > 0) {
            $trailerEchartDataQuery->whereIn('gid', $filters['plot_ids']);
        }

        if (isset($filters['farm_ids']) && count($filters['farm_ids']) > 0) {
            $trailerEchartDataQuery->whereIn('farm_id', $filters['farm_ids']);
        }

        if (isset($filters['machine_ids']) && count($filters['machine_ids']) > 0) {
            $trailerEchartDataQuery->whereIn('unit_id', $filters['machine_ids']);
        }

        if (isset($filters['names']) && count($filters['names']) > 0) {
            $trailerEchartDataQuery->whereIn('trailer_name', $filters['names']);
        }

        if (isset($filters['crop_ids']) && count($filters['crop_ids']) > 0) {
            $trailerEchartDataQuery->whereIn('sspc.crop_id', $filters['crop_ids']);
        }

        $trailerEchartDataQuery
            ->groupBy("trailer_name")
            ->orderBy("trailer_name");

        return $trailerEchartDataQuery->get()->pluck('echart')->toArray();
    }

    /**
     * Get current machine data by state for echart.
     *
     * @param int $organizationId
     * @param array $filters = [
     *      'plot_ids' => int[],
     *      'machine_ids' => int[],
     *      'farm_ids' => int[]
     * ]
     * @return array
     */
    public static function getStateEchart(int $organizationId, array $filters = []): array
    {
        $currentDate = Carbon::today()->format('Y-m-d');
        $geojsonFeaturesQuery = self::selectRaw(
                "json_array_elements(geojson->'features') AS feature"
            )
            ->where('organization_id', $organizationId);


        $currentDataQuery = self::from('geojson_features')
            ->select(
                DB::raw("feature->'properties'->>'status' AS status"),
                DB::raw("(feature->'properties'->>'unit_id')::int AS unit_id"),
                DB::raw("(feature->'properties'->>'plot_id')::int AS gid"),
                DB::raw("(feature->'properties'->>'farm_id')::int AS farm_id")
            );

        $echartDataQuery = self::from('current_data')
            ->withExpression('geojson_features', $geojsonFeaturesQuery)
            ->withExpression('current_data', $currentDataQuery)
            ->leftJoin('su_satellite_plots_crops AS sspc', function ($join) use ($currentDate) {
                $join->on('sspc.plot_id', '=', 'gid')
                    ->where('sspc.from_date', '<=', $currentDate)
                    ->where('sspc.to_date', '>=', $currentDate)
                    ->where('sspc.is_primary', '=', DB::raw("true"));
            })
            ->selectRaw("
                json_build_object(
                    'name', status,
                    'value', COALESCE(count(unit_id), 0),
                    'status', status
                ) as echart
            ");

        if (isset($filters['plot_ids']) && count($filters['plot_ids']) > 0) {
            $echartDataQuery->whereIn('gid', $filters['plot_ids']);
        }

        if (isset($filters['farm_ids']) && count($filters['farm_ids']) > 0) {
            $echartDataQuery->whereIn('farm_id', $filters['farm_ids']);
        }

        if (isset($filters['machine_ids']) && count($filters['machine_ids']) > 0) {
            $echartDataQuery->whereIn('unit_id', $filters['machine_ids']);
        }

        if (isset($filters['crop_ids']) && count($filters['crop_ids']) > 0) {
            $echartDataQuery->whereIn('sspc.crop_id', $filters['crop_ids']);
        }

        $echartDataQuery->groupBy('status')->orderBy('status');

        return $echartDataQuery->get()->pluck('echart')->toArray();
    }

    /**
     * Get current machine data by organization id.
     *
     * @param number $organizationId
     * @return CurrentMachineData|null
     */
    public static function getByOrganizationId($organizationId)
    {
        return self::where('organization_id', $organizationId)->first();
    }

    /**
     * Get current data geojson by filters
     *
     * @param int $organizationId
     * @param array $filters = array(
     *  'plot_ids'      => int[],
     *  'machine_ids'   => int[],
     *  'farm_ids'      => int[],
     *  'trailers'      => string[],
     *  'statuses'      => string[]
     * 
     * @return CurrentMachineData 
     */
    public static function getFilteredGeoJSON(int $organizationId = null, array $filters = []):CurrentMachineData
    {
        $query = self::select(DB::raw("
            json_build_object(
                'type', 'FeatureCollection',
                'features', coalesce(json_agg(features), '[]'::json)
            ) as geojson
        "))
            ->crossJoin(DB::raw("json_array_elements(su_current_machines_data.geojson->'features') features"));

        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }

        if (isset($filters['plot_ids']) && count($filters['plot_ids']) > 0) {
            $query = parent::queryWhereInWithNullCheck($query, "(features->'properties'->>'plot_id')::integer", $filters['plot_ids']);
        }

        if (isset($filters['farm_ids']) && count($filters['farm_ids']) > 0) {
            $query = parent::queryWhereInWithNullCheck($query, "(features->'properties'->>'farm_id')::integer", $filters['farm_ids']);
        }

        if (isset($filters['trailers']) && count($filters['trailers']) > 0) {
            $query = parent::queryWhereInWithNullCheck($query, "features->'properties'->>'trailer'", $filters['trailers']);
        }

        if (isset($filters['machine_ids']) && count($filters['machine_ids']) > 0) {
            $query->whereIn(DB::raw("features->'properties'->>'unit_id'"), $filters['machine_ids']);
        }

        if (isset($filters['statuses']) && count($filters['statuses']) > 0) {
            $query->whereIn(DB::raw("features->'properties'->>'status'"), $filters['statuses']);
        }

        return $query->first();
    }

}
