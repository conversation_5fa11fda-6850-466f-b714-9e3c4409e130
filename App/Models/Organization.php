<?php

namespace App\Models;

use DB;
use Auth;
use File;
use Config;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class Organization
 * @package App\Models
 *
 * @property integer $id
 * @property string $name
 * @property string $iso_alpha_2_code
 * @property string $address
 * @property string $email
 * @property string $phone
 */
class Organization extends BaseModel
{
	public $timestamps = false;
    protected $table = 'su_organizations';
    protected $primaryKey = 'id';
    protected $fillable = ['name', 'identity_number', 'vat_number', 'iso_alpha_2_code', 'address', 'email', 'phone'];

    protected $hidden = ['users', 'farms', 'createdBy', 'service_provider_id'];

    protected $casts = [
        'users_assigned' => 'array'
    ];

    public function __construct()
    {
        $this->createdBy()->associate(Auth::user());
    }

    public function users()
    {
        return $this->belongsToMany('App\Models\User', 'su_organizations_users');
    }

    public function farms()
    {
        return $this->hasMany('App\Models\Farm')->orderBy('id', 'desc');
    }

    public function stations()
    {
        return $this->hasMany('App\Models\UserStation', 'organization_id', 'id')->orderBy('id', 'desc');
    }

    public function devices()
    {
        return $this->hasMany('App\Models\OrganizationDevice');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'id');
    }

    public function contactsPersons()
    {
        return $this->hasMany('App\Models\Organization\ContactPerson', 'organization_id', 'id')->orderBy('id', 'desc');
    }

    public function addresses()
    {
        return $this->hasMany('App\Models\Organization\Address', 'organization_id', 'id')->orderBy('id', 'desc');
    }

    public static function getListQuery($identityNumber, $organizationManagerId, $serviceManagerId, $user = null): Builder
    {
        $queryList = self::buildListQuery($organizationManagerId, $serviceManagerId, $user);

        if ($identityNumber) {
            $queryList->where('identity_number', trim($identityNumber));
        }

        return $queryList;
    }

    public static function getListQueryWithPlotsInfo($identityNumber, $organizationManagerId, $serviceManagerId, $user = null): Builder
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $queryOrganizationsUsers = self::queryOrganizationsUsers($organizationManagerId, $serviceManagerId, $user);
        $queryOrganizationsUsers->leftJoin('su_farms AS f', 'f.organization_id', '=', 'su_organizations.id')
            ->leftJoin('su_satellite_plots AS p', 'p.farm_id', '=', 'f.id')
            ->addSelect(
                DB::raw("round((sum(p.area * {$areaCoef})::numeric), 2) as plots_area"),
                DB::raw("count(p.gid) as plots_count")
            );

        $queryUsersRoles = self::queryUsersRoles();
        $queryUsersRoles->addSelect('org.plots_count', 'org.plots_area');


        $queryList = self::buildListQuery($organizationManagerId, $serviceManagerId, $user, null, $queryOrganizationsUsers, null, $queryUsersRoles);
        $queryList->addSelect(
            'plots_count', 'plots_area'
        )
            ->groupBy('plots_count', 'plots_area');

        if ($identityNumber) {
            $queryList->where('identity_number', trim($identityNumber));
        }

        return $queryList;
    }

    public static function queryGlobalUsersRoles($serviceProviderId)
    {
        return DB::table('global_users_roles')
            ->select('id','username','old_id','role_id','name','title')
            ->where('service_provider_id', $serviceProviderId);
    }

    public static function queryUsersRoles()
    {
        return DB::table('org')->select(
            'org.id as id',
            'org.name as name',
            'org.identity_number as identity_number',
            'org.vat_number as vat_number',
            'org.active',
            'org.address',
            'org.email',
            'org.phone',
            'org.created_by',
            'iso_alpha_2_code',
            DB::raw("
                json_build_object(
                    'username', global_user_roles.username,
                    'role', json_build_object(
                        'name', global_user_roles.name,
                        'title', global_user_roles.title
                    )
                ) user_role
            ")
            )
            ->join('global_user_roles', 'org.user_id', '=', 'global_user_roles.old_id');
    }

    public static function queryOrganizationsUsers($organizationManagerId, $serviceManagerId, $user = null)
    {
        $query = self::select(
            'su_organizations.id',
            'su_organizations.name',
            'su_organizations.iso_alpha_2_code',
            'su_organizations.address',
            'su_organizations.email',
            'su_organizations.phone',
            'su_organizations.active',
            'su_organizations.created_by',
            'su_organizations.identity_number',
            'su_organizations.vat_number',
            DB::raw("array_agg(distinct ou.user_id) users")
        )
            ->leftJoin('su_organizations_users AS ou', 'ou.organization_id', '=', 'su_organizations.id')
            ->where('su_organizations.active', true)
            ->whereNotNull('su_organizations.identity_number')
            ->where('su_organizations.service_provider_id', Auth::user()->globalUser()->serviceProvider->id);
            
            if($user) {
                $query->where('ou.user_id', $user->id);
            }
            
            $query->groupBy('su_organizations.id')
            ->orderBy('su_organizations.id', 'desc');

        if($organizationManagerId) {
            $query->havingRaw('? = any ("array_agg"(ou.user_id))', [$organizationManagerId]);
        }
        if($serviceManagerId) {
            $query->havingRaw('? = any ("array_agg"(ou.user_id))', [$serviceManagerId]);
        }

        return $query;
    }

    public static function search($value, $limit = 10, $userId = null) {
        $queryString = '%' . $value . '%';

        $organizations = self::select('su_organizations.*')->where(function ($query) use ($queryString) {
            $query->orWhere('name', 'ILIKE', trim($queryString))
                ->orWhere('identity_number', 'ILIKE', trim($queryString))
                ->orWhere('vat_number', 'ILIKE', trim($queryString));
        })->whereNotNull('identity_number')->where('service_provider_id',
            Auth::user()->globalUser()->serviceProvider->id);

        if($userId) {
            $organizations->join('su_organizations_users as sou', 'sou.organization_id', '=', 'su_organizations.id')
                ->where('sou.user_id', $userId);
        }

        return $organizations->distinct()->limit($limit)->get();
    }

    public static function getByIdentityNumber(string $identityNumber): self
    {
        return self::where('identity_number', $identityNumber)->first();
    }

    private static function buildListQuery($organizationManagerId, $serviceManagerId, $user, ?object $queryGlobalUsersRoles = null, ?object $queryOrganizationsUsers = null, ?object $queryOrganizations = null, ?object $queryUsersRoles = null): Builder
    {
        $serviceProviderId = Auth::user()->globalUser()->serviceProvider->id;

        if (!$queryGlobalUsersRoles) {
            $queryGlobalUsersRoles = self::queryGlobalUsersRoles($serviceProviderId);
        }

        if (!$queryOrganizationsUsers) {
            $queryOrganizationsUsers = self::queryOrganizationsUsers($organizationManagerId, $serviceManagerId, $user);
        }

        if (!$queryOrganizations) {
            $queryOrganizations = DB::table('org_users')->select('*', DB::raw("UNNEST(users) user_id"));
        }

        if (!$queryUsersRoles) {
            $queryUsersRoles = self::queryUsersRoles();
        }

        $queryList = self::from('user_roles')
            ->withExpression('global_user_roles', $queryGlobalUsersRoles)
            ->withExpression('org_users', $queryOrganizationsUsers)
            ->withExpression('org', $queryOrganizations)
            ->withExpression('user_roles', $queryUsersRoles)
            ->select(
                'user_roles.id', // organization id
                'name',
                'identity_number',
                'vat_number',
                'active',
                'address',
                'created_by',
                'email',
                'iso_alpha_2_code',
                'phone',
                DB::raw('json_agg(user_role) users_assigned')
            )
            ->groupBy(
                'user_roles.id',
                'name',
                'identity_number',
                'vat_number',
                'active',
                'address',
                'created_by',
                'email',
                'iso_alpha_2_code',
                'phone'
            )
            ->orderBy('user_roles.id', 'desc');

        return $queryList;
    }
}
