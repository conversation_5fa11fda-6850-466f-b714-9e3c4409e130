<?php

namespace App\Models;

use DB;

/**
 * This model describes the 'su_satellite_soil_sample_numbers' table.
 */
class SoilSampleNumber extends BaseModel {

    protected $table = 'su_satellite_soil_sample_numbers';
    public $timestamps = false;

    public static function findOrderId($barcode)
    {
		$result = self::select([
        'sopr.order_id',
        'sopr.id',
        'ssp.sample_id'
        ])
        ->join('su_satellite_soil_points as ssp', 'ssp.gid', '=', 'su_satellite_soil_sample_numbers.gid')
        ->join('su_satellite_orders_plots_rel as sopr', 'sopr.id', '=', 'ssp.sopr_id')
        ->where('su_satellite_soil_sample_numbers.sample_number', $barcode)
        ->first();

        if($result){
        	return $result; 
        }

        return null;
    }

    public static function updateBarcodeRelation($arrBarcodes)
    {	
    	foreach ($arrBarcodes as $key => $value) {
    	
    		self::where('sample_number', '=', $value)
                ->update(array('analyzes_data_id' => $key));            
    	}
    }

    public static function updateStageBarcodeRelation($arrBarcodes)
    {   
        foreach ($arrBarcodes as $key => $value) {
        
            self::where('sample_number', '=', $value)
                ->update(array('analyzes_data_stage_id' => $key));            
        }
    }

    public static function loadForTemplateQuery()
    {
        $query = self::select([
            'su_satellite_soil_sample_numbers.sample_number as barcode',
            'ssp.sample_id',
            'sp.name'
            ])
            ->join('su_satellite_soil_points as ssp', 'ssp.gid', '=', 'su_satellite_soil_sample_numbers.gid')
            ->join('su_satellite_orders_plots_rel as sopr', 'sopr.id', '=', 'ssp.sopr_id')
            ->join('su_satellite_plots as sp', 'sp.gid', '=', 'sopr.plot_id');

        return $query;
    }

    public static function loadForTemplate($arrBarcodesCouch)
    {
        if(!$arrBarcodesCouch) {
            return [];
        }

        $query = self::loadForTemplateQuery();
        $query->whereIn('su_satellite_soil_sample_numbers.sample_number', $arrBarcodesCouch);
    
        return $query->get()->toArray();
    }

    public static function joinBarcodesWith($jsonDocs, $sort)
    {
        $sql = 'WITH batch_data AS (
        SELECT
            *
        FROM
            json_to_recordset (
                :jsonDocs
            ) AS  (
                "batch" TEXT,
                "samples" json,
                "dateStamp" TEXT,
                "clientName" TEXT
            )
        ) SELECT
            batch,
            count(barcode),
            "dateStamp",
            string_agg(DISTINCT(sopr.order_id)::text, \', \') as order,
	        string_agg(DISTINCT(o.name)::text, \', \') as organization,
	        "clientName"
        FROM
            batch_data,
            json_to_recordset (samples) AS barcode ("barcode" TEXT)
        LEFT JOIN su_satellite_soil_sample_numbers ssn ON ssn.sample_number = barcode
        LEFT JOIN su_satellite_soil_points ssp ON ssp.gid = ssn.gid
        LEFT JOIN su_satellite_orders_plots_rel sopr ON sopr. ID = ssp.sopr_id
        LEFT JOIN su_satellite_orders so ON so. ID = sopr.order_id
        LEFT JOIN su_organizations o ON o. ID = so.organization_id
        GROUP BY
            batch,
            "dateStamp",
	        "clientName"
	    ORDER BY "dateStamp" ' . $sort;
        return DB::select($sql, ['jsonDocs' => $jsonDocs]);
    }

    public static function dbCheckedBarcodes($filter)
    {
        $barcodeSampling = $filter['barcodeSampling'];
        $orderId = $filter['orderId'];
        $plotName = $filter['plotName'];

        $queryDataDb = SoilSampleNumber::queryDataDbBarcode();
        //filter
        if($barcodeSampling) {
            $queryDataDb->where('su_satellite_soil_sample_numbers.sample_number', $barcodeSampling);
        }
        if($orderId) {
            $queryDataDb->where('sopr.order_id', $orderId);
        }
        if($plotName) {
            $queryDataDb->where('sp.name', 'ilike', trim('%' . $plotName . '%'));
        }

        return $queryDataDb;
    }

    public static function joinCheckedBarcodes($jsonDocs, $filter)
    {
        $queryDataDb = SoilSampleNumber::dbCheckedBarcodes($filter);

        $sqlBatchData = 'SELECT batch, samples, "clientName" FROM
        json_to_recordset (
                \''.$jsonDocs.'\'
        ) AS  (
                "batch" TEXT,
                "samples" json,
                "clientName" TEXT
        )';

        $sqlSamplesData = 'SELECT samples.barcode as barcode_lab, batch, samples.labnumber, "dateStamp", "clientName" FROM 
			batch_data, json_to_recordset (samples) AS samples ("barcode" TEXT, labnumber TEXT, "dateStamp" TEXT)';

        $queryCheckedBarcodes = DB::table('data_db')
        ->withExpression('data_db', $queryDataDb)
        ->withExpression('batch_data', $sqlBatchData)
        ->withExpression('samples_data', $sqlSamplesData)
        ->join('samples_data', 'data_db.barcode_sampling', '=', 'samples_data.barcode_lab', 'full')
        ->select(
            'batch',
            'clientName',
            DB::raw('date(abstime("dateStamp"::INTEGER)) as "dateStamp"'),
            'labnumber',
            'barcode_lab',
            'order_id as order',
            'year',
            'plot_name',
            'sample_id',
            'barcode_sampling',
            'sync_date',
            'organization',
            'sampler_name',
            'gid'
        );

        $batch = $filter['batch'];
        $barcodeLab = $filter['barcodeLab'];
        //filter
        if($batch) {
            $queryCheckedBarcodes->where('batch', $batch);
        }
        if($barcodeLab) {
            $queryCheckedBarcodes->where('barcode_lab', $barcodeLab);
        }

        return $queryCheckedBarcodes->get();
    }

    public static function barcodeExists($barcode)
    {
        return SoilSampleNumber::where('sample_number', '=', $barcode)->exists();
    }

    public static function updateBarcode($gid, $barcode)
    {
        SoilSampleNumber::where('gid', '=', $gid)->update(['sample_number' => $barcode]);
    }

    public static function queryDataDbBarcode()
    {
        $queryDataDb = self::select(
            'sopr.order_id',
            'so.year',
            'sopr.sync_date',
            'u.name as sampler_name',
            'o.name as organization',
            'sp.name as plot_name',
            'su_satellite_soil_sample_numbers.gid',
            'su_satellite_soil_sample_numbers.sample_number as barcode_sampling',
            'ssp.sample_id')
            ->leftJoin('su_satellite_soil_points AS ssp', 'ssp.gid', '=', 'su_satellite_soil_sample_numbers.gid')
            ->leftJoin('su_satellite_orders_plots_rel AS sopr', 'sopr.id', '=', 'ssp.sopr_id')
            ->leftJoin('su_satellite_orders AS so', 'so.id', '=', 'sopr.order_id')
            ->leftJoin('su_organizations AS o', 'o.id', '=', 'so.organization_id')
            ->leftJoin('su_users AS u', 'u.id', '=', 'sopr.sampler_id')
            ->leftJoin('su_satellite_plots AS sp', 'sp.gid', '=', 'sopr.plot_id')
            ->groupBy('sopr.order_id', 'so.year', 'sopr.sync_date', 'u.name', 'o.name', 'sp.name', 'su_satellite_soil_sample_numbers.gid', 'su_satellite_soil_sample_numbers.sample_number', 'ssp.sample_id');

        return $queryDataDb;
    }
}
