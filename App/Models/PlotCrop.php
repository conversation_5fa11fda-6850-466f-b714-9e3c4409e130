<?php

namespace App\Models;
use DB;

class PlotCrop extends BaseModel
{
    public $timestamps = false;

	/**
     * Table name.
     *
     * @var string
     */
	protected $table = 'su_satellite_plots_crops';

	/**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'farm_year' => 'object',
    ];

    public static function setIrrigatedBy(array $param, $irrigated)
    {
        if(isset($param['id'])) {
            self::where('id', '=', $param['id'])->update(['irrigated' => $irrigated]);
        }

        if(isset($param['plot_ids'])) {
            self::whereIn('plot_id', $param['plot_ids'])->update(['irrigated' => $irrigated]);
        }
    }

    /**
     * Get crop history by plot
     * 
     * @param int $plotId   The plot's id
     * @param string $lang  The crop language (e.g. 'en', 'bg' etc.)
     * 
     * @return array
     */
    public static function getCropHistoryByPlot(int $plotId, string $lang = 'en'): array
    {
        return self::from('su_satellite_plots_crops as spc')
            ->select(
                'scc.id as crop_id',
                DB::raw("scc.crop_name_" . $lang . " as crop"),
                DB::raw("
                    JSON_BUILD_OBJECT(
                        'id', spc.year,
                        'year', spc.year,
                        'title', spc.year,
                        'farming_year', (spc.year - 1) || '/' || spc.year,
                        'from_date', spc.from_date,
                        'to_date', spc.to_date,
                        'default', spc.year = date_part('year', now()),
                        'is_primary', spc.is_primary
                    ) as farm_year
                ")
            )
            ->join('su_crop_codes as scc', 'scc.id', '=', 'spc.crop_id')
            ->where('spc.plot_id', $plotId)
            ->get()->toArray();
    }
}

?>