<?php

namespace App\Models;

use View;
use Config;

class Layer extends BaseModel
{
    const LAYER_TYPE_SATELLITE_WORK = 8;
    const LAYER_TYPE_SOIL_GRID = 9;
    const LAYER_TYPE_SOIL_POINTS = 10;
    const LAYER_TYPE_TRACKS = 11;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_users_layers';
    
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    
    /**
     * Get the user for the file.
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'id', 'user_id');
    }
}
