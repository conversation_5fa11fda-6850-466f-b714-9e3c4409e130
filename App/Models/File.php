<?php

namespace App\Models;

class File extends BaseModel
{
    const ERROR_RUNTIME = 7;
    const LOADING_FILE_NOW = 19;
    const LOADING_FILE = 0;
    const SUCCESSFULLY_TREATED = 1;
    const ERROR_INVALID_SHAPE = 2;
    const ERROR_INVALID_DBF = 3;
    const ERROR_INVALID_ARCHIVE = 4;
    const ERROR_INVALID_GEOMETRY = 5;
    const ERROR_INVALID_ISAK_FILE = 6;
    const ERROR_INVALID_TABLE_STRUCTURE = 8;
    const ERROR_INVALID_FILE_DATA = 9;
    const ERROR_WAITING_DEFINITION = 10;
    const ERROR_WAITING_COPYING = 11;
    const ERROR_INVALID_CRS = 12;
    const ERROR_NOT_ALLOWED_ADDING = 13;
    const ERROR_INCORRECT_ENCODING = 14;
    const ERROR_MISSING_COLUMN = 15;
    const ERROR_INCORRECT_ENCODING_FIELD = 16;
    const PARTIALLY_PROCESSED = 17;
    const INCONSISTENT_FILE_TYPE = 18;
    const ERROR_READING_SHAPE_OBJECT = 20;
    const ERROR_GEOMETRY_COLLECTION = 21;
    const ERROR_OGRINFO = 22;

    
    //Device Types
    const DEVICE_UNKNOWN = 0;
    const DEVICE_OSZ = 3;

    //File types
    const SHP = 'shp';
    const KML = 'kml';
    const KMZ = 'kmz';
    const ZIP = 'application/zip';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_users_files';
    
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    
    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = ['errors'];
    
    /**
     * Get the user for the file.
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'id', 'user_id');
    }

    public function farm() {
        return $this->belongsTo('App\Models\Farm', 'id', 'farm_id');
    }
}
