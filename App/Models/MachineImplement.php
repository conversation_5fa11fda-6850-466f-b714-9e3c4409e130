<?php

namespace App\Models;

use phpseclib\Math\BigInteger;
use Illuminate\Database\Eloquent\Builder;


/**
 * Class IrrigationPlatform
 * @package App
 *
 * @property BigInteger $contract_id
 * @property BigInteger $organization_id
 * @property string $name
 * @property float $width
 */
class MachineImplement extends BaseModel
{
    const STATUS_ACTIVE = 'Active';
    const STATUS_INACTIVE = 'Inactive';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_machines_implements';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['organization_id', 'name', 'width', 'status'];


    /**
     * @param int $organizationId
     * @param array $filters = array(
     *      'name' => string
     * )
     * 
     * @return Builder $query
     */
    public static function getFilteredMachineImplementsQuery(int $organizationId, array $filters = []): Builder
    {
        $query = MachineImplement::where('organization_id', $organizationId);

        if (isset($filters['name'])) {
            $searchStr = '%' . $filters['name'] . '%';
            $query->where('name', 'ILIKE', $searchStr);
        }

        return $query;
    }
}
