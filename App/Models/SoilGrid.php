<?php

namespace App\Models;

use DB;
use File;
use Config;
use App\Models\OrderPlotRel;

class SoilGrid extends BaseModel
{
	protected $table = 'su_satellite_soil_grid';
	protected $primaryKey = 'gid';
	public $timestamps = false;

    /**
     * @param array $soprIds
     * @return mixed
     */
    public static function getBySoprId(array $soprIds) {
        return self::whereIn('sopr_id', $soprIds);
    }

    public static function generatePng(int $soprId, int $protocolId) {
        $plot = OrderPlotRel::findOrFail($soprId)->plot;
        $outDir = storage_path('plots' . DIRECTORY_SEPARATOR . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . $plot->farm_id . DIRECTORY_SEPARATOR);
        $gridOutName = $outDir . "plot_grid_" . $plot->gid . "_" . $protocolId . ".png";

        if (!is_dir($outDir)) {
            mkdir($outDir, 0755, true);
        }

        $plotExtent = DB::table('su_satellite_plots as sp')
        ->select(DB::raw('ST_SetSRID(ST_Extent(sp.geom),' . Config::get('globals.DEFAULT_DB_CRS') . ') AS poly'))
        ->join('su_satellite_orders_plots_rel as sopr', 'sopr.plot_id', '=', 'sp.gid')
        ->join('su_satellite_soil_grid as ssg', 'ssg.sopr_id', '=', 'sopr.id')
        ->where('ssg.sopr_id', $soprId);

        $raster = DB::table('plot_extent')
        ->select(DB::raw('
            ST_AsRaster(
                plot_extent.poly,
                (400 * ((ST_XMax(plot_extent.poly) - ST_XMin(plot_extent.poly)) / (ST_YMax(plot_extent.poly) - ST_YMin(plot_extent.poly)))::numeric)::integer,
                400,
                ARRAY[\'8BUI\', \'8BUI\', \'8BUI\'],
                ARRAY[0,0,0],
                ARRAY[0,0,0]
            ) AS rast
        '));

        $finalQuery = DB::table(DB::raw('raster, su_satellite_soil_grid as ssg'))
        ->select(DB::raw('
            ST_AsPNG(
                st_union(
                    ST_AsRaster(
                        ST_BUFFER(st_boundary(ssg.geom),2),
                        raster.rast,
                        ARRAY[\'8BUI\', \'8BUI\', \'8BUI\'],
                        ARRAY[0, 131, 201],
                        ARRAY[255,255,255]
                    )
                )
            ) AS png
        '))
        ->where('ssg.sopr_id', $soprId);

        $result = DB::table('final_query')
        ->withExpression('plot_extent', $plotExtent)
        ->withExpression('raster', $raster)
        ->withExpression('final_query', $finalQuery);

        $png = stream_get_contents($result->first()->png);
        File::put($gridOutName, $png);

        return $gridOutName;
    }

}
?>
