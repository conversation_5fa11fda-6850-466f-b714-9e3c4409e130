<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Helpers\Helper;
use Carbon\Carbon;
use Dompdf\Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class Plot extends BaseModel
{
    use HasFactory;


    const LAYER_TYPE = Layer::LAYER_TYPE_SATELLITE_WORK;
    const CREATED_AT = 'upload_date';
    const UPDATED_AT = null;
    const MIN_AREA = 200;

    protected $table = 'su_satellite_plots';
    protected $primaryKey = 'gid';
    public $timestamps = true;
    protected $fillable = ['geom', 'area', 'user_id'];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'geom_json' => 'object',
        'GeoJSON' => 'array',
        'plot_area' => 'float',
        'overlap_percentage' => 'float',
        'days_since_sowing' => 'int',
        'area' => 'float',
        'avg_index' => 'float',
        'avg_index_diff' => 'float',
        'overlaps_with' => 'object',
        'orders' => 'array',
        'farm' => 'object',
        'name_uuids' => 'object',
        'value' => 'float',
        'itemStyle' => 'object',
        'soil_grid_points_coordinates' => 'array'
    ];

    public function orders()
    {
        return $this->belongsToMany('App\Models\Order', 'su_satellite_orders_plots_rel', 'plot_id', 'order_id')
            ->withPivot('price', 'note');
    }

    public function orderVra()
    {
        return $this->hasMany('App\Models\OrderVra.php', 'plot_id', 'gid');
    }

    public function orderSoilVra()
    {
        return $this->hasMany('App\Models\OrderSoilVra.php', 'plot_id', 'gid');
    }

    public function layers()
    {
        return $this->hasMany('App\Models\LayerPlot', 'plot_id', 'gid');
    }

    public function crops()
    {
        return $this->belongsToMany('App\Models\Crop', 'su_satellite_plots_crops', 'plot_id', 'crop_id')
            ->withPivot('id', 'year', 'from_date', 'to_date', 'is_primary', 'hybrid_id', 'emergence_date',
                'phenophase_date', 'gdd_phenophase_id', 'irrigated');
    }

    public function station()
    {
        return $this->hasOne('App\Models\UserStation', 'id', 'station_id');
    }

    public function farm()
    {
        return $this->belongsTo(Farm::class, 'farm_id', 'id');
    }

    public function files()
    {
        return $this->hasMany(PlotFile::class, 'plot_id', 'gid');
    }

    public static function getInfoByGids($gids, User $user)
    {
        return self::select(['sp.gid as id', 'sp.name', 'sp.area', 'sc.crop_name as cropName'])
            ->from('su_satellite_plots as sp')
            ->join('su_farms as f', 'f.id', '=', 'sp.farm_id')
            ->join('su_farms_users as fu', 'fu.farm_id', '=', 'f.id')
            ->join('su_users as u', 'u.id', '=', 'fu.user_id')
            ->join('su_organizations as o', 'o.id', '=', 'f.organization_id')
            ->join('su_satellite_plots_crops as spc', 'spc.plot_id', '=', 'sp.gid')
            ->join('su_crop_codes as sc', 'sc.id', '=', 'spc.crop_id')
            ->whereIn('gid', $gids)
            ->where('u.id', $user->id)
            ->where('o.id', $user->lastChosenOrganization->id);
    }

    /**
     * Add 'where' statements in the query Builder object
     * @param object $query query Builder object
     * @param array $params
     * @return query Builder object
     */
    public function scopeFilter($query, array $params)
    {

        if (isset($params['name']) && trim($params['name']) !== '') {
            $query->where('name', 'ILIKE', trim('%' . $params['name'] . '%'));
        }

        if (isset($params['culture']) && trim($params['culture']) !== '') {
            $query->where('culture', '=', trim($params['culture']));
        }

        return $query;
    }

    public static function getOrderDetailsQuery(User $user, $plotId, $date, $satelliteType)
    {
        return self::select([
            'sof.path',
            'su_satellite_plots.gid',
            'sopr.order_id',
            'slp.date',
            'slp.relative_stats',
            'slp.id',
            'su_satellite_plots.area',
            DB::raw("st_astext((st_dump(su_satellite_plots.geom)).geom) as geom")
        ])
            ->join('su_satellite_layers_plots as slp', 'su_satellite_plots.gid', '=', 'slp.plot_id')
            ->join('su_satellite_orders_plots_rel as sopr', 'su_satellite_plots.gid', '=', 'sopr.plot_id')
            ->join('su_satellite_orders as so', function ($join) {
                $join->on('so.id', '=', 'sopr.order_id')->on('slp.order_id', '=', 'so.id');
            })
            ->join('su_satellite_orders_files as sof', 'sof.order_id', '=', 'sopr.order_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', $user->id)
            ->where('f.organization_id', $user->lastChosenOrganization->id)
            ->where('su_satellite_plots.gid', $plotId)
            ->where('slp.date', $date)
            ->where('sof.date', $date)
            ->where('slp.type', 'index')
            ->where('sof.layer_type', 'index')
            ->where('slp.satellite_type', $satelliteType)
            ->where('sof.satellite_type', $satelliteType)
            ->where('so.status', 'processed')
            ->where('so.type', 'index');
    }

    public function getIndexDate(
        User $user,
        $plotId,
        $classes,
        $date,
        $satelliteType
    )
    {
        $plots = self::getOrderDetailsQuery($user, $plotId, $date, $satelliteType)->get();

        if (empty($plots[0])) {
            throw new \Exception('Няма поръчка с tiff');
        }

        $stats = $this->getAllIndexTiffStats($user, $plots, $satelliteType, $classes);


        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $dataArr = [['Index', $areaLabel, ['role' => 'style'], ['role' => 'tooltip']]];
        $dataArr[] = $stats;


        return $dataArr;
    }

    public function getAllIndexTiffStats(User $user, $plots, $satelliteType, $classes)
    {
        $imgPath = Config::get('globals.PROCESSED_ORDERS_PATH') . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR;

        $wrapFilePath = "{$satelliteType}/" . $plots[0]->date . '_' . $plots[0]->order_id . '_' . $plots[0]->gid;
        $userImagePath = $imgPath . $wrapFilePath;

        $vraWrapFile = $wrapFilePath . '_wrap.tif';
        $vraWrapRemapFile = $wrapFilePath . '_wrap_remap.tif';

        if (file_exists($imgPath . $vraWrapFile)) {
            unlink($imgPath . $vraWrapFile);
        }
        if (file_exists($imgPath . $vraWrapRemapFile)) {
            unlink($imgPath . $vraWrapRemapFile);
        }

        $tileImg = $plots[0]->path;
        $polygonWKT = $plots[0]->geom;
        $defaultProj = Config::get('globals.DEFAULT_DB_CRS');
        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');

        $scale = collect($classes);
        $scale = $scale->keys();
        $scale = json_encode($scale, 0);

        $area = $plots[0]->area;

        $command = "{$pythonPath} -W ignore {$scriptsPath}index_plot_stats.py {$tileImg} {$userImagePath} \"{$polygonWKT}\"  \"{$scale}\" {$area} --proj={$defaultProj}";

        if (!file_exists($tileImg)) {
            throw new \Exception('Грешен път на tiff');
        }

        $output = array();
        exec($command . " 2>&1", $output);
        $stats = json_decode($output[0], true);
        $stats['path'] = $vraWrapRemapFile;

        if (empty($stats)) {
            throw new \Exception('Грешна обработка на  tiff');
        }

        return $stats;
    }

    public static function getSoilOrderDetailsQuery(User $user, $plotId, $date, $element, $organizationId = null)
    {
        if (!$organizationId) {
            $organizationId = $user->lastChosenOrganization->id;
        }

        return self::select([
            'slpf.path',
            'su_satellite_plots.gid',
            'su_satellite_plots.area',
            'sopr.order_id',
            'slp.date',
            'slp.relative_stats',
            'slp.id',
            'slp.probe',
            DB::raw("st_astext((st_dump(su_satellite_plots.geom)).geom) as geom"),
            DB::raw("split_part(slp.layer_name, '_', 4) AS compound"),
            DB::raw("slp.stats_type AS type"),
            DB::raw("slp.element AS soil_element")
        ])
            ->join('su_satellite_layers_plots as slp', 'su_satellite_plots.gid', '=', 'slp.plot_id')
            ->join('su_satellite_orders_plots_rel as sopr', 'su_satellite_plots.gid', '=', 'sopr.plot_id')
            ->join('su_satellite_orders as so', function ($join) {
                $join->on('so.id', '=', 'sopr.order_id')->on('slp.order_id', '=', 'so.id');
            })
            ->join('su_satellite_layers_plots_files as slpf', 'slpf.layer_plot_id', '=', 'slp.id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', $user->id)
            ->where('f.organization_id', $organizationId)
            ->where('su_satellite_plots.gid', $plotId)
            ->where('slp.date', $date)
            ->where('slp.type', 'soil')
            ->where('slpf.type', 'TIFF')
            ->where('slp.element', $element)
            ->where('sopr.soil_sample_status', 'processed')
            ->where('so.type', 'soil');
    }

    public function makeSoilPlotStats($tileImg, $scaleType, $scale, $plotArea)
    {
        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');

        $command = "{$pythonPath} {$scriptsPath}soil_plot_stats.py {$tileImg} $scaleType \"{$scale}\" {$plotArea}";

        $output = [];
        exec($command . " 2>&1", $output);
        try {
            //checking if the output is a valid json.
            json_decode($output[0]);
        } catch (Exception $e) {
            throw $e;
        }
        return $output[0];
    }

    public function getAllSoilTiffStats(User $user, $plots, $classes, $soilElement)
    {
        $type = 'summarized';

        $tifPath = $plots[0]->path;

        $tifShortPath = Str::after($tifPath, strtoupper(Config::get('globals.MACHINE')) . DIRECTORY_SEPARATOR);

        $scale = collect($classes);
        $scale = $scale->keys();

        $plotStats = $this->makeSoilPlotStats($tifPath, $type, json_encode($scale, 0), $plots[0]->area);

        $stats = json_decode($plotStats, true);
        $stats['path'] = $tifShortPath;

        return $stats;
    }

    public function getSoilDate(User $user, $plotId, $classes, $date, $soilElement, $organizationId = null)
    {
        $plots = self::getSoilOrderDetailsQuery($user, $plotId, $date, $soilElement, $organizationId)->get();

        if (empty($plots[0])) {
            throw new \Exception('Няма поръчка с tiff');
        }

        $stats = json_decode($plots[0]->relative_stats);
        if (!$stats) {
            $stats = $this->getAllSoilTiffStats($user, $plots, $classes, $soilElement);
        }

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $dataArr = [['Index', $areaLabel, ['role' => 'style'], ['role' => 'tooltip']]];
        $dataRange = [];
        $indexMap = $stats;
        $dataArr[] = $stats;


        return $dataArr;
    }

    public function getSoilDateVectorized(User $user, $plotId, $classes, $rates, $date, $element, $product)
    {
        $plots = self::getSoilOrderDetailsQuery($user, $plotId, $date, $element)->get();

        if (empty($plots[0])) {
            throw new \Exception('Няма поръчка с tiff');
        }

        $plot = $plots[0];

        //$json = $this->polygonizeVraMap();
        $json = $this->polygonizeSoilVraMap($user, $plot, $date, $element, $classes, $rates, $product);

        return $json;
    }

    public function removeDuplicateGeoms($farmId)
    {
        $table = $this->table;
        $pkey = $this->primaryKey;

        $this->whereIn($pkey, function ($query) use ($table, $pkey, $farmId) {
            $query->select("b.{$pkey}")
                ->from(DB::raw("{$table} AS a, {$table} AS b"))
                ->whereRaw("a.{$pkey} < b.{$pkey}")
                ->where("a.farm_id", $farmId)
                ->where("b.farm_id", $farmId)
                ->whereRaw("ST_Equals(a.geom, b.geom)");
        })->delete();
    }

    /**
     * Get crop query
     *
     * @param User $user
     * @param int $year
     * @param array $farmIds
     * @param array $plotIds
     *
     * @return Illuminate\Database\Eloquent\Builder
     */
    public function cropQuery(User $user, int $year, array $farmIds, array $plotIds): Builder
    {
        $query = self::from('su_satellite_plots AS sp')
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'sp.gid')
            ->join('su_satellite_orders AS so', 'so.id', '=', 'sopr.order_id')
            ->join('su_farms_users AS fu', 'fu.farm_id', '=', 'sp.farm_id')
            ->join('su_farms AS f', 'f.id', '=', 'fu.farm_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function($join) use ($year) {
                $join->on('spc.plot_id', '=', 'sp.gid');
                $join->on('spc.is_primary', '=', DB::raw('true'));
                $join->on('spc.year', '=', DB::raw($year));
            })
            ->leftJoin('su_crop_codes AS cc', 'cc.id', '=', 'spc.crop_id')
            ->where([
                ['fu.user_id',  $user->id],
                ['f.organization_id', $user->lastChosenOrganization->id],
                ['so.year', $year],
                ['so.type', 'index']
            ])
            ->whereIn('so.status', ['processed', 'no_tile', 'paid', 'processing']);

        if (isset($farmIds) && count($farmIds) > 0) {
            $query->whereIn('sp.farm_id', $farmIds);
        }

        if (isset($plotIds) && count($plotIds) > 0) {
            $query->whereIn('sp.gid', $plotIds);
        }

        return $query;
    }

    public static function getHasWaterPounds($date, $plotId = null)
    {
        if (!$plotId || !$date) {
            #TODO Log error
            echo("Error:No plotId or date");
            return false;
        }

        try {

            $waterIntersection = self::select(DB::raw("su_satellite_plots.gid"))
                ->join('su_satellite_water_pounds AS wp',
                    DB::raw('ST_Intersects(ST_Transform(wp.geom, ' . Config::get('globals.DEFAULT_DB_CRS') . '), su_satellite_plots.geom)'),
                    '=', DB::raw('true'))
                ->where('su_satellite_plots.gid', $plotId)
                ->where('wp.date', $date)
                ->groupBy('su_satellite_plots.gid')
                ->first();

            if ($waterIntersection) {
                return true;
            }

            return false;

        } catch (\Exception $e) {
            echo("Has Water pounds Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update Last Marker Date
     * @param int $userId
     * @param numeric $longitude
     * @param numeric $latitude
     * @param string $date
     * @return void
     */
    public static function updateLastMarkerDate(User $user, $longitude, $latitude, $date)
    {
        $arrPlots = self::getPlotsInLocation($user, $longitude, $latitude);

        self::whereIn('gid', $arrPlots)->update(array('last_marker_date' => $date));
    }

    /**
     * Get Plots In Location
     * @param int $userId
     * @param numeric $longitude
     * @param numeric $latitude
     * @return array plot gids
     */
    public static function getPlotsInLocation(User $user, $longitude, $latitude)
    {
        $arrPlots = self::select(
            'su_satellite_plots.gid'
        )
            ->join('su_farms_users as uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', $user->id)
            ->where('f.organization_id', $user->lastChosenOrganization->id)
            ->where(DB::raw('ST_Contains(su_satellite_plots.geom, ST_Transform(ST_SetSRID(ST_MakePoint(' . $longitude . ', ' . $latitude . '), 900913), ' . Config::get("globals.DEFAULT_DB_CRS") . '))'),
                '=', true)
            ->get()->toArray();

        return array_column($arrPlots, "gid");
    }

    public static function findMeteoLocation($gid, Organization $organization = null)
    {
        $query = self::select([
            'lml.gid AS zone_gid',
            DB::raw('ST_X(ST_Transform (ST_Centroid(lml.geom), 4326)) AS longitude'),
            DB::raw('ST_Y(ST_Transform (ST_Centroid(lml.geom), 4326)) AS latitude'),
            DB::raw('ST_Distance(lml.geom, su_satellite_plots.geom) as distance')
        ])
            ->join('layer_meteo_locations AS lml', 'lml.gid', '=', 'su_satellite_plots.meteo_location_id')
            ->where('su_satellite_plots.gid', $gid);

        if (!empty($organization)) {
            $query->join('su_farms AS f', 'f.id', '=', 'su_satellite_plots.farm_id')
                ->where('f.organization_id', $organization->id);
        }

        $result = $query->first();

        return $result;
    }

    private function polygonizeSoilVraMap(User $user, $plot, $date, $soilElement, $classes, $rates, $product)
    {
        $plotName = Helper::convertToLatin($plot->name);
        $plotName = preg_replace('/[^a-zA-Z0-9\-\_]/', '', $plotName);

        $type = 'absolute';

        $tiffPath = Config::get('globals.PROCESSED_ORDERS_PATH') .
            Config::get('globals.MACHINE') .
            DIRECTORY_SEPARATOR .
            $user->id .
            DIRECTORY_SEPARATOR .
            'soils' .
            DIRECTORY_SEPARATOR .
            $plot->date .
            DIRECTORY_SEPARATOR .
            $plot->gid . '_' . $plot->date . '_' . $soilElement . '.tif';

        if ($plot->probe) {
            $tiffPath = Config::get('globals.PROCESSED_ORDERS_PATH') .
                Config::get('globals.MACHINE') .
                DIRECTORY_SEPARATOR .
                $user->id .
                DIRECTORY_SEPARATOR .
                'soils' .
                DIRECTORY_SEPARATOR .
                $plot->date .
                DIRECTORY_SEPARATOR .
                $plot->gid . '_' . $plot->date . '_probe_' . $plot->probe . '_' . $soilElement . '.tif';
        }

        $reclassifiedTiffPath = Config::get('globals.PROCESSED_ORDERS_PATH') .
            Config::get('globals.MACHINE') .
            DIRECTORY_SEPARATOR .
            $user->id .
            DIRECTORY_SEPARATOR .
            'soils' .
            DIRECTORY_SEPARATOR .
            $plot->date .
            DIRECTORY_SEPARATOR .
            $plot->gid . '_' . $plot->date . '_' . $soilElement . '_rec.tif';

        $vraPath = Config::get('globals.PROCESSED_ORDERS_PATH') .
            Config::get('globals.MACHINE') .
            DIRECTORY_SEPARATOR .
            $user->id .
            DIRECTORY_SEPARATOR .
            'soils' .
            DIRECTORY_SEPARATOR .
            $plot->date .
            DIRECTORY_SEPARATOR .
            $plot->gid . '_' . $plot->date . '_' . $soilElement . '_vra';


        $rangeParams = "<-998, ";
        $normaParams = "-999, ";
        $maxRanges = count($classes);

        $count = 0;
        foreach ($classes as $key => $range) {
            $rangeParams .= "<";
            $rangeParams .= $range[1];
            if ($count < $maxRanges - 1) {
                $rangeParams .= ", ";
            }
            $count++;
        }

        foreach ($rates as $key => $norm) {
            if ((int)$product > 0) {
                $normaParams .= ceil((int)$norm * 100 / $product);
            } else {
                $normaParams .= $norm;
            }

            if ($key < $maxRanges - 1) {
                $normaParams .= ", ";
            }
        }

        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');
        $gdalBinPath = Config::get('globals.GDAL_BIN_PATH');

        $cmd = "{$pythonPath} {$scriptsPath}gdal_reclassify.py {$tiffPath} {$reclassifiedTiffPath} -c \"{$rangeParams}\" -r \"{$normaParams}\" -d -999 -n true -p \"COMPRESS=LZW\"";
        exec($cmd);

        $cmd = "{$pythonPath} {$gdalBinPath}gdal_polygonize.py {$reclassifiedTiffPath} -f \"GeoJSON\" {$vraPath} vra_{$plot->gid}_{$date}_{$soilElement}";
        exec($cmd);

        $json = file_get_contents($vraPath);

        $deleteRec = "rm {$reclassifiedTiffPath}";
        exec($deleteRec);

        $deleteJson = "rm {$vraPath}";
        exec($deleteJson);

        return $json;
    }

    public function polygonizeVraMap($tiffPath, $plot, $date, $element, $classes, $rates, $product)
    {
        $tiffPath = Config::get('globals.PROCESSED_ORDERS_PATH') . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . $tiffPath;
        $reclassifiedTiffPath = Str::before($tiffPath, '.') . '_rec.' . Str::after($tiffPath, '.');
        $vraPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'tmp_vra_' . $plot->gid . '_' . $date . '_' . $element;

        $rangeParams = "<-998, ";
        $normaParams = "-999, ";
        $maxRanges = count($classes);

        $count = 0;
        foreach ($classes as $key => $range) {
            $rangeParams .= "<";
            $rangeParams .= $range[1];
            if ($count < $maxRanges - 1) {
                $rangeParams .= ", ";
            }
            $count++;
        }

        foreach ($rates as $key => $norm) {
            if ((int)$product > 0) {
                $normaParams .= ceil((int)$norm * 100 / $product);
            } else {
                $normaParams .= $norm;
            }

            if ($key < $maxRanges - 1) {
                $normaParams .= ", ";
            }
        }

        $pythonPath = Config::get('globals.PYTHON_PATH');
        $scriptsPath = Config::get('globals.SCRIPTS_PATH');
        $gdalBinPath = Config::get('globals.GDAL_BIN_REAL_PATH');

        $cmd = "{$pythonPath} {$scriptsPath}gdal_reclassify.py {$tiffPath} {$reclassifiedTiffPath} -c \"{$rangeParams}\" -r \"{$normaParams}\" -d -999 -n true -p \"COMPRESS=LZW\"";
        exec($cmd . " 2>&1", $output1);

        $cmd = "{$pythonPath} {$gdalBinPath}gdal_polygonize.py {$reclassifiedTiffPath} -f \"GeoJSON\" {$vraPath} vra_{$plot->gid}_{$date}_{$element}";
        exec($cmd . " 2>&1", $output2);

        $json = file_get_contents($vraPath);

        $deleteRec = "rm {$reclassifiedTiffPath}";
        exec($deleteRec);

        $deleteJson = "rm {$vraPath}";
        exec($deleteJson);

        return $json;
    }

    public static function getPlotGeoJson($plotId, $resultProj = 4326)
    {
        return DB::select("
        SELECT row_to_json(fc) AS geojson
        FROM (
 		    SELECT 'FeatureCollection' As type, array_to_json(array_agg(f)) As features
            FROM (
                SELECT 'Feature' As type,
                ST_AsGeoJSON(ST_Transform(plots.geom, {$resultProj}))::json As geometry,
                row_to_json((
                    SELECT l FROM (SELECT gid AS feat_id) As l
                )) As properties
                FROM su_satellite_plots As plots where plots.gid = {$plotId}) As f) As fc;
    ")[0]->geojson;
    }

    public static function getClosestStationId($plot)
    {
        $result = self::select(
            DB::raw("us.id as station_id"),
            DB::raw("ST_Distance(us.geom, su_satellite_plots.geom) as distance")
        )
            ->join('su_farms as f', 'f.id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users_stations as us', 'us.organization_id', '=', 'f.organization_id')
            ->where('su_satellite_plots.gid', $plot->gid)
            ->where('f.organization_id', $plot->farm->organization_id)
            ->where(DB::raw('ST_Distance(us.geom, su_satellite_plots.geom)'), '<=', DB::raw("us.radius * 1000"))
            ->orderBy('distance', 'asc')->pluck('station_id');

        return $result;
    }

    public static function getPlotCoords($plotId)
    {
        return self::select([
            DB::raw("ST_X(ST_Transform(ST_Centroid(geom), 4326)) AS longitude"),
            DB::raw("ST_Y(ST_Transform(ST_Centroid(geom), 4326)) AS latitude")
        ])
            ->where('su_satellite_plots.gid', $plotId)
            ->first();
    }

    public static function getPlotSize($plotId)
    {
        return self::select([
            DB::raw("ST_Extent(ST_Transform(su_satellite_plots.geom, 900913)) AS extent_900913"),
            DB::raw("(ST_XMax(ST_Extent(ST_Transform(su_satellite_plots.geom, 900913))) - ST_XMin(ST_Extent(ST_Transform(su_satellite_plots.geom, 900913)))) AS extent_width_900913"),
            DB::raw("(ST_YMax(ST_Extent(ST_Transform(su_satellite_plots.geom, 900913))) - ST_YMin(ST_Extent(ST_Transform(su_satellite_plots.geom, 900913)))) AS extent_height_900913")
        ])
            ->where('su_satellite_plots.gid', $plotId)
            ->first();
    }

    public function getCropNameByDate($date, $language)
    {
        return DB::table('su_satellite_plots as sp')
            ->select("cc.crop_name_" . $language . " as crop_name")
            ->join("su_satellite_plots_crops as spc", "spc.plot_id", "=", "sp.gid")
            ->join("su_crop_codes as cc", "cc.id", "=", "spc.crop_id")
            ->where("sp.gid", $this->gid)
            ->whereDate("spc.from_date", "<=", $date)
            ->whereDate("spc.to_date", ">=", $date);
    }

    /**
     * @param $soprId
     * @param $type
     * @param $area
     * @deprecated
     *
     */
    public function createGrid($soprId, $type, $area)
    {
        $randomPointIntensity = Config::get('globals.PLOT_GRID_RANDOM_POINT_INTENSITY');
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaCoefHa = Config::get('globals.DKA_HA');
        $gridCellSizes = Config::get('globals.GRID_CELL_SIZES');

        if ($type == '2ha') {
            $cellCount = intval(round(($this->area * $areaCoefHa) / 2, 0));
        } else if ($type == '5ha') {
            $cellCount = intval(round(($this->area * $areaCoefHa) / 5, 0));
        } else if ($type == 'vra') {
            if (round($this->area * $areaCoefHa) <= 6) {
                $cellCount = 2;
            } else {
                $cellCount = intval(($this->area * $areaCoefHa) / 3);

            }
        } else if ($type == 'iso') {
            switch ($a = round($this->area * $areaCoefHa)) {
                case $a <= 2:
                    $cellCount = $gridCellSizes['0-2'];
                    break;
                case $a > 2 && $a <= 5:
                    $cellCount = $gridCellSizes['2-5'];
                    break;
                case $a > 5 && $a <= 10:
                    $cellCount = $gridCellSizes['5-10'];
                    break;
                case $a > 10 && $a <= 15:
                    $cellCount = $gridCellSizes['10-15'];
                    break;
                case $a > 15 && $a <= 20:
                    $cellCount = $gridCellSizes['15-20'];
                    break;
                case $a > 20 && $a <= 30:
                    $cellCount = $gridCellSizes['20-30'];
                    break;
                default:
                    $cellCount = intval(round(1 + sqrt($this->area * $areaCoefHa), 0));
            }
        } else if ($type == 'custom' && $area) {
            $cellCount = intval(round((($this->area * $areaCoef) / $area), 0));
        } else {
            return;
        }

        if ($cellCount < 1) {
            $cellCount = 1;
        }

        $gridCellSize = ($this->area * 1000) / (4 * $cellCount);

        $query = "
        select row_number () over() sample_id, concat('#', left(lpad(to_hex((random() * 10000000)::bigint), 6, '0'), 6)) color, * from (
            with overlapped as(
            with for_union as 
            (with grid_cells as
                    (
                    with centroids as 
                    (
                    select st_Centroid(ST_collect(clusters.random_points)) as centroid, cluster_id, plot
                    from
                        (        
                            select random_points, ST_ClusterKMeans(random_points, {$cellCount}) over () as cluster_id, plot
                            from 
                            (
                            SELECT (ST_Dump(ST_GeneratePoints(geom, (plots.area*{$randomPointIntensity})::integer))).geom AS random_points, plots.geom plot
                            FROM su_satellite_plots plots
                            WHERE gid = {$this->gid}
                            ) random_points
                        ) clusters
                    group by clusters.cluster_id, plot
                    ) 
                    select (case when count(centroids.centroid)<=1 then centroids.plot end) grid_cell
                    from centroids
                    group by plot
                    union
                    select (ST_Dump(ST_Intersection((ST_Dump(ST_VoronoiPolygons(ST_collect(centroids.centroid), 0 , plot))).geom, plot))).geom
                    from centroids
                    group by plot)
            select distinct(a.grid_cell) big, b.grid_cell small, ST_Length(ST_CollectionExtract(ST_Intersection(a.grid_cell, b.grid_cell), 2)) lng from grid_cells a
            left join (select * from grid_cells where st_area(grid_cell) <= {$gridCellSize}) b on ST_Touches(a.grid_cell, b.grid_cell)
            where st_area(a.grid_cell)>{$gridCellSize}
            ) 
            select row_number () over () id , * from (select st_union(a.big,b.small) cell from for_union a
            join (select small, max(lng) blng from for_union a where small notnull group by small) b on b.blng=a.lng 
            union 
            select big cell from for_union
            where lng isnull or lng not in(select max(lng) from for_union where small notnull group by small )
            group by big) a )
            select cell, st_centroid(cell) point from (select (st_dump(st_union(cell))).geom cell from overlapped where overlapped.id in 
            (select a.id from overlapped a
            left join overlapped b on ST_Relate(a.cell, b.cell, '2********')
            where a.id!=b.id and a.id is not null)) a where a is not null
            union
            select cell cell, st_centroid(cell) point from overlapped a 
            where a.id not in (select b.id from overlapped a
            left join overlapped b on ST_Relate(a.cell, b.cell, '2********')
            where a.id!=b.id)) a
        ";

        $gridData = DB::select($query);

        SoilGrid::getBySoprId($soprId)->delete();
        SoilPoints::getBySoprId($soprId)->delete();

        foreach ($gridData as $data) {
            $soilGrid = new SoilGrid();

            $soilGrid->sopr_id = $soprId;
            $soilGrid->geom = $data->cell;
            $soilGrid->sample_id = $data->sample_id;
            $soilGrid->color = $data->color;
            $soilGrid->save();

            $soilPoints = new SoilPoints();
            $soilPoints->sopr_id = $soprId;
            $soilPoints->sample_id = $data->sample_id;
            $soilPoints->geom = $data->point;
            $soilPoints->save();

        }

    }

    public static function getForProtocol($gids)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $res =
            DB::table('su_satellite_plots as sp')
                ->join('su_satellite_plots_files as spf', 'spf.plot_id', '=', 'sp.gid')
                ->select(
                    'sp.name AS name',
                    DB::raw("round((sp.area*{$areaCoef})::numeric, 3) AS area"),
                    'sp.gid AS gid',
                    DB::raw('st_x(st_transform(st_centroid(sp.geom), 4326)) as x'),
                    DB::raw('st_y(st_transform(st_centroid(sp.geom), 4326)) as y'),
                    DB::raw("(array_agg(spf.web_path))[1] AS web_path"),
                    DB::raw("(array_agg(spf.path))[1] AS path")

                )
                ->whereIN('sp.gid', $gids)
                ->groupBy('sp.name', 'sp.area', 'sp.gid')
                ->get();

        return $res;
    }

    public static function plotBBox($plotId, $widht, $height, $padding)
    {
        $ratio = $widht / $height;
        $sql = "with extents_size as 
        (
            SELECT ST_XMin (ST_Transform(geom, 900913)) AS xmin,
            ST_XMax (ST_Transform(geom, 900913)) AS xmax,
            ST_YMin (ST_Transform(geom, 900913)) AS ymin,
            ST_YMax (ST_Transform(geom, 900913)) AS ymax,
            (ST_XMax (ST_Transform(geom, 900913)) - ST_XMin (ST_Transform(geom, 900913))) w,
            (ST_YMax (ST_Transform(geom, 900913)) - ST_YMin (ST_Transform(geom, 900913))) h
              FROM su_satellite_plots 
            WHERE gid = :plotId
            GROUP BY su_satellite_plots.geom
        ),
        new_dem as 
        (
            SELECT xmin, xmax, ymin, ymax, 
                CASE WHEN w > h THEN :ratio*h END as new_width, 
                CASE WHEN w < h THEN :ratio*w END as new_height
            FROM extents_size
        ),
        new_ext as 
        (
            SELECT 
                CASE WHEN new_height NOTNULL THEN (xmin - new_height/2) else (xmin - new_width/4) END as xmin_new,
	            CASE WHEN new_height NOTNULL THEN (ymin) else (ymin - new_width/4) END as ymin_new,
	            CASE WHEN new_height NOTNULL THEN (xmax + new_height/2) else (xmax + new_width/4) END as xmax_new,	
	            CASE WHEN new_height NOTNULL THEN (ymax) else (ymax + new_width/4) END as ymax_new
            FROM new_dem
        ) 
        SELECT concat_ws(',', xmin_new - :padding, ymin_new - :padding, xmax_new + :padding, ymax_new + :padding) as bbox FROM new_ext";

        $arrResult = DB::select($sql, ['plotId' => $plotId, 'ratio' => $ratio, 'padding' => $padding]);
        return reset($arrResult)->bbox;
    }

    public static function getPlotsOrderVra($order, $tableName, $sort, $areaCoef)
    {
        $orderPlots = Plot::from('su_satellite_plots as sp')->select(
            'sp.gid',
            'sp.name',
            DB::raw("round(((ST_Area(sp.geom)/1000)*{$areaCoef})::numeric, 3) AS area"),
            DB::raw('ST_Extent(sp.geom) AS extent'),
            DB::raw('ST_AsText(sp.geom) as geom'),
            'vra.id as orders_plots_rel_id',
            'spc.crop_id', DB::raw('cc.crop_name as culture'),
            'vra.order_id'
        )
            ->join('' . $tableName . ' AS vra', 'vra.plot_id', '=', 'sp.gid')
            ->leftJoin('su_satellite_plots_crops AS spc', 'spc.plot_id', '=', 'sp.gid')
            ->leftJoin('su_crop_codes AS cc', 'cc.id', '=', 'spc.crop_id')
            ->where('vra.order_id', (int)$order->id)
            ->groupBy('sp.gid', 'vra.order_id', 'sp.area', 'sp.name', 'sp.geom', 'vra.id',
                'spc.crop_id', 'cc.crop_name')
            ->restOrderBy($sort)->get();

        return $orderPlots;
    }

    public static function getPlotsOrder($order, $sort, $areaCoef)
    {
        $orderPlots = Plot::from('su_satellite_plots as sp')->select(
            'sp.gid', DB::raw("round(((ST_Area(sp.geom)/1000)*{$areaCoef})::numeric, 3) AS area"),
            DB::raw('ST_Extent(sp.geom) AS extent'),
            'sp.name', DB::raw('ST_AsText(sp.geom) as geom'), 'f.name as farm',
            'sopr.id as orders_plots_rel_id', 'sopr.price', 'sopr.note',
            DB::raw('(not count(spf.plot_id)=0) as has_soil_map'), 'spc.crop_id',
            DB::raw('cc.crop_name as culture'),
            'recom.title as recom_title', 'recom.description as recom_description', 'sopr.treatment_id',
            'sopr.sampler_id',
            'sopr.order_id', 'sopr.ekatte_code', 'sopr.ekatte_name',
            DB::raw('(not count(ssg.sopr_id)=0) as has_soil_grid'),
            DB::raw('array_agg(distinct(ssg.sample_id)) as cell_ids'), 'sopr.leaf_sample_cells',
            'sopr.is_soil_sample_completed'
        )
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'sp.gid')
            ->join('su_satellite_orders AS so', 'so.id', '=', 'sopr.order_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'sp.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->leftJoin('su_analyzes_old_files AS spf', 'spf.order_plot_rel_id', '=', 'sopr.id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) use ($order) {
                $join->on('spc.plot_id', '=', 'sp.gid')->where('spc.is_primary', '=', true);
                if ($order->year) {
                    $join->where('spc.year', '=', $order->year);
                }
            })
            ->leftJoin('su_crop_codes AS cc', 'cc.id', '=', 'spc.crop_id')
            ->leftJoin('su_satellite_soil_points AS ssg', 'ssg.sopr_id', '=', 'sopr.id')
            ->leftJoin('su_satellite_plots_recommendations AS recom', 'recom.order_plot_rel_id', '=',
                'spf.order_plot_rel_id')
            ->where('sopr.order_id', (int)$order->id)
            ->groupBy('sp.gid', 'sopr.order_id', 'sp.area', 'sp.name', 'sp.geom', 'f.name',
                'sopr.id', 'sopr.price', 'sopr.note', 'spc.crop_id', 'cc.crop_name', 'sopr.treatment_id',
                'sopr.sampler_id', 'recom.title', 'recom.description')
            ->restOrderBy($sort)->get();

        return $orderPlots;
    }

    /**
     * !IMPORTANT when use this query generator, needs to implement in your method groupBy('su_satellite_plots.gid', 'crop')
     * If add new select item, insert then in groupBy
     * @param int|null $farmYear
     * @return mixed
     */
    public static function getPlotDataQuery(int $farmYear = null)
    {
        $currentDate = Carbon::today()->format('Y-m-d');
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = Plot::select(
            'su_satellite_plots.gid as id',
            'su_satellite_plots.uuid as plot_uuid',
            DB::raw("round((su_satellite_plots.area*{$areaCoef})::numeric, 3) AS area"),
            'su_satellite_plots.name',
            DB::raw('su_satellite_plots.upload_date::date'),
            'crop_code.crop_name as crop',
            'pc.from_date as sowing_date',
            'pc.to_date as harvest_date',
            'pc.irrigated',
            DB::raw('round(AVG(sslp.mean)::numeric, 2) as avg_index')
        )
            ->selectRaw('ST_AsGeoJSON(ST_Transform(geom, 3857))::json as geom')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->leftJoin('su_satellite_orders_plots_rel as sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_satellite_orders as o', 'o.id', '=', 'sopr.order_id')
            ->leftJoin('su_satellite_plots_crops as pc', function ($join) use ($currentDate, $farmYear) {
                $join->on('pc.plot_id', '=', 'su_satellite_plots.gid')
                    ->where('pc.is_primary', '=', true);

                if (isset($farmYear)) {
                    $join->where('pc.year', '=', $farmYear);
                } else {
                    $join->where('pc.from_date', '<=', $currentDate)
                        ->where('pc.to_date', '>=', $currentDate);
                }
            })
            ->leftJoin('su_crop_codes as crop_code', "crop_code.id", '=', 'pc.crop_id')
            ->leftJoin('su_satellite_layers_plots AS sslp', 'sslp.plot_id', '=', 'su_satellite_plots.gid')
            ->where('uf.user_id', Auth::user()->id);
        // !IMPORTANT  groupBy()
        return $query;
    }

    public static function getPlotCrops($plotId, $year = null, $sort = null, $limit = null)
    {
        $q = Plot::findOrFail($plotId)
            ->crops()
            ->leftJoin('su_crop_hybrid AS ch', 'ch.id', '=', 'hybrid_id')
            ->restOrderBy($sort);

        if ($year) {
            $q->where('su_satellite_plots_crops.year', $year);
        }

        $crop_name = Config::get('globals.CROP_NAME');

        $cultures = $q->paginate($limit, [
            'crop_code',
            '' . $crop_name . ' as crop_name',
            'gdd_collection',
            'winter_crop',
            'ch.name as hybrid_name'
        ]);

        return [
            'total' => $cultures->total(),
            'rows' => $cultures->items()
        ];
    }

    /**
     * @return mixed
     */
    public static function getPlotsByUser()
    {
        $query = Plot::join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('uf.is_visible', true);

        return $query;
    }

    /**
     * This method returns filtered and sorted plots data.
     *
     * Note: The sorting order depends on the element position in $sort array!
     * For example: ['avg_index'=>'desc', 'area'=>'asc'] will sort by avg_index first and then by area.
     *
     * @param string $lang
     *
     * @param array $filters = array(
     *      'plot_ids'      => int[],
     *      'plot_name'     => string,
     *      'farm_ids'      => int[],
     *      'date'          => string, // Format: Y-m-d
     *      'crop_ids'      => int[]
     * )
     *
     * @param array $sort = array(
     *      'field_name' => string
     * )
     *
     * @return mixed
     */
    public static function getFilteredPlotsData($lang, array $filters = [], array $sort = [])
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $filteredPlotsQuery = self::getFilteredPlotsQuery($filters);

        $filteredPlotsQuery->select(
            DB::raw('DISTINCT ON (su_satellite_plots.gid) su_satellite_plots.gid'),
            'su_satellite_plots.uuid',
            'su_satellite_plots.name',
            DB::raw("ROUND((su_satellite_plots.area*{$areaCoef})::numeric, 3) AS area"),
            DB::raw('ST_AsGeoJSON(ST_Transform(st_envelope(su_satellite_plots.geom), 4326)) AS geom_json'),
            DB::raw('scc.crop_name_' . $lang . ' AS crop'),
            'sslp.mean AS avg_index',
            DB::raw('sslp.mean - LAG(sslp.mean, 1) OVER (PARTITION BY su_satellite_plots.gid ORDER BY sslp.date) AS avg_index_diff'),
            'sspf.web_path',
            'su_satellite_plots.thumbnail'
        )
            ->groupBy('su_satellite_plots.gid', DB::raw('scc.crop_name_' . $lang), 'sspc.irrigated', 'sslp.date', 'sslp.mean', 'sspf.web_path', 'sspc.is_primary')
            ->orderBy('su_satellite_plots.gid')
            ->orderBy('sspc.is_primary', 'DESC');

        $filteredPlotsData = self::from('filteredPlots')
            ->withExpression('filteredPlots', $filteredPlotsQuery)
            ->select(
                'gid', 'uuid', 'name', 'area', 'web_path', 'thumbnail', 'geom_json', 'crop', 'avg_index', 'avg_index_diff'
            );

        foreach ($sort as $fieldName => $sortType) {
            $filteredPlotsData->orderBy($fieldName, $sortType);
        }

        return $filteredPlotsData;
    }

    /**
     * This method returns filtered query for plots.
     *
     * @param array $filters = array(
     *      'plot_ids'      => int[],
     *      'plot_name'     => string,
     *      'farm_ids'      => int[],
     *      'date'          => string, // Format: Y-m-d
     *      'farm_year'     => int,
     *      'crop_ids'      => int[]
     * )
     * @param string|null $type
     *
     * @return mixed
     */
    public static function getFilteredPlotsQuery(array $filters = [], ?string $type = Order::TYPE_INDEX)
    {
        $plotsData = self::join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_uuid', 'su_satellite_plots.uuid')
            ->join('su_satellite_orders AS so', function ($join) use ($type) {
                $join->on('so.uuid', '=', 'sopr.order_uuid');
                if ($type) {
                    $join->where('so.type', '=', $type);
                }
                $join->whereRaw('so.organization_id=su.last_chosen_organization_id');
            })
            ->leftJoin('su_satellite_plots_crops AS sspc', function ($join) use ($filters) {
                $join->on('sspc.plot_id', '=', 'su_satellite_plots.gid');
                $join->where('sspc.is_primary', DB::raw('true'));

                if (isset($filters['farm_year']) && $filters['farm_year']) {
                    $join->where('sspc.year', $filters['farm_year']);
                }
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'sspc.crop_id')
            ->leftJoin('su_satellite_layers_plots AS sslp', function ($join) use ($filters, $type) {
                $join->on('sslp.plot_id', '=', 'su_satellite_plots.gid');
                $join->where('sslp.date', $filters['date'] ?? null);

                if ($type === Order::TYPE_SOIL) {
                    $join->where([
                        ['sslp.type', LayerPlot::TYPE_SOIL]
                    ]);
                }
                if ($type === Order::TYPE_INDEX) {
                    $join->where([
                        ['sslp.type', LayerPlot::TYPE_INDEX],
                        ['sslp.satellite_type', LayerPlot::SATELLITE_TYPE_SENTINEL]
                    ]);
                }

            })
            ->leftJoin('su_satellite_plots_files AS sspf', function ($join) {
                $join->on('sspf.plot_id', '=', 'sslp.plot_id');
                $join->where('sspf.type', 'plot');
            })
            ->where([
                ['su.last_chosen_organization_id', Auth::user()->lastChosenOrganization->id],
                ['su.id', Auth::user()->id]
            ]);

        if (isset($filters['plot_ids']) && count($filters['plot_ids']) > 0) {
            $plotsData->whereIn('su_satellite_plots.gid', $filters['plot_ids']);
        }

        if (isset($filters['plot_name']) && $filters['plot_name']) {
            $plotsData->where('su_satellite_plots.name', $filters['plot_name']);
        }

        if (isset($filters['farm_ids']) && count($filters['farm_ids']) > 0) {
            $plotsData->whereIn('su_satellite_plots.farm_id', $filters['farm_ids']);
        }

        if (isset($filters['farm_year']) && $filters['farm_year']) {
            $plotsData->where('so.year', $filters['farm_year']);
        }

        if (isset($filters['crop_ids']) && count($filters['crop_ids']) > 0) {
            $plotsData->where(function ($q) use ($filters) {
                $cropIdsNullValueIdx = array_search(null, $filters['crop_ids']);

                // Check if there is null value in $filters['crop_ids']
                if ($cropIdsNullValueIdx !== false) {
                    unset($filters['crop_ids'][$cropIdsNullValueIdx]); //remove null value from array

                    $q->whereIn('sspc.crop_id', $filters['crop_ids'])
                        ->orWhere('sspc.crop_id', null);
                    return;
                }

                $q->whereIn('sspc.crop_id', $filters['crop_ids']);
            });
        }

        return $plotsData;
    }

    public static function getPlotFarmsQuery()
    {
        $currentDate = Carbon::today()->format('Y-m-d');
        $query = Plot::join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->leftJoin('su_satellite_plots_crops as pc', function ($join) use ($currentDate) {
                $join->on('pc.plot_id', '=', 'su_satellite_plots.gid')
                    ->where('pc.from_date', '<=', $currentDate)
                    ->where('pc.to_date', '>=', $currentDate);
            })
            ->leftJoin('su_crop_codes as crop_code', "crop_code.id", '=', 'pc.crop_id');
        return $query;
    }

    public static function getDetails($filters, $lang)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $filteredPlotsQuery = self::getFilteredPlotsQuery($filters, null);

        $plotDetails = $filteredPlotsQuery->select(
            DB::raw('DISTINCT ON (su_satellite_plots.gid) su_satellite_plots.gid'),
            'su_satellite_plots.uuid',
            DB::raw('ST_AsGeoJSON(ST_Transform(su_satellite_plots.geom, 3857)) as geom_json'),
            'sspf.web_path',
            'su_satellite_plots.thumbnail',
            'su_satellite_plots.name',
            'sslp.mean as avg_index',
            DB::raw("sslp.mean - LAG(sslp.mean, 1) OVER (PARTITION BY su_satellite_plots.gid ORDER BY sslp.date) AS avg_index_diff"),
            DB::raw("round((su_satellite_plots.area*{$areaCoef})::numeric, 3) as area"),
            'sspc.crop_id as current_crop_id',
            DB::raw("scc.crop_name_{$lang} as current_crop_name"),
            'scct.id as current_crop_category_id',
            'scct.category as current_crop_category_name',
            'sch.id as current_crop_hybrid_id',
            'sch.name as current_crop_hybrid_name',
            'sspc.from_date as sowing_date',
            'sspc.to_date as harvest_date',
            'sspc.id as plot_crop_rel_id',
            DB::raw('
                case when extract(\'days\' from now() - sspc.from_date) >= 0
                then extract(\'days\' from now() - sspc.from_date)
                else 0 
                end as days_since_sowing
            '),
            'pp.slug as phenophase',
            'sspc.irrigated',
            'pp.sum_gdd as gdd',
            'pp.frontend_array_index as growing_stage',
            'gddc.slug as gdd_collection',
            'su_satellite_plots.station_id',
            DB::raw('json_build_object(\'id\', f.id, \'name\', f.name) as farm')
        )
            ->leftJoin('phenophases as pp', 'pp.id', '=', 'sspc.gdd_phenophase_id')
            ->leftJoin('gdd_collections as gddc', 'gddc.id', '=', 'pp.gdd_collection_id')
            ->leftJoin('su_crop_categories as scct', 'scct.id', '=', 'sspc.category_id')
            ->leftJoin('su_crop_hybrid as sch', 'sch.id', '=', 'sspc.hybrid_id')
            ->join('su_farms as f', 'f.id', '=', 'su_satellite_plots.farm_id')
            ->groupBy(
                'su_satellite_plots.gid',
                DB::raw('scc.crop_name_' . $lang),
                'sspc.irrigated',
                'sslp.mean',
                'sspf.web_path',
                'sspc.crop_id',
                'sspc.from_date',
                'sspc.to_date',
                'pp.slug',
                'pp.sum_gdd',
                'su_satellite_plots.station_id',
                'sslp.date',
                'sspc.id',
                'gddc.slug',
                'pp.frontend_array_index',
                'scct.id',
                'sch.id',
                'f.id',
                'f.name'
            )
            ->orderBy('su_satellite_plots.gid', 'ASC')
            ->orderBy('sspc.is_primary', 'DESC')
            ->orderBy('sslp.date', 'DESC');

        return $plotDetails->first()->toArray();
    }

    public static function getPlotsLastChosenOrganizationQuery()
    {
        return self::join('su_farms AS sf', 'sf.id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms_users AS sfu', 'sfu.farm_id', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', function ($join) {
                $join->on('su.id', '=', 'sfu.user_id')
                    ->on('sf.organization_id', '=', 'su.last_chosen_organization_id');
            })
            ->leftJoin('su_satellite_plots_crops AS sspc', 'sspc.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'sspc.crop_id')
            ->leftJoin('su_satellite_layers_plots AS sslp', 'sslp.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_satellite_orders AS so', 'so.id', '=', 'sslp.order_id')
            ->leftJoin('su_satellite_plots_files AS sspf', 'sspf.plot_id', '=', 'sslp.plot_id')
            ->where('sspf.type', 'plot')
            ->where(function ($q) {
                $q->where('sspc.is_primary', 'true');
                $q->orWhere('sspc.is_primary', null);
            });
    }

    public static function getExtent($organizationId, $userId, $layerName = 'all_plots_select_layer', $year = null, $gid = 0, $transformTo = "900913", $flatArray = true)
    {
        $extentQuery = self::selectRaw('ST_extent(geom) as extent');
        if ($transformTo) {
            $extentQuery = self::selectRaw('ST_extent( ST_Transform(geom, ' . $transformTo . ')) as extent');
        }

        $extentQuery->join('su_farms AS f', 'f.id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'f.id')
            ->where('uf.user_id', $userId)
            ->where('f.organization_id', $organizationId);

        if ($gid > 0) {
            $extentQuery->where('gid', $gid);
        }

        if ($layerName == 'vector_layer') {
            $extentQuery->whereDoesntHave('orders', function ($q) {
                $q->where('status', '<>', 'canceled');
            });

            $extent = $extentQuery->pluck('extent');
            return Helper::parseExtentToOpenLayer($extent, $flatArray);
        }

        if ($layerName == 'all_plots_select_layer') {
            $extent = $extentQuery->pluck('extent');
            return Helper::parseExtentToOpenLayer($extent, $flatArray);
        }

        if ($year) {
            $extentQuery->whereHas('orders', function ($q) use ($year) {
                $q->where('status', '<>', 'canceled');
                $q->where('year', $year);
            });
        }

        $extent = $extentQuery->pluck('extent');
        return Helper::parseExtentToOpenLayer($extent, $flatArray);
    }

    public static function getIrrigatedPlotsQueryBy(array $param)
    {
        $query = self::join('su_irrigation_platforms as sip', 'sip.farm_id', '=', 'su_satellite_plots.farm_id')
            ->whereRaw("ST_Intersects(su_satellite_plots.geom, sip.centre)");

        if (isset($param['gid'])) {
            $query->where('su_satellite_plots.gid', $param['gid']);
        }

        if (isset($param['farm_ids'])) {
            $query->whereIn('su_satellite_plots.farm_id', $param['farm_ids']);
        }

        return $query;
    }

    /**
     * This method returns filtered and sorted plots data.
     *
     * Note: The sorting order depends on the element position in $sort array!
     * For example: ['avg_index'=>'desc', 'area'=>'asc'] will sort by avg_index first and then by area.
     *
     * @param string $lang
     *
     * @param array $filters = array(
     *      'plot_ids'      => int[],
     *      'plot_name'     => string,
     *      'farm_ids'      => int[],
     *      'date'          => string, // Format: Y-m-d
     *      'crop_ids'      => int[]
     * )
     *
     * @param array $sort = array(
     *      'field_name' => string
     * )
     *
     * @return mixed
     */
    public static function getFilteredPlotsSoilsList($lang, array $filters = [], array $sort = [])
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $filteredPlotsQuery = self::getFilteredPlotsQuery($filters, Order::TYPE_SOIL);

        $filteredPlotsQuery->select(
            DB::raw('DISTINCT ON (su_satellite_plots.gid) su_satellite_plots.gid'),
            'su_satellite_plots.uuid',
            'su_satellite_plots.name',
            DB::raw("ROUND((su_satellite_plots.area*{$areaCoef})::numeric, 3) AS area"),
            DB::raw('ST_AsGeoJSON(ST_Transform(st_envelope(su_satellite_plots.geom), 4326)) AS geom_json'),
            DB::raw('scc.crop_name_' . $lang . ' AS crop'),
            'sslp.mean AS avg_index',
            DB::raw('sslp.mean - LAG(sslp.mean, 1) OVER (PARTITION BY su_satellite_plots.gid ORDER BY sslp.date) AS avg_index_diff'),
            'sspf.web_path',
            DB::raw("gs_svg_thumbnail(su_satellite_plots.geom, 'rgb(139, 196, 94)', 'rgb(196, 243, 212)', 55, 55) AS thumbnail"),
            'so.id as order_id',
            'so.uuid as order_uuid'
        );

        if (isset($filters['order_uuids']) && count($filters['order_uuids'])) {
            $filteredPlotsQuery->whereIn('so.uuid', $filters['order_uuids']);
        }

        $filteredPlotsQuery->groupBy('su_satellite_plots.gid', DB::raw('scc.crop_name_' . $lang), 'sspc.irrigated', 'sslp.date', 'sslp.mean', 'sspf.web_path', 'sspc.is_primary', 'so.uuid', 'so.id')
            ->orderBy('su_satellite_plots.gid')
            ->orderBy('sspc.is_primary', 'DESC');

        $filteredPlotsData = self::from('filteredPlots')
            ->withExpression('filteredPlots', $filteredPlotsQuery)
            ->select(
                'gid', 'uuid', 'name', 'area', 'web_path', 'thumbnail', 'geom_json', 'crop', 'order_id', 'order_uuid'
            );

        foreach ($sort as $fieldName => $sortType) {
            $filteredPlotsData->orderBy($fieldName, $sortType);
        }

        return $filteredPlotsData;
    }

    /**
     * This method is used when adding plots to package of a contract.
     * It returns the plots with the overlap percentage.
     *
     * @param array $plotUuids Plots that would be added to a package
     * @param array $contractPlotUuids Plots from contract's full sampling (is_full_sampling = true) packages
     * @param array $contractOrderUuids Orders from contract's full sampling (is_full_sampling = true) packages
     * @param int $organizationId The id of organization
     * @param string $lang The language used for the crop name
     *
     * @return array
     */
    public static function getPlotsOverlapByContract(array $plotUuids, array $contractPlotUuids, array $contractOrderUuids, int $organizationId, string $lang = 'en'): array
    {
        $currentUser = Auth::user();
        $currentDate = Carbon::today()->format('Y-m-d');
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        // Plots that would be added to a package
        $plotsToAddQuery = self::from('su_satellite_plots AS ssp')
            ->select(
                'ssp.gid',
                'ssp.uuid',
                'ssp.geom',
                'ssp.name AS plot_name',
                'ssp.area',
                DB::raw("scc.crop_name_{$lang} AS current_crop")
            )
            ->join('su_farms AS sf', 'sf.id', '=', 'ssp.farm_id')
            ->join('su_farms_users AS sfu', function ($join) use ($currentUser) {
                $join->on('sfu.farm_id', '=', 'sf.id')
                    ->where('sfu.user_id', $currentUser->id);
            })
            ->join('su_organizations_users AS sou', function ($join) use ($organizationId) {
                $join->on('sou.user_id', '=', 'sfu.user_id')
                    ->where('sou.organization_id', $organizationId);
            })
            ->leftJoin('su_satellite_plots_crops AS sspc', function ($join) use ($currentDate) {
                $join->on('sspc.plot_id', '=', 'ssp.gid')
                    ->where('sspc.from_date', '<=', $currentDate)
                    ->where('sspc.to_date', '>=', $currentDate)
                    ->where('sspc.is_primary', '=', DB::raw("true"));
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'sspc.crop_id')
            ->whereIn('ssp.uuid', $plotUuids);

        // Plots that are in packages with is_full_sampling = true
        $plotsInContractQuery = self::from('su_satellite_plots AS ssp')
            ->select(
                'plot_uuid',
                'order_uuid',
                'geom'
            )
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'ssp.gid')
            ->whereIn('sopr.plot_uuid', $contractPlotUuids)
            ->whereIn('sopr.order_uuid', $contractOrderUuids);


        $overlappedPlotsQuery = self::from('plots_to_add as pta')
            ->select(
                'pta.gid AS id',
                DB::raw("pta.uuid AS \"plotUuid\""),
                'pta.plot_name AS name',
                'pta.current_crop AS crop',
                DB::raw("ROUND((pta.area * {$areaCoef})::numeric, 3) AS area"),
                DB::raw("
                    ROUND(
                        COALESCE(MAX(
                                ((ST_Area(ST_Intersection(pta.geom, pic.geom))) / ST_Area(pta.geom)) * 100
                        ), 0)::numeric,
                        1
                    ) AS overlap_percentage"
                ),
                DB::raw("
                    JSON_BUILD_OBJECT(
                        'plot_uuid', pic.plot_uuid,
                        'order_uuid', pic.order_uuid
                    ) AS overlaps_with
                ")
            )
            ->leftJoin('plots_in_contract AS pic', 'pic.geom', '&&', 'pta.geom')
            ->groupBy(
                'pta.gid',
                'pta.uuid',
                'pic.plot_uuid',
                'pic.order_uuid',
                'pta.plot_name',
                'pta.current_crop',
                'area'
            );

        $query = self::from('overlapped_plots')
            ->withExpression('plots_to_add', $plotsToAddQuery)
            ->withExpression('plots_in_contract', $plotsInContractQuery)
            ->withExpression('overlapped_plots', $overlappedPlotsQuery)
            ->select(
                DB::raw("DISTINCT ON (\"plotUuid\") \"plotUuid\""),
                'id',
                'name',
                'crop',
                'area',
                'overlap_percentage',
                'overlaps_with'
            )
            ->orderBy(DB::raw("\"plotUuid\""))
            ->orderBy('overlap_percentage', 'DESC');

        return $query->get()->toArray();
    }

    /**
     * Get the stats for all plots by date (for given organization)
     *
     * @param $organizationId The id of organization
     * @param $date Filter The plots by orders with this date
     * @param $satelliteType The type of the satellite (e.g. sentinel, landsat, ..)
     *
     * @return array The stats for every filtered plot
     */
    public static function getPlotsStatsByDate(int $organizationId, string $date, string $satelliteType = 'sentinel'): array
    {
        return self::from('su_satellite_orders_plots_rel as sopr')
            ->select('slp.plot_id', 'slp.stats', 'slp.mean')
            ->join('su_satellite_layers_plots as slp', 'slp.plot_id', '=', 'sopr.plot_id')
            ->join('su_satellite_orders as so', 'so.id', '=', 'sopr.order_id')
            ->where('slp.date', $date)
            ->where('slp.type', 'index')
            ->where('slp.satellite_type', $satelliteType)
            ->where('so.organization_id', $organizationId)
            ->get()
            ->toArray();
    }

    /**
     * Get the  extent, width and height for all plots by date (for given organization)
     *
     * @param $organizationId The id of organization
     * @param $date Filter The plots by orders with this date
     * @param $satelliteType The type of the satellite (e.g. sentinel, landsat, ..)
     *
     * @return object Object containing the total extent, width and height for all the filtered plots
     */
    public static function getPlotsImageSizeByDate(int $organizationId, string $date, string $satelliteType = 'sentinel'): object
    {
        $allPlotsGeomQuery = self::from('su_satellite_orders_plots_rel as sopr')
            ->selectRaw(
                'ST_UNION(sp.geom) AS geom'
            )
            ->join('su_satellite_orders as so', 'so.id', '=', 'sopr.order_id')
            ->join('su_satellite_orders_files as sof', 'sof.order_id', '=', 'so.id')
            ->join('su_satellite_plots as sp', 'sp.gid', '=', 'sopr.plot_id')
            ->where('sof.date', $date)
            ->where('sof.layer_type', 'index')
            ->where('sof.satellite_type', $satelliteType)
            ->where('so.organization_id', $organizationId);

        return self::from('all_plots_geom')
            ->withExpression('all_plots_geom', $allPlotsGeomQuery)
            ->select(
                DB::RAW("ST_EXTENT(geom) as extent"),
                DB::RAW("((ST_XMax(ST_EXTENT(geom)) - ST_XMin(ST_EXTENT(geom))) / 10)::int as width"),
                DB::RAW("((ST_YMax(ST_EXTENT(geom)) - ST_YMin(ST_EXTENT(geom))) / 10)::int as height")
            )
            ->first();
    }

    /**
     * Get update date
     *
     * @param int|null $organizationId
     * @param int|null $farmYear
     * @return mixed
     */
    public static function getPlotsDate(?int $organizationId = null, ?int $farmYear = null)
    {
        $query = self::selectRaw('distinct on(su_satellite_plots.upload_date) su_satellite_plots.upload_date::date as date')
            ->join('su_farms as sf', 'sf.id', '=', 'su_satellite_plots.farm_id');

        if ($organizationId) {
            $query->where('sf.organization_id', $organizationId);
        }

        if ($farmYear) {
            $query->whereRaw("extract (year from su_satellite_plots.upload_date) = {$farmYear}");
        }

        return $query->pluck('date')->toArray();
    }

    /**
     * Get plots data with overlap parameter
     *
     * @param int|null $organizationId
     * @param string $lang
     * @param array $filter
     * @param array $sort
     *
     * @return mixed
     */
    public static function listPlotsDataWithOverlapParam(?int $organizationId, string $lang = 'en', array $filter = [], $sort = [])
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = self::select(
            DB::raw("distinct on(su_satellite_plots.gid) su_satellite_plots.gid"),
            'su_satellite_plots.uuid',
            'su_satellite_plots.name as plot_name',
            DB::raw("ST_AsGeoJSON(ST_Transform(st_envelope(su_satellite_plots.geom), 3857)) AS geom_json"),
            'sf.name as farm_name',
            'sf.id as farm_id',
            DB::raw("round((su_satellite_plots.area * {$areaCoef})::numeric, 3) as area"),
            DB::raw("scc.crop_name_{$lang} as crop_name"),
            DB::raw("su_satellite_plots.upload_date::date as date"),
            DB::raw("case when(ssp2.gid is null) then false else true end as overlaps"),
            'su_satellite_plots.is_editable'
        )
            ->leftJoin('su_satellite_plots AS ssp2', function ($join) {
                $join->on(DB::raw("ST_Overlaps(su_satellite_plots.geom, ssp2.geom)"), '=', DB::raw("true"))
                    ->on('su_satellite_plots.uuid', '=', 'ssp2.uuid')
                    ->on('su_satellite_plots.farm_id', '=', 'ssp2.farm_id');
            })
            ->join('su_farms as sf', 'sf.id', '=', 'su_satellite_plots.farm_id')
            ->leftJoin('su_satellite_plots_crops as sspc', 'sspc.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_crop_codes as scc', 'scc.id', '=', 'sspc.crop_id');

        if ($organizationId) {
            $query->where('sf.organization_id', '=', $organizationId);
        }

        if (isset($filter['startDate']) && isset($filter['endDate'])) {
            $query->whereBetween(DB::raw("su_satellite_plots.upload_date::date"), [$filter['startDate'], $filter['endDate']]);
        }

        if (isset($filter['farmIds']) && is_array($filter['farmIds'])) {
            $query->whereIn('sf.id', $filter['farmIds']);
        }
        if (isset($filter['plotIds']) && is_array($filter['plotIds'])) {
            $query->whereIn('su_satellite_plots.gid', $filter['plotIds']);
        }
        if (isset($filter['plotUuIds']) && is_array($filter['plotUuIds'])) {
            $query->whereIn('su_satellite_plots.uuid', $filter['plotUuIds']);
        }

        foreach ($sort as $fieldName => $sortType) {
            $query->orderBy($fieldName, $sortType);
        }

        return $query;
    }

    /**
     * @param string|null $year
     * @return mixed
     */
    public static function basicDataQuery(?string $year = null)
    {
        return Plot::leftJoin('su_satellite_plots_crops AS spc', function ($join) use ($year) {
            $join->on('spc.plot_id', '=', 'su_satellite_plots.gid')->where('spc.is_primary', '=', true);
            if ($year) {
                $join->where('spc.year', '=', $year);
            }
        })
            ->leftJoin('su_crop_codes AS c', 'c.id', '=', 'spc.crop_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id');
    }

    public static function getPlotsWithSoilGridPointsAndFarms(int $organizationId)
    {
        return Plot::join('su_farms as sf', function ($join) use ($organizationId) {
            $join->on('sf.id', '=', 'su_satellite_plots.farm_id')
                ->where('sf.organization_id', '=', $organizationId);
        })
            ->join('su_satellite_orders_plots_rel as sopr', 'sopr.plot_uuid', '=', 'su_satellite_plots.uuid')
            ->join('su_satellite_soil_grid as sssg', 'sssg.sopr_id', '=', 'sopr.id')
            ->groupBy('su_satellite_plots.gid', 'su_satellite_plots.uuid', 'su_satellite_plots.name', 'sf.name')
            ->selectRaw("
                su_satellite_plots.gid as plot_id, 
                su_satellite_plots.uuid as plot_uuid, 
                su_satellite_plots.name as plot_name,
                sf.name as farm_name,
                json_object_agg(sssg.sample_id, json_build_object(
                    'longitude', ST_X(ST_Transform(ST_Centroid(sssg.geom), 4326)),
                    'latitude', ST_Y(ST_Transform(ST_Centroid(sssg.geom), 4326))
                )) as soil_grid_points_coordinates
            ")->get()->toArray();
    }
}
