<?php

namespace App\Models\API;

use Illuminate\Database\Eloquent\Model;

/**
 * This model describes the 'api_soil_ordered_plots' view.
 */
class ViewSoilOrderedPlots extends Model {

    protected $table = 'api_soil_ordered_plots';
    public $timestamps = false;

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'plot_geom' => 'object',
        'sample_points' => 'array',
        'sample_grid' => 'array',
        'plots' => 'array'
    ];

    public static function getTableName()
    {
        return with(new static)->getTable();
    }
}
