<?php

namespace App\Models\API;

use Illuminate\Database\Eloquent\Model;

/**
 * This model describes the 'su_users_notifications' table.
 */
class Notifications extends Model {

    protected $table = 'su_users_notifications';
    protected $hidden = array('id', 'group_id');
    //protected $appends = array('geom', 'images_data');
    public $timestamps = false;

    public function getDataAttribute() {
        return json_decode($this->attributes['data']);
    }

    public function getId() {
        return $this->attributes['id'];
    }

    public function getGroupId() {
        return $this->attributes['group_id'];
    }
}
