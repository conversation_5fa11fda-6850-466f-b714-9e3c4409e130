<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Http\Request;
use Laravel\Passport\HasApiTokens;
use Illuminate\Foundation\Auth\User as Authenticatable;
use LaravelDoctrine\ORM\Types\Json;
use DB;
use Auth;
use Hash;

/**
 * Class User
 * @package App\Models
 *
 * @property string $username
 * @property string $password
 * @property string $name
 * @property string $address
 * @property string $phone
 * @property string $email
 * @property string $comment
 * @property boolean $is_superadmin
 * @property string $database
 * @property string $hash
 * @property integer $parent_id
 * @property integer $can_create
 * @property integer $level
 * @property integer $group_id
 * @property boolean $active
 * @property integer $server
 * @property \DateTime $start_date
 * @property \DateTime $due_date
 * @property boolean $entry_flag
 * @property integer $entries_left
 * @property boolean $date_flag
 * @property integer $map_type
 * @property boolean $is_trial
 * @property integer $allowed_farmings
 * @property string $track_username
 * @property string $track_password
 * @property \DateTime $creation_date
 * @property \DateTime $last_login_date
 * @property string $last_login_ip
 * @property string $app_version
 * @property boolean $app_critical_upd
 * @property \DateTime $paid_support
 * @property boolean $is_demo
 * @property boolean $is_cached
 * @property boolean $ip_filter
 * @property Json $ip_white_list
 * @property string $profile_image
 * @property double $ordered_area
 * @property integer $gdd_entries_left
 * @property string $email_bcc
 * @property integer $last_chosen_organization_id
 *
 */
class User extends Authenticatable implements
    AuthorizableContract,
    CanResetPasswordContract
{
    use HasFactory;

    use HasApiTokens, Authorizable, CanResetPassword;

    const LEVEL_ADMIN  = 1;
    const LEVEL_CLIENT = 2;

    const ROLE_ORGANIZATION_MANAGER = 'ORGANIZATION_MANAGER';
    const ROLE_SERVICE_ADMIN = 'SERVICE_ADMIN';
    const ROLE_SERVICE_MANAGER = 'SERVICE';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'su_users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'email', 'password'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = ['password', 'remember_token'];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'ip_white_list' => 'array',
    ];

    /**
     * Get the files for the user.
     */
    public function files()
    {
        return $this->hasMany('App\Models\File', 'group_id', 'id');
    }

    public function orders()
    {
        return $this->hasMany('App\Models\Order', 'user_id', 'id');
    }

    public function recommendations()
    {
        return $this->hasMany('App\Models\Recommendation', 'user_id', 'id');
    }

    public function parent()
    {
        return $this->belongsTo('App\Models\User', 'parent_id', 'id');
    }

    public function stations()
    {
        return $this->hasMany('App\Models\UserStation', 'user_id', 'id')->orderBy('id', 'desc');
    }

    //using username instead of email on login
    public function findForPassport($username)
    {
        return $this->where('username', $username)->first();
    }

    public function organizations()
    {
        return $this->belongsToMany('App\Models\Organization', 'su_organizations_users')->orderBy('id', 'desc');
    }

    public function farms()
    {
        return $this->belongsToMany('App\Models\Farm', 'su_farms_users')->withPivot(['is_visible']);
    }

    public function lastChosenOrganization()
    {
        return $this->belongsTo('App\Models\Organization', 'last_chosen_organization_id', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo('App\Models\User', 'created_by', 'id');
    }

    public function plotsCount()
    {

        return self::join('su_farms_users as fu', 'fu.user_id', '=', 'su_users.id')
            ->join('su_farms as f', 'f.id', '=', 'fu.farm_id')
            ->join('su_satellite_plots as p', 'p.farm_id', '=', 'f.id')
            ->where('su_users.id', $this->id)
            ->count();
    }

    public function farmsByOrganization(Organization $organization = null)
    {

        $query = self::select('f.id', 'f.name', 'fu.is_visible')
            ->join('su_farms_users as fu', 'fu.user_id', '=', 'su_users.id')
            ->join('su_farms as f', 'f.id', '=', 'fu.farm_id')
            ->where('su_users.id', $this->id);

        if ($organization) {
            $query->where('f.organization_id', $organization->id);
        }

        $query->orderBy('id', 'desc');

        return $query->get();
    }

    public function allFarmsByOrganization(Organization $organization = null)
    {
        $query = Farm::selectRaw("DISTINCT su_farms.id, su_farms.name, COALESCE(fu.is_visible, false) is_visible, fu.user_id notnull as is_attached")
            ->leftJoin('su_farms_users as fu', function($join) {
                $join->on('fu.farm_id', '=', 'su_farms.id');
                $join->where('fu.user_id', '=',  $this->id);
            });

        if ($organization) {
            $query->where('su_farms.organization_id', $organization->id);
        }

        $query->orderBy('id', 'desc');

        return $query->get();
    }

    /**
     * @return GlobalUser
     */
    public function globalUser()
    {
        return GlobalUser::where('old_id', $this->id)->where('username', $this->username)->first();
    }

    public function getDescendants()
    {
        /** @var GlobalUser $globalUser */
        $globalUser = $this->globalUser();

        /** @var Collection $globalUsers */
        $globalUsers = GlobalUser::where('_lft', '>', $globalUser->getLft())
            ->where('_rgt', '<', $globalUser->getRgt())
            ->get();

        $globalUsersIds = $globalUsers->map(function ($user) {
            return $user->only(['old_id']);
        });

        /** @var Collection $users */
        $users = self::whereIn('id', $globalUsersIds)->orderBy('id', 'desc')->with('farms')->get();
        $users->map(function ($user) use ($globalUsers) {
            $roles = [];
            $forbiddenAbilities = [];
            /** @var GlobalUser[] $tmp */
            $tmp = $globalUsers->filter(function ($globalUser) use ($user) {
                return $globalUser->old_id == $user->id;
            });

            foreach ($tmp as $item) {
                $roles[] = $item->roles;
                $forbiddenAbilities[] = $item->getForbiddenAbilities();
            }

            $user->roles = reset($roles);
            $user->forbidden_abilities = reset($forbiddenAbilities);
        });

        return $users;
    }

    public static function createUser(Request $request)
    {
        DB::beginTransaction();
        try {

            /** @var User $currentUser */
            $currentUser = Auth::user();
            $currentGlobalUser = $currentUser->globalUser();

            $newUser = new User();
            $newUser->username = $request->get('username');
            $newUser->password = Hash::make($request->get('password'));
            $newUser->email = $request->get('email');
            $newUser->name = $request->get('name');
            $newUser->phone = $request->get('phone');
            $newUser->parent()->associate($currentUser);
            $newUser->level = User::LEVEL_CLIENT;
            $newUser->save();
            $newUser->group_id = $newUser->id;
            $newUser->save();

            $newUser->organizations()->attach($currentUser->lastChosenOrganization->id);
            foreach ($request->get('farms') as $farm) {
                $newUser->farms()->attach($farm['id']);
            }

            $newGlobalUser = new GlobalUser();
            $newGlobalUser->username = $request->get('username');
            $newGlobalUser->password = Hash::make($request->get('password'));
            $newGlobalUser->email = $request->get('email');
            $newGlobalUser->name = $request->get('name');
            $newGlobalUser->country = $currentGlobalUser->country;
            $newGlobalUser->old_id = $newUser->id;
            $newGlobalUser->old_group_id = $newUser->group_id;
            $newGlobalUser->parent()->associate($currentUser->globalUser()->id);
            $newGlobalUser->service_provider_id = $currentUser->globalUser()->serviceProvider->id;
            $newGlobalUser->save();

            $newGlobalUser->group_id = $newGlobalUser->id;
            $newGlobalUser->save();

            $newGlobalUser->assign('STAFF');

            foreach ($request->get('abilities') as $ability) {
                if ($ability["allowed"]) {
                    $newGlobalUser->unforbid($ability["name"]);
                } else {
                    $newGlobalUser->forbid($ability["name"]);
                }
            }   

            GlobalUser::fixTree();

            DB::table('su_system_users')->insert(
                ['user_id' => $newUser->id, 'username' => $newUser->username, 'password' => $request->get('password')]
            );

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            throw $e;
        }

        return $newUser;
    }

    public static function search($value)
    {
        return self::select('su_users.id', 'su_users.username', 'su_users.name', 'su_users.email')
            ->where('su_users.username', 'ILIKE', trim('%' . $value . '%'))
            ->orWhere('su_users.name', 'ILIKE', trim('%' . $value . '%'))
            ->orWhere('su_users.email', 'ILIKE', trim('%' . $value . '%'))
            ->orderBy('username', 'asc')
            ->get();
    }

    public function getLastAddedOrganizationId() {
        $lastAddedOrgData =  $this->selectRaw('su_users.id as user_id, MAX(sou.id) AS lst, sou.organization_id as last_org_id')
        ->join('su_organizations_users as sou', 'su_users.id', '=', 'sou.user_id')
        ->where('su_users.id', $this->id)
        ->groupBy(['su_users.id', 'sou.organization_id'])
        ->orderBy('lst', 'DESC')
        ->first();

        if($lastAddedOrgData) {
            return $lastAddedOrgData->last_org_id;
        }

        return null;
    }

    /**
     * Returns sql string with bind values
     * @param  object $query query Builder object
     * @return query Builder object
     * @internal param string $sort
     */
    public function scopeToSqlWithBindings($query) {
        $sql = $query->toSql();

        $sql = str_replace(['%', '?', '"'], ['%%', '%s', ''], $sql);
        $sql = vsprintf($sql, $query->getBindings());

        return $sql;
    }

    /**
     * allowedSatelliteTypes Get User Allowed Satellite Types
     * @return array
     */
    public static function allowedSatelliteTypes()
    {
        $user = Auth::user();

        $allowedSatelliteTypes = [];
        if ($user->globalUser()->can('use_rapideye')) {
            array_push($allowedSatelliteTypes, 'rapideye');
        }
        if ($user->globalUser()->can('use_sentinel')) {
            array_push($allowedSatelliteTypes, 'sentinel');
        }
        if ($user->globalUser()->can('use_landsat')) {
            array_push($allowedSatelliteTypes, 'landsat');
        }

        return $allowedSatelliteTypes;
    }
}
