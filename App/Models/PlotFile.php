<?php

namespace App\Models;

use App\Helpers\Helper;
use Config;
use Dompdf\Exception;
use View;
use App\Models\StaticModels\FarmingYear;
use App\Models\OrderPlotRel;
use App\Models\SoilGrid;
use App\Models\SoilPoints;
use App\Models\LayerPlot;
use DB;

class PlotFile extends BaseModel
{
    const TYPE_PLOT = 'plot';
    const TYPE_COVER = 'cover';

    public $timestamps = true;
    protected $table = 'su_satellite_plots_files';

    public function plot()
    {
        return $this->belongsTo(Plot::class, 'plot_id');
    }
}