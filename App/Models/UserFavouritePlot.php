<?php

namespace App\Models;

use Config;
use View;
use DB;

class UserFavouritePlot extends BaseModel
{
    protected $table = 'su_users_favourite_plots';
    protected $primaryKey = 'id';
    public $timestamps = false;


    /**
     * Get the user.
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'group_id', 'group_id');
    }

    /**
     * Get the plot.
     */
    public function plot()
    {
        return $this->belongsTo('App\Models\AllowablePlot', 'plot_id', 'elg_ident');
    }

    public static function findByUserAndPlot($elgIdent, $goupId)
    {
        return self::where('su_users_favourite_plots.plot_id', $elgIdent)
            ->where('su_users_favourite_plots.group_id', $goupId)
            ->first();
    }

    public static function getFavouritePlot($elgIdent, $goupId)
    {
        return self::select([
            'su_users_favourite_plots.id',
            'su_users_favourite_plots.plot_id',
            'su_users_favourite_plots.sowing_date',
            'su_users_favourite_plots.crop_code_id',
            'al.zemlishte as name',
            DB::raw("ST_X(ST_Transform(ST_Centroid(geom), 4326)) AS longitude"),
            DB::raw("ST_Y(ST_Transform(ST_Centroid(geom), 4326)) AS latitude")
        ])
            ->join('layer_allowable_final as al', 'su_users_favourite_plots.plot_id', '=', 'al.elg_ident')
            ->where('su_users_favourite_plots.plot_id', $elgIdent)
            ->where('su_users_favourite_plots.group_id', $goupId)
            ->first();
    }

    public static function getFavouritePlotsList($goupId)
    {
        return self::select([
            'su_users_favourite_plots.id',
            'su_users_favourite_plots.plot_id',
            'su_users_favourite_plots.sowing_date',
            'su_users_favourite_plots.crop_code_id',
            'al.zemlishte as name',
            DB::raw("ST_X(ST_Transform(ST_Centroid(geom), 4326)) AS longitude"),
            DB::raw("ST_Y(ST_Transform(ST_Centroid(geom), 4326)) AS latitude")
            ])
            ->join('layer_allowable_final as al', 'su_users_favourite_plots.plot_id', '=', 'al.elg_ident')
            ->where('su_users_favourite_plots.group_id', $goupId);
    }

    public static function isFavouritePlot($plotId, $userId)
    {
        $isFavouritePlot = self::where('plot_id', '=', $plotId)
            ->where('group_id', '=', $userId)
            ->first();

        return isset($isFavouritePlot);
    }

    public static function deleteFavouritePlot($elgIdent)
    {
        return self::where('plot_id', '=', $elgIdent)->first()->delete();
    }
}
