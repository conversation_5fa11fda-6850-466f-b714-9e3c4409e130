<?php

namespace App\Models;

use Auth;
use Config;
use DB;
use Request;

class OrderSoilVra extends BaseModel
{
    protected $table = 'su_orders_soil_vra';
    public $timestamps = false;

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'vector_data_json' => 'object',
        'rates' => 'array',
        'plot_geom_coords' => 'array',
        'bbox_max_x' => 'float',
        'bbox_min_x' => 'float',
        'bbox_max_y' => 'float',
        'bbox_min_y' => 'float'
    ];

    public function getByPlot($plotId, $farmYear)
    {
        return self::getByPlotQuery($plotId, $farmYear)->orderBy('su_orders_soil_vra.id', 'desc')
            ->groupBy('su_orders_soil_vra.id', 'so.status', 'slp.date', 'so.type', 'so.year', 'slp.satellite_type', 'slp.type', 'o.name', 'f.name', 'slp.element', 'p.geom', 'p.name')
            ->get();
    }

    public function plot()
    {
        return $this->belongsTo('App\Models\Plot', 'plot_id', 'gid');
    }

    public static function getByPlotQuery($plotId, $farmYear)
    {
        $crop_name = Config::get('globals.CROP_NAME');
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        return self::select([
            'su_orders_soil_vra.*',
            'so.status',
            'slp.date',
            'so.type as order_type',
            'so.year as year',
            'o.name as organization',
            'f.name as farm',
            'slp.element as soil_element',
            DB::raw('max(DISTINCT c.' . $crop_name . ') as crop'),
            DB::raw("round(((ST_Area(p.geom)/1000)*{$areaCoef})::numeric, 3) AS area"),
            'p.name as plot_name'
        ])
            ->join('su_satellite_orders as so', 'so.id', '=', 'su_orders_soil_vra.order_id')
            ->join('su_satellite_layers_plots as slp', 'slp.id', '=', 'su_orders_soil_vra.layer_id')
            ->join('su_satellite_plots as p', 'p.gid', '=', 'slp.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'p.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->join('su_organizations as o', 'o.id', '=', 'f.organization_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) use($farmYear) {
                $join->on('spc.plot_id', '=', 'p.gid')
                    ->where('spc.is_primary', '=', true)
                    ->where('spc.year', '=', $farmYear);
            })
            ->leftJoin('su_crop_codes AS c', 'c.id', '=', 'spc.crop_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('so.year', $farmYear)
            ->where('su_orders_soil_vra.plot_id', $plotId)
            ->where('so.type', 'soil_vra')
            ->whereNotIn('so.status', array_keys(
                    Order::getOrderStatuses()
                        ->filter(function ($val) {
                            if ($val === 'Error' || $val === 'Canceled') {
                                return $val;
                            }
                        })
                        ->toArray())
            );
    }

    /**
     * Get soil order vra details
     * 
     * @param int $id The OrderSoilVra id
     * @return mixed
     */
    public static function getVraOrderDetails(int $id)
    {
        return self::getVraOrderDetailsQuery($id)->first();
    }

    /**
     * Get soil order vra details query
     * 
     * @param int $id The OrderSoilVra id
     * @return mixed
     */
    public static function getVraOrderDetailsQuery(int $id)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        return self::select([
            'su_orders_soil_vra.*',
            'su_orders_soil_vra.vector_data as vector_data_json',
            'slp.element as soil_element',
            DB::raw("round(((ST_Area(p.geom)/1000)*{$areaCoef})::numeric, 3) AS area"),
            'so.status',
            'slp.date',
        ])
            ->join('su_satellite_orders as so', 'so.id', '=', 'su_orders_soil_vra.order_id')
            ->join('su_satellite_layers_plots as slp', 'slp.id', '=', 'su_orders_soil_vra.layer_id')
            ->join('su_satellite_plots as p', 'p.gid', '=', 'slp.plot_id')
            ->where('su_orders_soil_vra.id', $id)
            ->where('so.type', 'soil_vra');
    }

    public static function getByOrganizationAndIdsQuery(int $organizationId, array $soilOrderVraIds)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        return self::select([
            'su_orders_soil_vra.*',
            'so.uuid as order_uuid',
            'su_orders_soil_vra.vector_data as vector_data_json',
            'so.status',
            'slp.date',
            'so.type as order_type',
            'so.year as year',
            'o.name as organization',
            'f.name as farm',
            DB::raw("split_part(split_part(slp.layer_name, '_', 4), '-', 1) AS soil_element"),
            DB::raw("round(((ST_Area(p.geom)/1000)*{$areaCoef})::numeric, 3) AS area"),
            'p.name as plot_name'
        ])
            ->join('su_satellite_orders as so', 'so.id', '=', 'su_orders_soil_vra.order_id')
            ->join('su_satellite_layers_plots as slp', 'slp.id', '=', 'su_orders_soil_vra.layer_id')
            ->join('su_satellite_plots as p', 'p.gid', '=', 'slp.plot_id')
            ->join('su_farms as f', 'f.id', '=', 'p.farm_id')
            ->join('su_organizations as o', 'o.id', '=', 'f.organization_id')
            ->where('f.organization_id', $organizationId)
            ->whereIn('su_orders_soil_vra.id', $soilOrderVraIds)
            ->where('so.type', 'soil_vra')
            ->whereNotIn('so.status', ['error', 'canceled']);
    }

    public static function getVraDataForTFPlugin(int $userId, int $organizationId, int $serviceProviderId, int $soilOrderId, int $cellSize, int $rxProductLookupId): ?self
    {
        $dbProj = Config::get("globals.DEFAULT_DB_CRS");

        $vraOrderDataQuery = self::from('su_orders_soil_vra AS sosv')
            ->select(
                'sosv.plot_id AS plot_id',
                'sosv.id AS vra_order_id',
                'sosv.name AS vra_name',
                DB::raw("jsonb_array_elements(sosv.vector_data::jsonb->'features') AS vector_data")
            )
            ->where('sosv.id', $soilOrderId)
            ->whereNotNull('sosv.vector_data');

        $vraOrderPlotDataQuery = self::from('vra_order_data')
            ->select(
                'ssp.gid AS plot_id',
                'ssp.name AS plot_name',
                'ssp.area AS plot_area',
                'ssp.geom AS plot_geom',
                DB::raw("(ST_AsGeoJSON(ST_Multi(ST_Force3D(ST_Transform(ssp.geom, 4326))))::json->'coordinates')::jsonb AS plot_geom_coords"),
                'sou.id AS grower_id',
                'su.username AS grower_name',
                'sf.id AS farm_id',
                'sf.name AS farm_name',
                'vra_order_data.vra_order_id',
                'vra_order_data.vra_name',
                DB::raw("ST_Envelope(ST_Transform(ssp.geom, 4326)) AS bbox"),
                DB::raw("ST_Transform(ST_SetSRID(ST_GeomFromGeoJSON(vector_data->>'geometry'), 4326), {$dbProj}) AS plot_zone"),
                DB::raw("(vra_order_data.vector_data ->'properties'->>'DN')::int AS rate")
            )
            ->join('su_satellite_plots AS ssp', 'ssp.gid', '=', 'vra_order_data.plot_id')
            ->join('su_farms AS sf', 'sf.id', '=', 'ssp.farm_id')
            ->join('su_organizations_users AS sou', function ($join) use ($organizationId, $userId) {
                $join->on('sou.organization_id', '=', 'sf.organization_id');
                $join->on('sou.organization_id', '=', DB::raw($organizationId));
                $join->on('sou.user_id', '=', DB::raw($userId));
            })
            ->join('su_organizations AS so', 'so.id', '=', 'sou.organization_id')
            ->join('su_users AS su', 'su.id', '=', 'sou.user_id')
            ->where(function ($q) use ($serviceProviderId) {
                $q->where([
                    ['so.active', DB::raw('true')],
                    ['so.service_provider_id', $serviceProviderId]
                ]);
                $q->whereNotNull('so.identity_number');
            });

        $gridDataQuery = self::from('vra_order_plot_data')
            ->select(
                DB::raw("jsonb_agg(ARRAY[vra_order_plot_data.rate, {$rxProductLookupId}] ORDER BY grid.row_num, grid.col_num) AS rates"),
                DB::raw("MAX(grid.col_num) AS col_count"),
                DB::raw("MAX(grid.row_num) AS row_count")
            )
            ->join(
                DB::raw("
                    get_multipoint_data(
                            makegrid(vra_order_plot_data.plot_geom, {$cellSize})
                    ) AS grid(geom, row_num, col_num)
                "),
                DB::raw("st_intersects(grid.geom, vra_order_plot_data.plot_zone)"), '=', DB::raw("true")
        );

        $resultQuery = self::from(DB::raw("grid_data, vra_order_plot_data"))
            ->withExpression('vra_order_data', $vraOrderDataQuery)
            ->withExpression('vra_order_plot_data', $vraOrderPlotDataQuery)
            ->withExpression('grid_data', $gridDataQuery)
            ->select(
                'vra_order_plot_data.plot_id',
                'vra_order_plot_data.plot_name',
                'vra_order_plot_data.plot_area',
                'vra_order_plot_data.plot_geom_coords',
                'vra_order_plot_data.grower_id',
                'vra_order_plot_data.grower_name',
                'vra_order_plot_data.farm_id',
                'vra_order_plot_data.farm_name',
                'vra_order_plot_data.vra_order_id',
                DB::raw("COALESCE(vra_order_plot_data.vra_name, '') AS vra_name"),
                DB::raw("ST_XMax(vra_order_plot_data.bbox) AS bbox_max_x"),
                DB::raw("ST_YMax(vra_order_plot_data.bbox) AS bbox_max_y"),
                DB::raw("ST_XMin(vra_order_plot_data.bbox) AS bbox_min_x"),
                DB::raw("ST_YMin(vra_order_plot_data.bbox) AS bbox_min_y"),
                'grid_data.rates',
                'grid_data.col_count',
                'grid_data.row_count'
            )->distinct();

        return $resultQuery->first();
    }

    public static function getFilteredOrdersQuery(array $filters = [])
    {
        $crop_name = Config::get('globals.CROP_NAME');
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query = self::select([
            'su_orders_soil_vra.id',
            'su_orders_soil_vra.order_id',
            'su_orders_soil_vra.plot_id',
            'su_orders_soil_vra.layer_id',
            'su_orders_soil_vra.created',
            'su_orders_soil_vra.class_number',
            'su_orders_soil_vra.flat_rate',
            'su_orders_soil_vra.data',
            'su_orders_soil_vra.flat_rate_total',
            'su_orders_soil_vra.variable_rate_total',
            'su_orders_soil_vra.difference',
            'su_orders_soil_vra.difference_percent',
            'su_orders_soil_vra.vector_data',
            'su_orders_soil_vra.product_percent',
            'su_orders_soil_vra.product_text',
            'su_orders_soil_vra.tiff_path',
            'su_orders_soil_vra.name',
            'so.status',
            'slp.date',
            'so.type as order_type',
            'so.year as year',
            'o.name as organization',
            'f.name as farm',
            'slp.satellite_type',
            'slp.type',
            'slp.element as soil_element',
            DB::raw('max(DISTINCT c.' . $crop_name . ') as crop'),
            DB::raw("round(((ST_Area(p.geom)/1000)*{$areaCoef})::numeric, 3) AS area"),
            'p.name as plot_name'
        ])
            ->join('su_satellite_orders as so', 'so.id', '=', 'su_orders_soil_vra.order_id')
            ->join('su_satellite_layers_plots as slp', 'slp.id', '=', 'su_orders_soil_vra.layer_id')
            ->join('su_satellite_plots as p', 'p.gid', '=', 'slp.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'p.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->join('su_organizations as o', 'o.id', '=', 'f.organization_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) use($filters) {
                $join->on('spc.plot_id', '=', 'p.gid')
                    ->where('spc.is_primary', '=', true);
                if(isset($filters['farm_year']) && $filters['farm_year']) {
                    $join ->where('spc.year', '=', $filters['farm_year']);
                }
            })
            ->leftJoin('su_crop_codes AS c', 'c.id', '=', 'spc.crop_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('so.type', 'soil_vra');

        if(isset($filters['farm_year']) && $filters['farm_year']) {
            $query->where('so.year', $filters['farm_year']);
        }

        if(isset($filters['element']) && $filters['element']) {
            $query->where('slp.element', $filters['element']);
        }

        if (isset($filters['plot_ids']) && count($filters['plot_ids']) > 0) {
            $query->whereIn('su_orders_soil_vra.plot_id', $filters['plot_ids']);
        }

        if (isset($filters['farm_ids']) && count($filters['farm_ids']) > 0) {
            $query->whereIn('p.farm_id', $filters['farm_ids']);
        }
        if (isset($filters['crop_ids']) && count($filters['crop_ids']) > 0) {
            $query->where(function ($q) use ($filters) {
                $cropIdsNullValueIdx = array_search(null, $filters['crop_ids']);

                // Check if there is null value in $filters['crop_ids']
                if ($cropIdsNullValueIdx !== false) {
                    unset($filters['crop_ids'][$cropIdsNullValueIdx]); //remove null value from array

                    $q->whereIn('spc.crop_id', $filters['crop_ids'])
                        ->orWhere('spc.crop_id', null);
                    return;
                }

                $q->whereIn('spc.crop_id', $filters['crop_ids']);
            });
        }

        $query->whereNotIn('so.status', array_keys(
            Order::getOrderStatuses()
                ->filter(function ($val) {
                    if ($val === 'Error' || $val === 'Canceled') {
                        return $val;
                    }
                })
                ->toArray())
        );
        $query->orderBy('su_orders_soil_vra.id', 'desc')
            ->groupBy('su_orders_soil_vra.id', 'so.status', 'slp.date', 'so.type', 'so.year', 'slp.satellite_type', 'slp.type', 'o.name', 'f.name', 'slp.element', 'p.geom', 'p.name');

        return $query;
    }
}
