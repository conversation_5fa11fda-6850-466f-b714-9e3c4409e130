<?php

namespace App\Models;

class Recommendation extends BaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_users_recommendations';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'id', 'organization_id');
    }
}
