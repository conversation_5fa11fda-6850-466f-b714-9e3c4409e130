<?php

namespace App\Models;


/**
 * Class FarmTrackReportsLog
 * @package App
 *
 * @property int $integration_id
 * @property timestamp $start_time
 * @property timestamp $end_time
 * @property json $request_parameters
 * @property int $integration_reports_types_id
 * @property srting $state
 * @property json $error
 * @property string $tmp_table_name
 */
class FarmTrackReportsLog extends BaseModel
{
    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'request_parameters' => 'array',
        'error' => 'array',
    ];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'farm_track_reports_log';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @param int $organizationId
     * @param string|null $packageSlugShort
     * @param string|null $state
     * @param array|null $reportNames
     *
     * @return mixed
     */
    public static function getFarmTrackReportsByOrganization(int $organizationId, ?string $packageSlugShort = null, ?string $state = null, ?array $reportNames = null)
    {
        $query = self::select(
            'sirt.name as report_name',
            'farm_track_reports_log.id',
            'farm_track_reports_log.state',
            'farm_track_reports_log.error',
            'farm_track_reports_log.start_time',
            'farm_track_reports_log.end_time',
            'farm_track_reports_log.request_parameters'
        )
            ->join('su_integration as si', 'si.id', '=', 'farm_track_reports_log.integration_id')
            ->join('su_integration_reports_types as sirt', 'sirt.id', '=', 'farm_track_reports_log.integration_reports_types_id')
            ->where('si.organization_id', $organizationId)
            ->orderBy('farm_track_reports_log.id', 'desc');

        if ($reportNames) {
            $query->whereIn('sirt.name', $reportNames);
        }

        if ($state) {
            $query->where('farm_track_reports_log.state', $state);
        }

        if ($packageSlugShort) {
            $query->where('si.package_slug_short', $packageSlugShort);
        }

        return $query->get()->toArray();
    }
}
