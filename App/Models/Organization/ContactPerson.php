<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Models\Organization;


use App\Models\BaseModel;

class ContactPerson extends BaseModel
{
    public    $timestamps = true;
    protected $table      = 'su_organizations_contact_persons';
    protected $primaryKey = 'id';
    protected $fillable   = ['name', 'email', 'phone', 'is_representative', 'position', 'note'];
    protected $hidden     = [];

    public function organization()
    {
        return $this->belongsTo('App\Models\Organization', 'organization_id', 'id');
    }
}
