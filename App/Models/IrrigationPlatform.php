<?php

namespace App\Models;


use phpseclib\Math\BigInteger;
use DB;
use Config;

/**
 * Class IrrigationPlatform
 * @package App
 *
 * @property BigInteger $contract_id
 * @property string $centre
 * @property string $name
 * @property integer $farm_id
 * @property boolean $status
 */
class IrrigationPlatform extends BaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_irrigation_platforms';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['contract_id', 'centre', 'centre_buff', 'name', 'farm_id', 'status'];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'geom_geojson' => 'object',
        'task_geom_geojson' => 'object',
        'task_segment_geom_geojson' => 'object',
        'center' => 'array'
    ];

    public static function getIrrigations($name = '')
    {
        $query = self::select(
            'su_irrigation_platforms.id',
            'su_irrigation_platforms.name',
            'contract_id',
            'f.name as farm_name',
            DB::raw('to_char(su_irrigation_platforms.created_at, \'YYYY-MM-DD\') as created_at'),
            'farm_id',
            'status',
            DB::raw('ST_X(ST_Transform(centre, 4326)) as longitude'),
            DB::raw('ST_Y(ST_Transform(centre, 4326)) as latitude')
        )
            ->join('su_farms as f', 'f.id', '=', 'su_irrigation_platforms.farm_id');

        if ($name) {
            $query->where('su_irrigation_platforms.name', 'ilike', trim('%' . $name . '%'));
        }

        return $query;
    }

    public static function getIrrigationBuId($id)
    {
        return self::select('id', "name", 'contract_id', 'farm_id', 'status', DB::raw('st_asgeojson(centre) as center'))
            ->where('id', '=', $id);
    }

    public static function irrigationReport(int $organizationId)
    {
        $query = DB::table('su_irrigation_events as sie')
            ->select(
                'f.name as farm_name',
                'ssp.name as plot_name',
                'scc.crop_name_ro as crop_name',
                DB::raw('(ssp.area / 10)::numeric(10,2) as area'),
                DB::raw('(st_area(st_union(st_intersection(sie.coverage, ssp.geom)))/ 10000)::numeric(10,3) as covered_area'),
                DB::raw('(avg(sie.water_rate) * (st_area(st_union(st_intersection(sie.coverage, ssp.geom))))/ 10000)::numeric(10,2) as total_water'),
                'siu.name as pivot_name',
                DB::raw('round(avg(sie.water_rate),2) as avg_rate'),
                DB::raw('string_agg(distinct sie.start_time::text, \',\') as dates')
            )
            ->join('su_irrigation_platforms as ip', 'ip.id', '=', 'sie.platform_id')
            ->join('su_satellite_plots as ssp', function ($join) {
                $join->on(DB::raw('st_intersects(sie.coverage, ssp.geom)'), DB::raw('true::boolean'));
            })
            ->join('su_farms as f', 'f.id', '=', 'ssp.farm_id')
            ->join('su_satellite_plots_crops as sspc', function ($join) {
                $join->on('sspc.plot_id', '=', 'ssp.gid');
                $join->on('sspc.from_date', '<=', 'sie.start_time');
                $join->on('sspc.to_date', '>=', 'sie.start_time');
            })
            ->join('su_crop_codes as scc', 'scc.id', '=', 'sspc.crop_id')
            ->join('su_irrigation_units as siu', 'siu.id', '=', 'sie.irrigation_unit_id')
            ->where('f.organization_id', $organizationId)
            ->where('sspc.is_primary', true)
            ->where('sie.type', 'Irrigation')
            ->groupBy('f.id', 'ssp.gid', 'sie.irrigation_unit_id', 'siu.name', 'scc.crop_name_ro')
            ->orderBy('ssp.upload_date');

        return $query;
    }

    /**
     * @param int $organizationId
     * @param array $filter
     * @param int $limit
     * @param int $page
     * @return array
     */
    public static function getIrrigationTasksList(int $organizationId, array $filter)
    {
        $eventTypesColorsQuery = IrrigationEvent::getEventTypesColorsQuery();

        $query = self::withExpression('irrigation_event_types_colors', $eventTypesColorsQuery)
            ->join('su_irrigation_events AS sie', 'sie.platform_id', '=', 'su_irrigation_platforms.id')
            ->join('su_irrigation_events_plots AS siep', 'siep.event_id', '=', 'sie.id')
            ->join('su_satellite_plots AS ssp', 'ssp.gid', '=', 'siep.plot_id')
            ->join('su_irrigation_units AS siu', 'siu.id', '=', 'sie.irrigation_unit_id')
            ->join('su_farms AS sf', 'sf.id', '=', 'ssp.farm_id')
            ->join('irrigation_event_types_colors AS ietc', 'ietc.event_type', '=', 'sie.type')
            ->leftJoin('su_irrigation_platforms AS dest_sip', 'sie.destination_platform_id', '=', 'dest_sip.id')
            ->leftJoin('su_farms AS dest_sf', 'dest_sf.id', '=', 'dest_sip.farm_id')
            ->where('siu.organization_id', $organizationId);

        if (isset($filter['from']) && isset($filter['to'])) {
            $query->whereRaw('sie.date BETWEEN to_timestamp(?)::date AND to_timestamp(?)::date', [$filter['from'], $filter['to']]);
        }

        if (isset($filter['farmIds']) && count($filter['farmIds']) > 0) {
            $query->whereIn('sf.id', $filter['farmIds']);
        }

        if (isset($filter['plotIds']) && count($filter['plotIds']) > 0) {
            $query->whereIn('ssp.gid', $filter['plotIds']);
        }

        if (isset($filter['cropIds']) && count($filter['cropIds']) > 0) {
            $query->whereIn('siep.crop_id', $filter['cropIds']);
        }

        if (isset($filter['platformIds']) && count($filter['platformIds']) > 0) {
            $query->whereIn('sie.platform_id', $filter['platformIds']);
        }

        if (isset($filter['eventPlotIds']) && count($filter['eventPlotIds']) > 0) {
            $query->whereIn('siep.id', $filter['eventPlotIds']);
        }

        if (isset($filter['types']) && count($filter['types']) > 0) {
            $query->whereIn('sie.type', $filter['types']);
        }

        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $query->select(
            'siep.id AS event_plot_id',
            'siep.thumbnail',
            DB::raw('CASE
                WHEN sie.destination_platform_id NOTNULL 
                    THEN  ST_AsGeoJSON(ST_Transform(ST_Collect(su_irrigation_platforms.centre, dest_sip.centre), 3857))
                ELSE ST_AsGeoJSON(ST_Transform(su_irrigation_platforms.centre, 3857))
                END AS geom_geojson
            '),
            'su_irrigation_platforms.name AS platform_name',
            'dest_sip.id AS dest_platform_id',
            'dest_sip.name AS dest_platform_name',
            'sf.name AS farm_name',
            'dest_sf.name AS dest_farm_name',
            'sie.start_time',
            'sie.end_time',
            DB::raw("round((siep.event_area * {$areaCoef})::numeric, 2) as event_area"),
            'sie.water_rate',
            'sie.type',
            'sie.unit_speed',
            DB::raw('
                CASE
                    WHEN sie.forward = 1 AND sie.rearward = 0 THEN \'FWD\'
                    WHEN sie.forward = 0 AND sie.rearward = 1 THEN \'RWD\'
                END AS direction
            '),
            'sie.state_description_type as state_description',
            DB::raw("
                CASE
                    WHEN sie.type = 'Transportation'::irrigation_events_types_enum THEN NULL
                    ELSE 
                        JSON_BUILD_OBJECT(
                            'type', 'Feature',
                            'geometry', ST_AsGeoJSON(st_transform(siep.geom, 3857))::jsonb,
                            'properties', JSON_BUILD_OBJECT(
                                'color', ietc.color,
                                'task_id', siep.id
                            )
                        )
                  END
                 AS task_geom_geojson
            ")
        )
            ->orderBy(DB::raw('start_time::date'), 'DESC')
            ->orderBy(DB::raw('su_irrigation_platforms.id'), 'DESC')
            ->orderBy(DB::raw('start_time::time'), 'ASC');


        $results = $query->paginate($filter['limit']);

        return [
            'total' => $results->total(),
            'rows' => $results->items(),
        ];
    }

    public static function getIrrigationTaskRawData(int $irrigationEventPlotId, int $organizationId): array
    {
        $eventTypesColorsQuery = IrrigationEvent::getEventTypesColorsQuery();

        $query = self::withExpression('irrigation_event_types_colors', $eventTypesColorsQuery)
            ->join('su_irrigation_events AS sie', 'sie.platform_id', '=', 'su_irrigation_platforms.id')
            ->join('su_irrigation_events_plots AS siep', 'siep.event_id', '=', 'sie.id')
            ->join('su_irrigation_data_raw AS sidr', 'sidr.irrigation_event_id', '=', 'sie.id')
            ->join('su_irrigation_units AS siu', 'siu.id', '=', 'sie.irrigation_unit_id')
            ->join('irrigation_event_types_colors AS ietc', 'ietc.event_type', '=', 'sie.type')
            ->join('su_satellite_plots AS ssp', 'ssp.gid', '=', 'siep.plot_id')
            ->where([
                ['siep.id', $irrigationEventPlotId],
                ['siu.organization_id', $organizationId]
            ])
            ->select(
                'siep.id AS event_plot_id',
                'ssp.gid AS plot_id',
                'ssp.name AS plot_name',
                'sidr.id AS irrigation_data_raw_id',
                'sidr.platform_id',
                'su_irrigation_platforms.name AS platfrom_name',
                'sidr.irrigation_unit_id AS unit_id',
                'siu.name AS unit_name',
                'siu.length AS length',
                'sidr.type AS state',
                DB::raw("EXTRACT (epoch FROM sidr.start_time) AS start_time"),
                DB::raw("EXTRACT (epoch FROM sidr.end_time) AS end_time"),
                DB::raw("CASE WHEN sidr.unit_speed NOTNULL THEN  sidr.unit_speed || ' %' ELSE NULL END AS speed"),
                DB::raw("CASE WHEN sidr.pressure NOTNULL THEN  sidr.pressure || ' bar' ELSE NULL END AS pressure"),
                DB::raw("CASE WHEN sidr.water_rate NOTNULL THEN  sidr.water_rate || ' m3/ha' ELSE '- m3/ha' END AS rate"),
                'sidr.angle',
                'sidr.state_description_type AS state_description',
                DB::raw("
                    CASE
                        WHEN sidr.forward = 1 AND sidr.rearward = 0 THEN 'FWD'
                        WHEN sidr.forward = 0 AND sidr.rearward = 1 THEN 'RWD'
                    END AS direction
                "),
                DB::raw("json_build_object(
                    'type', 'Feature',
                    'geometry', ST_AsGeoJSON(st_transform(sidr.segment, 3857))::jsonb,
                    'properties', json_build_object(
                        'color', ietc.color,
                        'task_id', siep.id
                    )
                ) AS task_segment_geom_geojson")
            )
            ->orderBy('sidr.start_time', 'ASC');

        return $query->get()->toArray();
    }
}
