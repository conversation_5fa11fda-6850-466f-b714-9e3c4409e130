<?php

namespace App\Models;

use DB;
use Config;


class Pin extends BaseModel
{

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_users_pins';

    protected $primaryKey = 'id';

    public $incrementing = false;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'images_json' => 'object',
    ];

    /**
     * Get the user for the file.
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'id', 'group_id');
    }

    public function farm()
    {
        return $this->belongsTo(Farm::class);
    }

    /**
     * @param int $userId
     * @param int $organizationId
     * @param string $fromDate
     * @param string $toDate
     * @param array $farmIds
     *
     * @return array
     */
    public function getPinsByDates(int $userId, int $organizationId, string $fromDate, string $toDate, array $farmIds): array
    {
        $query = self::join('su_farms_users as fu', 'fu.farm_id', '=', 'su_users_pins.farm_id')
            ->leftJoin('su_users_pins_images as upi', 'upi.pin_id', '=', 'su_users_pins.id')
            ->join('su_farms as f', 'f.id', '=', 'fu.farm_id')
            ->where('fu.user_id', $userId)
            ->where('su_users_pins.isDeleted', false)
            ->where('fu.is_visible', true)
            ->where('f.organization_id', $organizationId)
            ->where(DB::raw('su_users_pins.date::date'), '>=', $fromDate)
            ->where(DB::raw('su_users_pins.date::date'), '<=', $toDate);


        if (isset($farmIds) && count($farmIds) > 0) {
            $query->whereIn('f.id', $farmIds);
        }

        $imagesJsonQ = clone $query;
        $imagesJsonQ->select(
            'upi.id',
            DB::raw("json_build_object(
            'original', upfo.web_path,
            'thumb', upft.web_path,
            'cover', upfc.web_path
            ) as images")
        )
            ->join('su_users_pins_images_files AS upft', function ($join) {
                $join->on('upft.image_id', '=', 'upi.id');
                $join->where('upft.type', 'thumb');
            })
            ->join('su_users_pins_images_files as upfo', function ($join) {
                $join->on('upfo.image_id', '=', 'upi.id');
                $join->where('upfo.type', 'original');
            })->join('su_users_pins_images_files as upfc', function ($join) {
                $join->on('upfc.image_id', '=', 'upi.id');
                $join->where('upfc.type', 'cover');
            })
            ->groupBy('upi.id', "upfo.web_path",
                "upft.web_path",
                "upfc.web_path");

        $query->select(
            'su_users_pins.id',
            'su_users_pins.title',
            DB::raw("COALESCE(su_users_pins.comment, '') AS comment"),
            'su_users_pins.lon',
            'su_users_pins.lat',
            DB::raw("jsonb_agg(upf.web_path) as images"),
            DB::raw("jsonb_agg(images_json.images) as images_json"),
            "f.id as farm_id",
            "f.name as farm_name"
        )
            ->leftJoin('su_users_pins_images_files AS upf', function ($join) {
                $join->on('upf.image_id', '=', 'upi.id');
                $join->where('upf.type', 'thumb');
            })
            ->leftJoinSub($imagesJsonQ, 'images_json', 'images_json.id', 'upi.id');

        return $query->groupBy('su_users_pins.id', 'f.id')->get()->toArray();
    }

    /**
     * @param int $userId
     * @param int $organizationId
     * @param string $sort
     *
     * @return mixed
     */
    public function getIndexQuery(int $userId, int $organizationId, string $sort)
    {
        return self::select(
            DB::raw('MAX(sp.gid) AS gid'),
            'su_users_pins.id',
            'su_users_pins.title',
            DB::raw("COALESCE(su_users_pins.comment, '') AS comment"),
            'su_users_pins.date',
            'su_users_pins.lon',
            'su_users_pins.lat',
            DB::raw("array_to_json(array_agg(upf.web_path)) as images")
        )
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'su_users_pins.farm_id')
            ->leftJoin('su_users_pins_images as upi', 'upi.pin_id', '=', 'su_users_pins.id')
            ->leftJoin('su_users_pins_images_files AS upf', function ($join) {
                $join->on('upf.image_id', '=', 'upi.id');
                $join->where('upf.type', 'thumb');
            })
            ->leftJoin('su_satellite_plots as sp', function ($join) {
                $join->on('sp.farm_id', '=', 'uf.farm_id');
                $join->where(DB::raw('ST_Contains(sp.geom, ST_Transform(ST_SetSRID(ST_MakePoint(su_users_pins.lon, su_users_pins.lat), 900913), ' . Config::get("globals.DEFAULT_DB_CRS") . '))'),
                    '=', true);
            })
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', $userId)
            ->where('f.organization_id', $organizationId)
            ->where('su_users_pins.isDeleted', false)
            ->groupBy('su_users_pins.id')
            ->restOrderBy($sort);
    }

    /**
     * @param int $userId
     * @param int $soprId
     *
     * @return mixed
     */
    public function pinsForSoilSamples(int $userId, int $soprId)
    {
        return self::where('type', 'soil_samples')
            ->where('isDeleted', false)
            ->where('sopr_id', $soprId)
            ->get([
                'id',
                'title',
                DB::raw("COALESCE(comment, '') AS comment"),
                'lon',
                'lat',
                DB::raw("(CASE WHEN su_users_pins.group_id = {$userId} then true else false end) AS is_my_pin")
            ]);
    }

    /**
     * @param string|null $newTitle
     * @param string|null $newComment
     * @param int|null $newFarmId
     *
     * @return Pin
     */
    public function updatePin(?string $newTitle, ?string $newComment, ?int $newFarmId): Pin
    {
        $this->title = $newTitle;

        if (isset($newComment)) {
            $this->comment = $newComment;
        }

        if (isset($newFarmId)) {
            $this->farm_id = $newFarmId;
        }

        $this->save();

        return $this;
    }
}
