<?php

namespace App\Models;

use App\Helpers\Helper;
use Auth;
use Config;
use DB;
use mapObj;
use View;

class LayerPlot extends BaseModel
{

    const TYPE_INDEX = 'index';
    const TYPE_SOIL = 'soil';
    const TYPE_METEO = 'meteo';
    const TYPE_VRA = 'vra';
    const TYPE_INDEX_WATER = 'index_water';
    const TYPE_SOIL_VRA = 'soil_vra';

    const SATELLITE_TYPE_LANDSAT = 'landsat';
    const SATELLITE_TYPE_SENTINEL = 'sentinel';
    const SATELLITE_TYPE_RAPIDEYE = 'rapideye';


    protected $table = 'su_satellite_layers_plots';
    public $timestamps = true;
    protected $casts = [
        'stats' => 'array',
    ];

    protected $fillable = [
        'plot_id',
        'layer_name',
        'date',
        'date_time',
        'stats',
        'mean',
        'created_at',
        'order_id',
        'type',
        'satellite_type',
        'is_viewed',
        'clouds_percent',
        'has_water_pounds',
        'probe',
        'element',
        'unit',
        'stats_type'
    ];

    public function orderVra()
    {
        return $this->hasMany('App\Models\OrderVra.php', 'layer_id', 'id');
    }

    public function layerPlotFiles()
    {
        return $this->hasMany(LayerPlotFile::class, 'layer_plot_id', 'id');
    }

    public function getStatsInChartFormatAttribute()
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $dataArr = [['Index', $areaLabel, ['role' => 'style'], ['role' => 'tooltip']]];

        if (!is_array($this->stats)) {
            return $dataArr;
        }

        foreach ($this->stats as $key => $value) {

            $area = round($value * $areaCoef, 3);

            if ($this->order_type === 'index') {
                $indexColors = collect(Config::get('globals.INDEX_COLORS')[10]);

                if (isset($indexColors[$key])) {
                    $arrRow = [$key, (float)$area, $indexColors[$key][0], (float)($area) . ' ' . $areaLabel];
                    $dataArr[] = $arrRow;
                }
            } elseif ($this->order_type === 'index_water') {
                $indexColors = collect(Config::get('globals.WATER_INDEX_COLORS')[0]);

                if (isset($indexColors[$key])) {
                    $arrRow = [$key, (float)$area, $indexColors[$key][0], (float)($area) . ' ' . $areaLabel];
                    $dataArr[] = $arrRow;
                }
            } elseif ($this->order_type === 'soil') {
                $index = explode('|', $key);

                $name = explode('-', $this->compound);

                $colorSchemeFile = Config::get('globals.SOILS_PATH') . $name[0] . '_' . $this->type . '.txt';
                $indexColors = collect(Helper::getSoilColorScheme($colorSchemeFile));

                if (isset($index[1]) && isset($indexColors[$index[0]])) {
                    $arrRow = [$index[1], (float)$area, $indexColors[$index[0]], (float)($area) . ' ' . $areaLabel];
                    $dataArr[] = $arrRow;
                }
            }
        }

        return $dataArr;
    }

    public function getStatsInSoilChartFormatAttribute()
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = trans('general.' . strtolower(Config::get('globals.AREA_UNIT')));

        $dataArr = [['Index', $areaLabel, ['role' => 'style'], ['role' => 'annotation']]];

        if (!is_array($this->stats)) {
            return $dataArr;
        }

        $stats = array_reverse($this->stats);

        foreach ($stats as $key => $value) {

            $area = round($value * $areaCoef, 3);

            $index = explode('|', $key);

            $name = explode('-', $this->compound);
            $compound = $name[0];

            $colorSchemeFile = Config::get('globals.SOILS_PATH') . $compound . '_' . $this->type . '.txt';
            $indexColors = collect(Helper::getSoilColorScheme($colorSchemeFile));
            $indexLabels = collect(Helper::getSoilLabelScheme($colorSchemeFile));

            if (isset($index[1]) && isset($indexColors[$index[0]])) {
                $annotation = (float)($area) . ' ' . $areaLabel;

                if (isset($indexLabels[$index[0]])) {
                    $label = $indexLabels[$index[0]];
                    $annotation = trans("soils.{$compound}.{$label}") . ' - ' . $annotation;
                }

                $arrRow = [$index[1], (float)$area, $indexColors[$index[0]], $annotation];
                $dataArr[] = $arrRow;
            }

        }

        return $dataArr;
    }

    /**
     * Delete Soil Layers
     * @param $orderId
     * @param $plotId
     * @param $fileDate
     * @param $probe
     * @return void
     */
    public function deleteSoilLayers($orderId, $plotId, $fileDate, $probe)
    {

        $time = strtotime($fileDate);
        $fileDate = date('Y-m-d', $time);

        $result = self::select(['*'])
            ->where('order_id', $orderId)
            ->where('plot_id', $plotId)
            ->where('date', $fileDate)
            ->where('probe', $probe)
            ->where('type', 'soil')
            ->delete();
    }

    /**
     * Get Index Аnalysis
     * @param $userId
     */
    public function imagesDates(User $user, $fromDate, $toDate, $farmIds, $plotIds)
    {
        $query = self::select([
            'su_satellite_layers_plots.date',
            'su_satellite_layers_plots.satellite_type',
            DB::raw('AVG(su_satellite_layers_plots.clouds_percent) as clouds_percent')
        ])
            ->join('su_satellite_orders AS so', 'so.id', '=', 'su_satellite_layers_plots.order_id')
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'sp.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', Auth::user()->id)
            ->where('f.organization_id', Auth::user()->lastChosenOrganization->id)
            ->where('so.status', 'processed')
            ->where('su_satellite_layers_plots.type', 'index')
            ->where('su_satellite_layers_plots.satellite_type', '=', 'sentinel')
            ->groupBy('su_satellite_layers_plots.date', 'su_satellite_layers_plots.satellite_type')
            ->orderBy('su_satellite_layers_plots.date', 'ASC');

        if ($fromDate) {
            $query->where(DB::raw('su_satellite_layers_plots.date::DATE'), '>=', $fromDate);
        }

        if ($toDate) {
            $query->where(DB::raw('su_satellite_layers_plots.date::DATE'), '<=', $toDate);
        }

        if (isset($farmIds) && count($farmIds) > 0) {
            $query->whereIn('sp.farm_id', $farmIds);
        }

        if (isset($plotIds) && count($plotIds) > 0) {
            $query->whereIn('sp.gid', $plotIds);
        }

        return $query;
    }

    /**
     * Get Index Аnalysis
     * @param $userId
     */
    public static function imagesDatesByPlotAndFarmingYear(int $plotId, int $farmYear)
    {
        $query = self::select([
            'so.id as order_id',
            'so.uuid as order_uuid',
            'sp.uuid as plot_uuid',
            'sp.gid as plot_id',
            'su_satellite_layers_plots.date'
        ])
            ->join('su_satellite_orders AS so', 'so.id', '=', 'su_satellite_layers_plots.order_id')
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->whereIn('so.status', ['processed', 'waiting_payment', 'paid'])
            ->where('su_satellite_layers_plots.plot_id', $plotId)
            ->where('su_satellite_layers_plots.type', self::TYPE_SOIL)
            ->where('so.year', $farmYear)
            ->groupBy('so.id', 'so.uuid', 'sp.uuid', 'sp.gid', 'su_satellite_layers_plots.date')
            ->orderBy('su_satellite_layers_plots.date', 'DESC');


        return $query;
    }

    /**
     * Get Avg Index
     * @param $userId
     */
    public function avgIndexQuery(User $user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)
    {
        $query = self::join('su_satellite_orders AS so', 'so.id', '=', 'su_satellite_layers_plots.order_id')
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) use ($year) {
                $join->on('spc.plot_id', '=', 'su_satellite_layers_plots.plot_id')
                    ->on('spc.from_date', '<=', 'su_satellite_layers_plots.date')
                    ->on('spc.to_date', '>=', 'su_satellite_layers_plots.date')
                    ->where('spc.is_primary', '=', true)
                    ->where('spc.year', '=', $year);
            })
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'sp.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->where('uf.user_id', $user->id)
            ->where('f.organization_id', $user->lastChosenOrganization->id)
            ->where('so.status', 'processed')
            ->where('so.year', $year)
            ->where('su_satellite_layers_plots.type', $type)
            ->where('su_satellite_layers_plots.satellite_type', $satelliteType)
            ->where('su_satellite_layers_plots.date', $date)
            ->whereNotNull('stats');

        if (is_array($cropIds) && count($cropIds) > 0) {
            $query->where(function ($q) use ($cropIds) {
                if (in_array(null, $cropIds)) {
                    $q->whereNull('spc.crop_id');

                    $cropIds = array_filter($cropIds, function ($item) {
                        return isset($item);
                    });

                    if (count($cropIds) > 0) {
                        $q->orWhereIn('spc.crop_id', $cropIds);
                    }

                    return $q;
                }

                return $q->whereIn('spc.crop_id', $cropIds);
            });
        }

        if (is_array($farmIds) && count($farmIds) > 0) {
            $query->whereIn('sp.farm_id', $farmIds);
        }

        if (is_array($plotIds) && count($plotIds) > 0) {
            $query->whereIn('sp.gid', $plotIds);
        }

        return $query;
    }

    /**
     * Get Avg Index ECharts
     * @param $user
     * @param $date
     * @param $satelliteType
     * @param $year
     * @param $type
     * @param $plotIds
     * @param $cropIds
     * @param $farmIds
     */
    public function avgIndexECharts(User $user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)
    {
        return $this->avgIndexQuery($user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)->select([
            DB::raw('round(SUM((stats::json->>\'0-10\')::NUMERIC)::NUMERIC, 0) AS "0-10"'),
            DB::raw('round(SUM((stats::json->>\'10-20\')::NUMERIC)::NUMERIC, 0) AS "10-20"'),
            DB::raw('round(SUM((stats::json->>\'20-30\')::NUMERIC)::NUMERIC, 0) AS "20-30"'),
            DB::raw('round(SUM((stats::json->>\'30-40\')::NUMERIC)::NUMERIC, 0) AS "30-40"'),
            DB::raw('round(SUM((stats::json->>\'40-50\')::NUMERIC)::NUMERIC, 0) AS "40-50"'),
            DB::raw('round(SUM((stats::json->>\'50-60\')::NUMERIC)::NUMERIC, 0) AS "50-60"'),
            DB::raw('round(SUM((stats::json->>\'60-70\')::NUMERIC)::NUMERIC, 0) AS "60-70"'),
            DB::raw('round(SUM((stats::json->>\'70-80\')::NUMERIC)::NUMERIC, 0) AS "70-80"'),
            DB::raw('round(SUM((stats::json->>\'80-90\')::NUMERIC)::NUMERIC, 0) AS "80-90"'),
            DB::raw('round(SUM((stats::json->>\'90-100\')::NUMERIC)::NUMERIC, 0) AS "90-100"'),
            DB::raw('round(SUM((stats::json->>\'100-110\')::NUMERIC)::NUMERIC, 0) AS "100-110"'),
        ]);
    }

    /**
     * Get Avg Index GCharts
     * @param $user
     * @param $date
     * @param $satelliteType
     * @param $year
     * @param $type
     * @param $plotIds
     * @param $cropIds
     * @param $farmIds
     */
    public function avgIndexGCharts(User $user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)
    {
        return $this->avgIndexQuery($user, $date, $satelliteType, $year, $type, $plotIds, $cropIds, $farmIds)->select([
            DB::raw('round(SUM(su_satellite_layers_plots.mean*sp.area)/SUM(sp.area)) as mean'),
            DB::raw('round(SUM((stats::json->>\'0-10\')::NUMERIC)::NUMERIC, 0) AS "0-10"'),
            DB::raw('round(SUM((stats::json->>\'10-20\')::NUMERIC)::NUMERIC, 0) AS "10-20"'),
            DB::raw('round(SUM((stats::json->>\'20-30\')::NUMERIC)::NUMERIC, 0) AS "20-30"'),
            DB::raw('round(SUM((stats::json->>\'30-40\')::NUMERIC)::NUMERIC, 0) AS "30-40"'),
            DB::raw('round(SUM((stats::json->>\'40-50\')::NUMERIC)::NUMERIC, 0) AS "40-50"'),
            DB::raw('round(SUM((stats::json->>\'50-60\')::NUMERIC)::NUMERIC, 0) AS "50-60"'),
            DB::raw('round(SUM((stats::json->>\'60-70\')::NUMERIC)::NUMERIC, 0) AS "60-70"'),
            DB::raw('round(SUM((stats::json->>\'70-80\')::NUMERIC)::NUMERIC, 0) AS "70-80"'),
            DB::raw('round(SUM((stats::json->>\'80-90\')::NUMERIC)::NUMERIC, 0) AS "80-90"'),
            DB::raw('round(SUM((stats::json->>\'90-100\')::NUMERIC)::NUMERIC, 0) AS "90-100"'),
            DB::raw('round(SUM((stats::json->>\'100-110\')::NUMERIC)::NUMERIC, 0) AS "100-110"'),
        ]);
    }

    /**
     * Get Avg Index
     * @param $userId
     */
    public function statsInChartFormat($avgData)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $totalArea = 0;
        $dataArr = [['Index', $areaLabel, ['role' => 'style'], ['role' => 'annotation'], ['role' => 'tooltip']]];

        if (!isset($avgData[0]["0-10"])) {
            return $dataArr;
        }

        $stats = $avgData[0];

        foreach ($stats as $key => $value) {

            $area = round($value * $areaCoef, 3);

            $indexColors = collect(Config::get('globals.INDEX_COLORS')[10]);

            if (isset($indexColors[$key])) {
                $arrRow = [
                    $key,
                    (float)$area,
                    $indexColors[$key][0],
                    (float)($area) . ' ' . $areaLabel,
                    $key . ': ' . (float)($area) . ' ' . $areaLabel
                ];
                $dataArr[] = $arrRow;
                $totalArea = $totalArea + $value * $areaCoef;
            }
        }

        $totalArea = round($totalArea) . ' ' . $areaLabel;
        $meanIndex = 0;

        if (isset($stats['mean'])) {
            $meanIndex = $stats['mean'];
        }

        $result = array('total_area' => $totalArea, 'chart_data' => $dataArr, 'mean_index' => $meanIndex);

        return $result;
    }

    public static function getPlotIdsByDates($beginDate, $endDate)
    {
        return self::select('plot_id')->whereBetween("date", [$beginDate, $endDate])->distinct()->get();
    }

    public static function updateHasWaterPounds($beginDate, $endDate, $tiles)
    {
        $tilesIds = array_unique(array_column($tiles, "tileId"));

        self::whereBetween("date", [$beginDate, $endDate])
            ->update(["has_water_pounds" => "false"]);

        self::join('su_satellite_plots AS sp', 'sp.gid', '=', 'plot_id')
            ->join('su_satellite_water_pounds AS wp',
                DB::raw('ST_Intersects(ST_Transform(wp.geom, ' . Config::get('globals.DEFAULT_DB_CRS') . '), sp.geom)'),
                '=', DB::raw('true'))
            ->whereBetween('su_satellite_layers_plots.date', [$beginDate, $endDate])
            ->update(["has_water_pounds" => "true"]);
    }

    public static function plotIndexImagePath($plotId, $indexDate, $satelliteType)
    {
        return self::select([
            'slpf.path'
        ])
            ->join('su_satellite_layers_plots_files AS slpf', 'slpf.layer_plot_id', '=', 'su_satellite_layers_plots.id')
            ->where('su_satellite_layers_plots.plot_id', $plotId)
            ->where('su_satellite_layers_plots.date', $indexDate)
            ->where('su_satellite_layers_plots.satellite_type', $satelliteType)
            ->where('su_satellite_layers_plots.type', 'index')
            ->where('slpf.type', 'PNG')
            ->first();
    }

    public function getAvgMeanForDate($fromDate, $toDate, $cropIds, $farmIds, $plotIds)
    {
        $avgData = $this->select(DB::raw('avg(su_satellite_layers_plots.mean)::integer as avg_mean'), DB::raw('extract(epoch from su_satellite_layers_plots.date)*1000 as dates'))
            ->join('su_satellite_orders', 'su_satellite_orders.id', 'su_satellite_layers_plots.order_id')
            ->join('su_farms', 'su_farms.organization_id', 'su_satellite_orders.organization_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'su_satellite_layers_plots.plot_id')
                    ->on('spc.from_date', '<=', 'su_satellite_layers_plots.date')
                    ->on('spc.to_date', '>=', 'su_satellite_layers_plots.date')
                    ->where('spc.is_primary', '=', true);
            })
            ->whereBetween("su_satellite_layers_plots.date", [DB::raw('to_timestamp(' . $fromDate . ')::date'), DB::raw('to_timestamp(' . $toDate . ')::date')])
            ->where('su_satellite_layers_plots.type', 'index')
            ->where('su_satellite_orders.organization_id', Auth::user()->lastChosenOrganization->id);

        if (is_array($farmIds) && count($farmIds) > 0) {
            $avgData->whereIn('su_farms.id', $farmIds);
        }

        if (is_array($plotIds) && count($plotIds) > 0) {
            $avgData->whereIn('su_satellite_layers_plots.plot_id', $plotIds);
        }

        if (is_array($cropIds) && count($cropIds) > 0) {
            $avgData->where(function ($q) use ($cropIds) {
                if (in_array(null, $cropIds)) {
                    $q->whereNull('spc.crop_id');

                    $cropIds = array_filter($cropIds, function ($item) {
                        return isset($item);
                    });

                    if (count($cropIds) > 0) {
                        $q->orWhereIn('spc.crop_id', $cropIds);
                    }

                    return $q;
                }

                return $q->whereIn('spc.crop_id', $cropIds);
            });
        }

        $avgData->groupBy('su_satellite_layers_plots.date');
        $avgData->orderBy('dates', 'asc');

        $data = DB::table('avgData')
            ->withExpression('avgData', $avgData)
            ->select(DB::raw('json_agg(avg_mean) as avg_mean'), DB::raw('json_agg(dates) as dates'))
            ->get()->first();

        return ["avg_mean" => json_decode($data->avg_mean, true), "dates" => json_decode($data->dates, true)];
    }

    public static function getDataQuery()
    {
        return self::join('su_satellite_orders AS so', 'so.id', '=', 'su_satellite_layers_plots.order_id')
            ->join('su_satellite_plots AS sp', 'sp.gid', '=', 'su_satellite_layers_plots.plot_id')
            ->join('su_farms_users AS uf', 'uf.farm_id', '=', 'sp.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'uf.farm_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'su_satellite_layers_plots.plot_id')
                    ->on('spc.from_date', '<=', 'su_satellite_layers_plots.date')
                    ->on('spc.to_date', '>=', 'su_satellite_layers_plots.date')
                    ->where('spc.is_primary', '=', true);
            });
    }

    /**
     * Create soil analysis chart structure
     *
     * @param array $filters
     * @param array $soilElementsRanges
     *
     * @return mixed
     */
    public static function getSoilAnalysisChartData(array $filters, array $soilElementsRanges)
    {
        $areaCoef = Config::get('globals.DKA_' . Config::get('globals.AREA_UNIT'));

        $elements_data_laravel = DB::table('su_satellite_layers_plots as sspl')
            ->select(
                DB::raw('element::text'),
                DB::raw("
                    case
                        when(nullif(data[2], ''))::decimal(5,1) = 0 then null
                        when (nullif(data[2], ''))::decimal(5,1) is null then (nullif(data[3], ''))::decimal(5,1)
                        else (nullif(data[2], ''))::decimal(5,1)
                    end as from"),
                DB::raw("
                    case
                        when (nullif(data[2], ''))::decimal(5,1) is null then null
                        else (nullif(data[3], ''))::decimal(5,1)
                    end as to
                "),
                DB::raw("round((sum(ranges.value::decimal) * " . $areaCoef . ")::numeric, 3) as area")
            )
            ->crossJoin(DB::raw("json_each_text(sspl.stats::json) as ranges"))
            ->crossJoin(DB::raw("regexp_split_to_array(ranges.key, '(\||\-|>=)') as data"))
            ->leftJoin('su_satellite_plots as ssp', 'sspl.plot_id', '=', 'ssp.gid')
            ->leftJoin('su_satellite_orders as sso', 'sspl.order_id', '=', 'sso.id')
            ->leftJoin('su_satellite_plots_crops as sspc', function ($q) use ($filters) {
                $q->on('sspc.plot_id', '=', 'ssp.gid')
                    ->where('sspc.is_primary', true);

                if (isset($filters['farm_year']) && $filters['farm_year']) {
                    $q->where('sspc.year', $filters['farm_year']);
                }
            })
            ->where('sspl.type', '=', 'soil')
            ->whereRaw('ranges.value::decimal > 0')
            ->where('sspl.stats_type', '=', 'summarized')
            ->whereIn('sspl.element', $filters['elements']) // Filter by elements from CMS
            ->whereRaw('gs_is_json(sspl.stats)')
            ->where('sso.type', '=', 'soil');


        if (isset($filters['farm_ids']) && count($filters['farm_ids']) > 0) {
            $elements_data_laravel->whereIn('ssp.farm_id', $filters['farm_ids']);
        }

        if (isset($filters['plot_ids']) && count($filters['plot_ids']) > 0) {
            $elements_data_laravel->whereIn('ssp.gid', $filters['plot_ids']);
        }

        if (isset($filters['plot_name']) && $filters['plot_name']) {
            $elements_data_laravel->where('ssp.name', $filters['plot_name']);
        }

        if (isset($filters['farm_year']) && $filters['farm_year']) {
            $elements_data_laravel->where('sso.year', $filters['farm_year']);
        }

        if (isset($filters['organization_id']) && $filters['organization_id']) {
            $elements_data_laravel->where('sso.organization_id', $filters['organization_id']);
        }

        if (isset($filters['order_uuids']) && count($filters['order_uuids']) > 0) {
            $elements_data_laravel->whereIn('sso.uuid', $filters['order_uuids']);
        }

        if (isset($filters['date']) && $filters['date']) {
            $elements_data_laravel->where('sspl.date', $filters['date']);
        }

        if (isset($filters['crop_ids']) && count($filters['crop_ids']) > 0) {
            $elements_data_laravel->whereIn('sspc.crop_id', $filters['crop_ids']);
        }

        $elements_data_laravel->groupBy('element', 'data')
            ->orderBy('element', 'ASC')
            ->orderBy('to', 'ASC');

        // Insert elements with ranges from CMS
        $elements_data_cms = DB::table(DB::raw('json_each(\'' . json_encode($soilElementsRanges) . '\') a'))
            ->selectRaw('
                    a.key as element,
                    (b.value->>\'from\')::decimal(5,1) "from",
                    (b.value->>\'to\')::decimal(5,1) "to",
                    b.value->>\'color\' "color",
                    b.value->>\'label\' "label"
                ')
            ->crossJoin(DB::raw('json_array_elements(a.value) b'))
            ->orderBy('element', 'ASC')
            ->orderBy('to', 'ASC');

        // Array elements to be used to create data element for chart
        $all_elements_arr = DB::table('elements_data_laravel')
            ->selectRaw('array_agg(distinct "element") elements')
            ->orderBy('elements', 'ASC');

        $chart_raw_data = DB::table('elements_data_laravel as l')
            ->selectRaw('
            c.element::text,
            c.from,
            c.to,
            c.color,
            c.label,
            coalesce(l.area, 0) area
            ')
            ->join('elements_data_cms as c', function ($q) {
                $q->on('l.element', '=', 'c.element')
                    ->where(function ($quert) {
                        $quert->where('l.element', '=', DB::raw('c.element'))
                            ->where('l.from', '=', DB::raw('c.from'))
                            ->where('l.to', '=', DB::raw('c.to'));
                    })
                    ->orOn(function ($query) {
                        $query->where('l.element', '=', DB::raw('c.element'))
                            ->where('l.from', '=', DB::raw('c.from'))
                            ->whereNull('l.to')
                            ->whereNull('c.to');
                    })
                    ->orOn(function ($query) {
                        $query->where('l.element', '=', DB::raw('c.element'))
                            ->where('l.to', '=', DB::raw('c.to'))
                            ->whereNull('l.from')
                            ->whereNull('c.from');
                    });
            })
            ->orderBy('l.element', 'ASC')
            ->orderBy('l.to', 'ASC');

        $chart_raw_data_grouped = DB::table('chart_raw_data')
            ->selectRaw('
                label,
                element,
                "from",
                "to",
                color,
                array_fill(0, array[array_position(aea.elements, "element")-1])::numeric[] || array["area"] || array_fill(0, array[array_length(aea.elements, 1) - array_position(aea.elements, "element")])::numeric[] "data"
                ')
            ->leftJoin('all_elements_arr as aea', 'aea.elements', '@>', DB::raw('array["element"]'));

        $chartQuery = DB::table('chart_raw_data_grouped')
            ->withExpression('elements_data_laravel', $elements_data_laravel)
            ->withExpression('elements_data_cms', $elements_data_cms)
            ->withExpression('all_elements_arr', $all_elements_arr)
            ->withExpression('chart_raw_data', $chart_raw_data)
            ->withExpression('chart_raw_data_grouped', $chart_raw_data_grouped)
            ->selectRaw("
                jsonb_build_object(
                    'xAxis', json_build_object(
                                'type', 'category', 
                                'data', jsonb_agg(distinct element)
                                ),
                    'yAxis', json_build_object(
                                'type', 'value'), 
                    'series', array_agg(
                                jsonb_build_object(
                                    'name', label, 
                                    'type', 'bar', 
                                    'stack', 'total', 
                                    'label', jsonb_build_object('show', false), 
                                    'data', array_to_json(\"data\"), 
                                    'itemStyle', jsonb_build_object('color', color) 
                                )
                             )
                    )");

        $result = $chartQuery->pluck('jsonb_build_object')->first();

        return json_decode($result, true);

    }

}
