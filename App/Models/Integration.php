<?php

namespace App\Models;


/**
 * Class Integration
 * @package App
 *
 * @property string $token
 * @property string $organization_id
 * @property string $contract_id
 * @property string $package_id
 * @property string package_slug
 * @property string $integration_address
 * @property string $status
 */
class Integration extends BaseModel
{
    const ACTIVE = 'Active';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_integration';

    /**
     * @var string
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['token', 'organization_id', 'contract_id', 'package_id', 'package_slug_short', 'package_period', 'integration_address', 'status'];

    public function integrationAddress()
    {
        return $this->belongsTo('App\Models\IntegrationAddress', 'integration_address', 'id');
    }

    public function reportsTypes()
    {
        return $this->hasManyThrough('App\Models\IntegrationReportsTypes','App\Models\IntegrationsReports', 'integration_id', 'id', 'id', 'integration_reports_types_id');
    }

    public static function getLinkedQuery($organizationIds, $packageSlugShort = null, $status = null) {
        $query = self::with('integrationAddress')
            ->join('su_organizations as org', 'org.id' , '=', 'su_integration.organization_id')
            ->whereIn('org.id', $organizationIds);
        
        if($packageSlugShort) {
            $query->where('su_integration.package_slug_short', $packageSlugShort);
        }

        if($status) {
            $query->where('su_integration.status', $status);
        }

        return $query;
    }

    public static function getIntegrationsDataForSync($organizationId, $packageSlugShort, $status = 'Active', $reportTypesFilter = [])
    {
        $query = self::getLinkedQuery([$organizationId], $packageSlugShort, $status)
            ->join('su_integration_reports_types', function ($query) use ($reportTypesFilter)
            {
                if (isset($reportTypesFilter) && count($reportTypesFilter) > 0) {
                    $query->whereIn('su_integration_reports_types.name', $reportTypesFilter);
                }
            })
            ->join('su_integrations_reports', function ($query) {
                $query->on('su_integrations_reports.integration_id', '=', 'su_integration.id');
                $query->on('su_integrations_reports.integration_reports_types_id', '=', 'su_integration_reports_types.id');

            })
            ->select('su_integration.id', 'su_integration.token', 'su_integration.integration_address', 'su_integrations_reports.id as integrations_report_id');

        
        return $query->get();
    }
}
