<?php

namespace App\Models;


use phpseclib\Math\BigInteger;

/**
 * Class IrrigationUnit
 * @package App
 *
 * @property BigInteger $organisation_id
 * @property string $type
 * @property integer $length
 * @property string $name
 * @property integer $wialon_unit_imei
 * @property integer $wialon_unit_id
 */
class IrrigationUnit extends BaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_irrigation_units';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['organization_id', 'type', 'length', 'name', 'wialon_unit_imei', 'wialon_unit_id'];

    public static function getUnits()
    {
        return self::select('id', "organization_id", 'name', 'wialon_unit_imei', 'type', 'length', 'wialon_unit_id');
    }
}
