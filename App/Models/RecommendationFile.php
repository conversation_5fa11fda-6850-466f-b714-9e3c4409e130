<?php

namespace App\Models;

class RecommendationFile extends BaseModel
{       
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_recommendations_files';
    
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;
    
    /**
     * Get the user for the file.
     */
    public function recommendation()
    {
        return $this->belongsTo('App\Models\Recommendation', 'id', 'recommendation_id');
    }
}
