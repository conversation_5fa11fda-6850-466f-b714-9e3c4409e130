<?php

namespace App\Models;

use DB;
use phpseclib\Math\BigInteger;

/**
 * Class WorkOperation
 * @package App
 *
 * @property BigInteger $id
 * @property string $implement_id
 * @property string $name
 */
class WorkOperation extends BaseModel
{
    const APPROVED = 'Approved';
    const PROPOSED = "Proposed";
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_work_operations_types';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['id', 'implement_id', 'name'];

    public static function getNameEnumsData()
    {
        $resultColumns = DB::select("SELECT unnest(enum_range(NULL::work_operations_types_enum))");

        $results = array_map(function ($column) {
            return $column->unnest;
        }, $resultColumns);

        return $results;
    }

    /**
     * @param array $filter
     * @return mixed
     */
    public static function getWorkOperationsFilteredData(array $filter)
    {
        $query = MachineEvent::join('su_machines_implements as smi', 'smi.id', '=', 'su_machine_events.machine_implement_id')
            ->join('su_organizations as so', 'so.id', '=', 'smi.organization_id')
            ->join('su_farms as sf', 'sf.organization_id', '=', 'so.id')
            ->join('su_work_operations_types as swot', 'swot.implement_id', '=', 'smi.id')
            ->where(DB::raw("to_char(su_machine_events.start_date, 'YYYY MM DD')"), '>=', $filter['start_date'])
            ->where(DB::raw("to_char(su_machine_events.end_date, 'YYYY MM DD')"), '<=', $filter['end_date']);

        if (isset($filter['farm_ids'])) {
            $query->whereIn('sf.id', json_decode($filter['farm_ids'],true));
        }

        if (isset($filter['plot_ids'])) {
            $query->whereIn('su_machine_events.plot_id', json_decode($filter['plot_ids'], true));
        }

        return $query;
    }
}
