<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2018 TechnoFarm Ltd.
 */

namespace App\Models;

use DB;

/**
 * Class RequestLog
 * @package App\Models
 *
 * @property string $url
 * @property string $method
 * @property string $ip
 * @property integer $status
 * @property string $request
 * @property double $duration
 * @property integer|null $user_id
 * @property \DateTime created_at
 * @property \DateTime updated_at
 */
class RequestLog extends BaseModel
{
    public $timestamps = true;
    protected $table = 'request_logs';
    protected $connection = 'main';


    public function getUsersLog($url, $method, $request)
    {
        $requestLogsQb = $this->requestLogsQb($url, $method);
        $userLogsQb = DB::connection($this->connection)
            ->table('requests')
            ->withExpression('requests', $requestLogsQb);

        return $this->getLog($userLogsQb, $request);
    }

    public function getStationsL<PERSON>($url, $method, $stationId, $request)
    {
        $requestLogsQb = $this->requestLogsQb($url, $method);
        $stationLogsQuery = $requestLogsQb->where([
            [DB::raw("request_logs_previous.request::jsonb->>'id'"), $stationId],
            [DB::raw("request_logs_current.request::jsonb->>'id'"), $stationId]
        ]);

        $stationLogsQb = DB::connection($this->connection)
            ->table('requests')
            ->withExpression('requests', $stationLogsQuery);

        return $this->getLog($stationLogsQb, $request);
    }

    public function getIntegrationLog($url, $method, $request)
    {
        $requestLogsQb = $this->requestLogsQb($url, $method);
        $userLogsQb = DB::connection($this->connection)
            ->table('requests')
            ->withExpression('requests', $requestLogsQb);
        return $this->getLog($userLogsQb, $request);
    }


    public function requestLogsQb($url, $method)
    {
        $conn = DB::connection($this->connection);

        $qb = $conn->table('request_logs as request_logs_previous')
            ->select(
                'request_logs_previous.created_at',
                'request_logs_current.created_at as updated_at',
                'request_logs_previous.request as previous_request',
                'su_users.name',
                'request_logs_current.request as current_request'
            )
            ->join('su_users', 'su_users.id', 'request_logs_previous.user_id')
            ->crossJoin(DB::raw('
                LATERAL (
                    SELECT request, created_at, status
                    FROM request_logs 
                    WHERE method = ? AND url = ? AND request_logs_previous.created_at < created_at 
                    AND (status BETWEEN 200 AND 300) AND (request_logs_previous.status BETWEEN 200 AND 300)
                    LIMIT 1
                ) AS request_logs_current
            '))
            ->setBindings([$method, $url])
            ->where([
                ['request_logs_previous.method', $method],
                ['request_logs_previous.url', $url]
            ])
            ->orderBy('request_logs_previous.created_at', 'desc');

        return $qb;
    }

    public function getLog($qb, $request)
    {
        $result = $qb->select(
            'requests.updated_at as updatedAt',
            DB::raw('json_agg(prev_request.*) AS previous'),
            DB::raw('json_agg(next_request.*) AS current'),
            'requests.name AS editorName'
        )
            ->from(DB::raw('requests, json_each(previous_request::json) AS prev_request'))
            ->join(DB::raw('json_each(current_request::json) AS next_request'), 'prev_request.key', '=', 'next_request.key', 'full')
            ->whereRaw('prev_request.value::text <> next_request.value::text')
            ->groupBy('updatedAt', 'requests.name')
            ->orderBy('updatedAt', 'DESC')
            ->offset($request->get('page') * $request->get('limit'))
            ->limit($request->get('limit'))->get()->toArray();

        foreach ($result as $row) {
            $row->previous = json_decode($row->previous, true);
            $row->current = json_decode($row->current, true);
        }

        return $result;
    }
}
