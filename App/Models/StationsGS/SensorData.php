<?php

namespace App\Models\StationsGS;

use App\Exceptions\NotImplementedException;
use DB;
use Request;
use Auth;
use Config;

class SensorData extends BaseStationModel implements ISensorData
{
    protected $connection = 'weather_stations';
    protected $table      = 'sens_data';

    public static function getData($stationId, $sensorId, $fromDate, $toDate)
    {
        return self::select([
            DB::RAW("JSON_OBJECT('value', sens_data.value * s.multiplier, 'date', sens_data.ts) data")
        ])
            ->join('sensors as s', 's.id', '=', 'sens_data.sens_id')
            ->where('sens_data.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
            ->where('sens_data.stat_id', $stationId)
            ->where('sens_data.sens_id', $sensorId)
            ->where('sens_data.recieved', '>=', $fromDate)
            ->where('sens_data.recieved', '<=', $toDate);
    }

    public static function getSensorData($stationId, $sensorId, $fromDate, $feed)
    {
        if ($feed == 'raw') {
            return self::getRawSensorData($stationId, $sensorId, $fromDate);
        }
        if ($feed == 'hourly') {
            return self::getHourlySensorData($stationId, $sensorId, $fromDate);
        }
    }

    public static function getSensorsData($stationId, $sensors, $fromDate, $toDate, $feed)
    {
        if ($feed == 'raw') {
            return self::getRawSensorsData($stationId, $sensors, $fromDate, $toDate);
        }
        if ($feed == 'hourly') {
            return self::getHourlySensorsData($stationId, $sensors, $fromDate, $toDate);
        }
        if ($feed == 'daily') {
            return self::getDailySensorsData($stationId, $sensors, $fromDate, $toDate);
        }
    }

    public static function getPrecipitationData($stationId, $sensorId, $fromDate, $feed)
    {
        if ($feed == 'raw') {
            return self::getRawPrecipitationData($stationId, $sensorId, $fromDate);
        }
        if ($feed == 'hourly') {
            return self::getHourlyPrecipitationData($stationId, $sensorId, $fromDate);
        }
    }

    private static function getRawSensorData($stationId, $sensorId, $fromDate)
    {
        $query = self::select(
            'sens_data.stat_id',
            'sens_data.sens_id',
            'sens_data.owner',
            'sens_data.ts as dt',
            'sens_data.value as min_value',
            'sens_data.value as avg_value',
            'sens_data.value as max_value',
            's.multiplier'
        )
            ->join('sensors as s', 's.id', '=', 'sens_data.sens_id')
            ->where('sens_data.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
            ->where('sens_data.stat_id', $stationId)
            ->where('sens_data.sens_id', $sensorId)
            ->where('sens_data.ts', '>=', $fromDate)
            ->orderBy('sens_data.ts', 'ASC');

        return $query;
    }

    private static function getRawPrecipitationData($stationId, $sensorId, $fromDate)
    {
        $subQueryThis = self::subQueryRawPrecipitation('this', $stationId, $sensorId, $fromDate);
        $subQueryPrev = self::subQueryRawPrecipitation('prev', $stationId, $sensorId, $fromDate);

        $subQueryMaxDate = DB::raw('(SELECT
                                        max(t.date)
                                    FROM
                                        (
                                            SELECT
                                            ts AS date
                                        FROM
                                            sens_data
                                        WHERE
                                            owner = ' . Config::get('globals.ON_SITE_STATION_USER_ID') . '
                                        AND stat_id = ' . $stationId . '
                                        AND sens_id = ' . $sensorId . '
                                        AND ts >= ' . $fromDate . '
                                        ) t
                                    WHERE
                                        t.date < this.date)');

        $query = self::select(
            'this.date',
            DB::RAW("IFNULL(ABS(this.max_value - prev.max_value) * s.multiplier, 0) AS precipitation")
        )
            ->from($subQueryThis)
            ->leftJoin($subQueryPrev, 'prev.date', '=', $subQueryMaxDate)
            ->join('sensors as s', 's.id', '=', 'this.sens_id')
            ->where('this.date', '>=', $fromDate)
            ->orderBy('this.date', 'ASC');

        return $query;
    }

    private static function subQueryRawPrecipitation($tableName, $stationId, $sensorId, $fromDate)
    {
        return DB::raw('(SELECT ts AS date, sens_id, value as min_value, value as max_value, value as avg_value FROM sens_data
                            WHERE
                                owner = ' . Config::get('globals.ON_SITE_STATION_USER_ID') . '
                                AND stat_id = ' . $stationId . '
                                AND sens_id = ' . $sensorId . '
                                AND ts >= ' . $fromDate . ') as ' . $tableName . '');
    }

    private static function getRawSensorsData($stationId, $sensors, $fromDate, $toDate)
    {
        $query = self::select(
            DB::RAW('ts*1000 as date'),
            'sens_id',
            DB::RAW('CASE s.id
              WHEN 14 THEN IF(calculated_value < 0, 0, calculated_value) * s.multiplier
              ELSE value * s.multiplier
              END as min_value'),
            DB::RAW('CASE s.id
              WHEN 14 THEN IF(calculated_value < 0, 0, calculated_value) * s.multiplier
              ELSE value * s.multiplier
              END as max_value'),
            DB::RAW('CASE s.id
              WHEN 14 THEN IF(calculated_value < 0, 0, calculated_value) * s.multiplier
              ELSE value * s.multiplier
              END as avg_value'),
            DB::RAW('CASE s.id
         WHEN 14 THEN IF(calculated_value < 0, 0, calculated_value) * s.multiplier
         ELSE value * s.multiplier
         END                  as sum_value')
        )
            ->join('sensors as s', 's.id', '=', 'sens_id')
            ->where('stat_id', $stationId)
            ->where('owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
            ->whereIn('sens_id', explode(',', $sensors))
            ->where('ts', '>=', $fromDate)
            ->whereRaw('value between s.min_value and s.max_value');

        if($toDate){
            $query->where('ts', '<=', $toDate);
        }    

        return $query;
    }

    private static function getHourlySensorData($stationId, $sensorId, $fromDate)
    {
        $query = self::select(
            'sens_data.stat_id',
            'sens_data.sens_id',
            'sens_data.owner',
            'sens_data.ts as dt',
            DB::RAW('min(sens_data.value) as min_value'),
            DB::RAW('avg(sens_data.value) as avg_value'),
            DB::RAW('max(sens_data.value) as max_value'),
            's.multiplier'
        )
            ->join('sensors as s', 's.id', '=', 'sens_data.sens_id')
            ->where('sens_data.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
            ->where('sens_data.stat_id', $stationId)
            ->where('sens_data.sens_id', $sensorId)
            ->where('sens_data.ts', '>=', $fromDate)
            ->groupBy(DB::RAW('date(FROM_UNIXTIME(sens_data.ts))'))->groupBy(DB::RAW('HOUR(FROM_UNIXTIME(sens_data.ts))'))
            ->orderBy('sens_data.ts', 'ASC');

        return $query;
    }

    private static function getHourlyPrecipitationData($stationId, $sensorId, $fromDate)
    {
        $subQueryThis = self::subQueryHourlyPrecipitation('this', $stationId, $sensorId, $fromDate);
        $subQueryPrev = self::subQueryHourlyPrecipitation('prev', $stationId, $sensorId, $fromDate);

        $subQueryMaxDate = DB::raw('(SELECT
                                        max(t.date)
                                    FROM
                                        (
                                            SELECT
                                            ts AS date, sens_id
                                        FROM
                                            sens_data
                                        WHERE
                                            owner = ' . Config::get('globals.ON_SITE_STATION_USER_ID') . '
                                        AND stat_id = ' . $stationId . '
                                        AND sens_id = ' . $sensorId . '
                                        AND ts >= ' . $fromDate . '
                                        GROUP BY date(FROM_UNIXTIME(ts)), HOUR(FROM_UNIXTIME(ts))
                                        ) t
                                    WHERE
                                        t.date < this.date AND t.sens_id = this.sens_id)');

        $query = self::select(
            'this.date',
            DB::RAW("IFNULL(ABS(this.max_value - prev.max_value) * s.multiplier, 0) AS precipitation")
        )
            ->from($subQueryThis)
            ->leftJoin($subQueryPrev, 'prev.date', '=', $subQueryMaxDate)
            ->join('sensors as s', 's.id', '=', 'this.sens_id')
            ->where('this.date', '>=', $fromDate)
            ->orderBy('this.date', 'ASC');

        return $query;
    }

    private static function subQueryHourlyPrecipitation($tableName, $stationId, $sensorId, $fromDate)
    {
        return DB::raw('(SELECT ts AS date, sens_id, min(value) as min_value, max(value) as max_value, avg(value) as avg_value FROM sens_data
                            WHERE
                                owner = ' . Config::get('globals.ON_SITE_STATION_USER_ID') . '
                                AND stat_id = ' . $stationId . '
                                AND sens_id = ' . $sensorId . '
                                AND ts >= ' . $fromDate . '
                                GROUP BY date(FROM_UNIXTIME(sens_data.ts)), HOUR(FROM_UNIXTIME(sens_data.ts))
                                ) as ' . $tableName . ''
        );
    }

    private static function getHourlySensorsData($stationId, $sensors, $fromDate, $toDate)
    {
        $query = self::select(
            DB::RAW('max(ts*1000) as date'),
            'sens_id',
            DB::RAW('CASE s.id
         WHEN 14 THEN min(IF(calculated_value < 0, 0, calculated_value) * s.multiplier)
         ELSE min(value * s.multiplier)
         END as min_value'),
            DB::RAW('CASE s.id
         WHEN 14 THEN max(IF(calculated_value < 0, 0, calculated_value) * s.multiplier)
         ELSE max(value * s.multiplier)
         END                  as max_value'),
            DB::RAW('CASE s.id
         WHEN 14 THEN avg(IF(calculated_value < 0, 0, calculated_value) * s.multiplier)
         ELSE avg(value * s.multiplier)
         END                  as avg_value'),
            DB::RAW('CASE s.id
         WHEN 14 THEN sum(IF(calculated_value < 0, 0, calculated_value) * s.multiplier)
         ELSE sum(value * s.multiplier)
         END                  as sum_value')
        )
            ->join('sensors as s', 's.id', '=', 'sens_id')
            ->where('stat_id', $stationId)
            ->where('owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
            ->whereIn('sens_id', explode(',', $sensors))
            ->where('ts', '>=', $fromDate)
            ->whereRaw('value between s.min_value and s.max_value')
            ->groupBy('sens_id')
            ->groupBy(DB::RAW('date(FROM_UNIXTIME(ts))'))
            ->groupBy(DB::RAW('HOUR(FROM_UNIXTIME(ts))'));

        if($toDate){
            $query->where('ts', '<=', $toDate);
        }        

        return $query;
    }

    private static function getDailySensorsData($stationId, $sensors, $fromDate, $toDate)
    {
        $query = self::select(
            DB::RAW('max(ts*1000) as date'),
            'sens_id',
            DB::RAW('CASE s.id
         WHEN 14 THEN min(IF(calculated_value < 0, 0, calculated_value) * s.multiplier)
         ELSE min(value * s.multiplier)
         END as min_value'),
            DB::RAW('CASE s.id
         WHEN 14 THEN max(IF(calculated_value < 0, 0, calculated_value) * s.multiplier)
         ELSE max(value * s.multiplier)
         END                  as max_value'),
            DB::RAW('CASE s.id
         WHEN 14 THEN avg(IF(calculated_value < 0, 0, calculated_value) * s.multiplier)
         ELSE avg(value * s.multiplier)
         END                  as avg_value'),
            DB::RAW('CASE s.id
         WHEN 14 THEN sum(IF(calculated_value < 0, 0, calculated_value) * s.multiplier)
         ELSE sum(value * s.multiplier)
         END                  as sum_value')
        )
            ->join('sensors as s', 's.id', '=', 'sens_id')
            ->where('stat_id', $stationId)
            ->where('owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
            ->whereIn('sens_id', explode(',', $sensors))
            ->where('ts', '>=', $fromDate)
            ->whereRaw('value between s.min_value and s.max_value')
            ->groupBy('sens_id')
            ->groupBy(DB::RAW('date(FROM_UNIXTIME(ts))'));

        if($toDate){
            $query->where('ts', '<=', $toDate);
        }

        return $query;
    }
}
