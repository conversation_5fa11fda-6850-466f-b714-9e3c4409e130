<?php

namespace App\Models\StationsGS;

use DB;
use Request;
use Auth;
use Config;

class SensorAvgData extends BaseStationModel implements ISensorData
{
    protected $connection = 'weather_stations';
    protected $table      = 'sens_avg';

    public static function getSensorData($stationId, $sensorId, $fromDate, $feed = 'daily')
    {

        return self::select(
            'sens_avg.stat_id',
            'sens_avg.sens_id',
            'sens_avg.owner',
            'sens_avg.dt',
            'sens_avg.min_value',
            'sens_avg.avg_value',
            'sens_avg.max_value',
            's.multiplier'
        )
            ->join('sensors as s', 's.id', '=', 'sens_avg.sens_id')
            ->where('sens_avg.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
            ->where('sens_avg.stat_id', $stationId)
            ->where('sens_avg.sens_id', $sensorId)
            ->where(DB::RAW('UNIX_TIMESTAMP(sens_avg.dt)'), '>=', $fromDate)
            ->orderBy('sens_avg.dt', 'ASC');
    }

    public static function getPrecipitationData($stationId, $sensorId, $fromDate, $feed = 'daily')
    {
        $subQueryThis = self::subQueryPrecipitation('this', $stationId, $sensorId, $fromDate);
        $subQueryPrev = self::subQueryPrecipitation('prev', $stationId, $sensorId, $fromDate);

        $subQueryMaxDate = DB::raw('(SELECT
                                        max(t.date)
                                    FROM
                                        (
                                            SELECT
                                            dt AS date
                                        FROM
                                            sens_avg
                                        WHERE
                                            owner = ' . Config::get('globals.ON_SITE_STATION_USER_ID') . '
                                        AND stat_id = ' . $stationId . '
                                        AND sens_id = ' . $sensorId . '
                                        AND UNIX_TIMESTAMP(dt) >= ' . $fromDate . '
                                        ) t
                                    WHERE
                                        t.date < this.date)');

        return self::select(
            'this.date',
            DB::RAW("IFNULL(ABS(this.max_value - prev.max_value) * s.multiplier, 0) AS precipitation")
        )
            ->from($subQueryThis)
            ->leftJoin($subQueryPrev, 'prev.date', '=', $subQueryMaxDate)
            ->join('sensors as s', 's.id', '=', 'this.sens_id')
            ->where(DB::RAW('UNIX_TIMESTAMP(this.date)'), '>=', $fromDate)
            ->orderBy('this.date', 'ASC');
    }

    private static function subQueryPrecipitation($tableName, $stationId, $sensorId, $fromDate)
    {
        return DB::raw('(SELECT dt AS date, sens_id, min_value, max_value, avg_value FROM sens_avg
                            WHERE
                                owner = ' . Config::get('globals.ON_SITE_STATION_USER_ID') . '
                                AND stat_id = ' . $stationId . '
                                AND sens_id = ' . $sensorId . '
                                AND UNIX_TIMESTAMP(dt) >= ' . $fromDate . ') as ' . $tableName . '');
    }

    public static function getSensorsData($stationId, $sensors, $fromDate, $feed = 'daily')
    {
        $ownerId = Config::get('globals.ON_SITE_STATION_USER_ID');


        return self::select(
            'sens_avg.dt as date',
            'sens_avg.sens_id',
            DB::raw("
            CASE s.id
                WHEN 14 THEN
                  (
                    SELECT
                      IFNULL(ABS(sens_avg.max_value - sa2.max_value) * s.multiplier, 0)
                    FROM sens_avg sa2
                    WHERE owner = '{$ownerId}'
                      AND stat_id = '{$stationId}'
                      and sens_id = sens_avg.sens_id
                      AND sa2.dt < sens_avg.dt order by sa2.dt desc limit 1
                  )
                ELSE
                  sens_avg.min_value * s.multiplier END as min_value,
              CASE s.id
                WHEN 14 THEN
                  (
                    SELECT
                      IFNULL(ABS(sens_avg.max_value - sa2.max_value) * s.multiplier, 0)
                    FROM sens_avg sa2 USE index (sens_avg_stat_id_sens_id_owner_dt_index)
                    WHERE owner = '{$ownerId}'
                      AND stat_id = '{$stationId}'
                      and sens_id = sens_avg.sens_id
                      AND sa2.dt < sens_avg.dt order by sa2.dt desc limit 1
                  )
                ELSE
                  sens_avg.max_value * s.multiplier END as max_value,
              CASE s.id
                WHEN 14 THEN
                  (
                    SELECT
                      IFNULL(ABS(sens_avg.max_value - sa2.max_value) * s.multiplier, 0)
                    FROM sens_avg sa2 USE index (sens_avg_stat_id_sens_id_owner_dt_index)
                    WHERE owner = '{$ownerId}'
                      AND stat_id = '{$stationId}'
                      and sens_id = sens_avg.sens_id
                      AND sa2.dt < sens_avg.dt order by sa2.dt desc limit 1
                  )
                ELSE
                  sens_avg.avg_value * s.multiplier END as avg_value
                  "))
            ->from('sens_avg')
            ->join('sensors as s', 'sens_avg.sens_id', '=', 's.id')
            ->where('sens_avg.owner', '=', $ownerId)
            ->where('sens_avg.stat_id', '=', $stationId)
            ->whereIn('sens_avg.sens_id', explode(',', $sensors))
            ->whereRaw("UNIX_TIMESTAMP(sens_avg.dt) >= {$fromDate}")
            ->orderBy('sens_avg.dt', 'asc')
        ;

    }
}
