<?php

namespace App\Models\StationsGS;

use DB;
use Request;
use Auth;
use Config;

class Station extends BaseStationModel
{
    protected $table = 'stations';
    protected $connection = 'weather_stations';

    public static function getStationsAndSensors()
    {
        return self::select([
            'stations.id',
            'stations.name',
            'm.descr',
            DB::RAW("CONCAT('[', GROUP_CONCAT(CONCAT(JSON_OBJECT('id', s.id, 'name', s.name, 'label', s.label))), ']') sensors"),
            DB::RAW("JSON_OBJECT('lat', stations.lat_gps, 'lon', stations.lon_gps) coords")
        ])
        ->join('sens2mod as s2m', 's2m.mod_id', '=', 'stations.model')
        ->join('models as m', 'm.id', '=', 'stations.model')
        ->join('sensors as s', 's.id', '=', 's2m.sens_id')
        ->where('stations.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
        ->groupBy('stations.id', 'stations.name', 'm.descr', 'stations.lat_gps', 'stations.lon_gps');
    }

    public static function getSensorsByStation($stationId, $sensorId = 0, $fromDate, $toDate = 0)
    {
        $query = self::select([
            'avg.sens_id',
            's.name',
            's.label',
            's.descr',
            's.icon',
            DB::RAW("CONCAT('[', GROUP_CONCAT(CONCAT(JSON_OBJECT('date', avg.dt, 'min', avg.min_value * s.multiplier, 'max', avg.max_value * s.multiplier))), ']') averages"),
            DB::RAW("JSON_OBJECT('value', last.value * s.multiplier, 'date', DATE_FORMAT(FROM_UNIXTIME(last.ts), '%Y-%m-%d')) last")
        ])
        ->join('sens_last as last', 'last.stat_id', '=', 'stations.id')
        ->join('sens_avg as avg', 'last.sens_id', '=', 'avg.sens_id')
        ->join('sensors as s', 's.id', '=', 'avg.sens_id')
        ->where('stations.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
        ->where('last.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
        ->where('avg.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
        ->where('stations.id', $stationId)
        ->where('last.stat_id', $stationId)
        ->where('avg.stat_id', $stationId)
        ->where('last.ts','>=', $fromDate)
        ->where( DB::RAW('UNIX_TIMESTAMP(avg.dt)'),'>=', $fromDate);

        if ($sensorId != 0) {
            $query->where('avg.sens_id', $sensorId)
                ->where('last.sens_id', $sensorId);
        }

        if ($toDate != 0) {
            $query->where('last.ts','<=', $toDate)
                ->where( DB::RAW('UNIX_TIMESTAMP(avg.dt)'),'<=', $toDate);
        } else {
            $query->where('last.ts','<=', time())
                ->where( DB::RAW('UNIX_TIMESTAMP(avg.dt)'),'<=', time());
        }

        $query->groupBy('avg.stat_id', 'avg.sens_id', 's.name','s.label', 's.descr', 's.icon', 'last.value', 'last.ts', 's.multiplier');

        return $query;
    }

    public static function getInformation($stationSid)
    {
        $query = self::select([
            'stations.sid',
            'stations.name',
            'stations.owner',
            'stations.lat_gps',
            'stations.lon_gps',
            'stations.reg_ts',
            'last.ts as last_communication'
        ])
        ->join('sens_last as last', 'last.stat_id', '=', 'stations.id')
        ->where('stations.owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
        ->where('stations.sid', $stationSid)
        ->orderBy('last.ts', 'DESC');
        
        return $query;

    }

    public static function getStationsByLastCommunication($hours){
        $seconds =  $hours * 3600;
        $query = self::selectRaw("
            CAST(CONVERT(stations.name USING latin1) AS BINARY) as name,
            stations.sid as sid,
            MAX(FROM_UNIXTIME(last.ts)) as last_communication
        ")
        ->join('sens_last as last', 'last.stat_id', '=', 'stations.id')
        ->where('last.ts', '>', $seconds)
        ->groupBy('sid');

        return $query;
    }

    public static function stationId($stationSid)
    {
        
        return self::select(
            'id'
            )
            ->where('owner', Config::get('globals.ON_SITE_STATION_USER_ID'))
            ->where('sid', $stationSid);
    } 
}
