<?php

namespace App\Models;


use phpseclib\Math\BigInteger;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class IrrigationEventPlot
 * @package App
 *
 * @property BigInteger $event_id
 * @property integer $crop_id
 * @property integer $plot_id
 * @property float $event_area
 * @property string $geom
 * @property string $thumbnail (xml)
 */
class IrrigationEventPlot extends BaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'su_irrigation_events_plots';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var BigInteger
     */
    protected $primaryKey = 'id';

    /** @var array */
    protected $fillable = ['event_id', 'crop_id', 'plot_id', 'event_area', 'geom', 'thumbnail'];

    public function irrigationEvent()
    {
        return $this->hasMany('App\Models\IrrigationEvent', 'event_id', 'id');
    }

    public static function insertIrrigationEventPlot(Builder $eventsPlotsQuery): void
    {
        $querySql = $eventsPlotsQuery->toSqlWithBindings();
        $querySqlDoNothing = "({$querySql}) ON CONFLICT DO NOTHING";

        self::insertUsing([
            'event_id',
            'crop_id',
            'plot_id',
            'event_area',
            'geom',
            'thumbnail'
        ], $querySqlDoNothing);
    }
}
