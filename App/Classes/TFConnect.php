<?php

namespace App\Classes;

use GuzzleHttp\Client;
use GuzzleHttp\Psr7;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\NotFoundException;


class TFConnect
{
	/**
	 * Constructor 
	 * 
	 * @param {String} url TFConnect base url.
	 * @param {String} username TFConnect platform user's name.
	 * @param {String} password TFConnect platform user's password.
	 */
	public function __construct($url, $username, $password)
    {
    	$this->url = $url;
    	$this->username = $username;
    	$this->password = $password;

        $this->client = new Client([
            'base_uri' => $url,
            'timeout' => 2.0,
        ]);
    }

    /**
	 * Uploads file to TFconnect api
	 * @param serialNumber The device's serial
	 * @param filePath Path to the file that will be uploaded
	 */
	public function uploadFile($serialNumber, $filePath) {
		$token = $this->getToken();
		$ext = "." . pathinfo($filePath, PATHINFO_EXTENSION);
		$baseFileName = basename($filePath, $ext);
		$filename = $baseFileName . "_" . mt_rand() . $ext;

		$response = $this->client->request('POST', '/api/1.0/file/upload', [
			'multipart' => [
				[
					'name'     => 'file',
					'contents' => fopen($filePath, "r"),
					'filename' => $filename,
				],
				[
					'name' => 'device_serial',
					'contents' => $serialNumber,
				],
			],
			'headers' => ['Authorization' => 'Bearer ' . $token]
		]);

		return json_decode($response->getBody()->getContents(), true);
	}

    /**
     * Sends request for token.
     * @return token
     */
    private function getToken(){
		$response = $this->client->request('POST', '/api/1.0/token', [
			'form_params' => [
				'name' => $this->username,
            	'password' => $this->password,
			]
		]);
		$contentJson = $response->getBody()->getContents();
		$content = json_decode($contentJson, true);

		return $content['token'];
	}


	public function setStatus($deviceSerial, $fileId, $status)
	{
		$token = $this->getToken();
		$response = $this->client->request('POST', "/api/1.0/file/{$fileId}/status", [
			'form_params' => [
				'deviceSerial' => $deviceSerial,
            	'status' => $status,
			],
			'headers' => ['Authorization' => 'Bearer ' . $token]
		]);
		return json_decode($response->getBody()->getContents(), true);
	}


	public function getDevices()
	{
		$token = $this->getToken();
		$response =  $this->client->request('GET', "/api/1.0/user/devices", [
			'headers' => ['Authorization' => 'Bearer ' . $token]
		]);
		return json_decode($response->getBody()->getContents(), true);
	}

	public function getFilesLogByDeviceId($deviceId){
		$token = $this->getToken();
		$response =  $this->client->request('GET', "/api/1.0/device/files/all", [
			'headers' => ['Authorization' => 'Bearer ' . $token],
			'query' => ['deviceId' => $deviceId]
		]);
		return json_decode($response->getBody()->getContents(), true);
	}

	public function downloadFile($fileId){
		$token = $this->getToken();
		$response =  $this->client->request('POST', "/api/1.0/file/download", [
			'headers' => ['Authorization' => 'Bearer ' . $token],
			'form_params' => ['fileId' => $fileId]
		]);
		$content = $response->getBody()->getContents();

		$parsedContentDispositionHeader = Psr7\parse_header($response->getHeader('Content-Disposition'));
		if(empty($parsedContentDispositionHeader)){
			throw new NotFoundException("File not found!");
		}
		$fileName = $parsedContentDispositionHeader[0]["filename"];
		$storeFilePath = "tfconnect_tmp/{$fileName}";

		Storage::put($storeFilePath, $content);

		return $storeFilePath;
	}
}