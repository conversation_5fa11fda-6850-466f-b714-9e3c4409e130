<?php

namespace App\Classes\AdaptConverter\TFPlugin\FieldInfo;

use App\Classes\AdaptConverter\TFPlugin\Base\AbstractBaseModel;
use App\Classes\AdaptConverter\TFPlugin\FieldInfo\Grower;
use App\Classes\AdaptConverter\TFPlugin\FieldInfo\Farm;

class Field extends AbstractBaseModel
{
    private $farm;
    private $grower;
    private $fieldBoundaryId;
    private $guidanceGroupsIds = [];
    private $code = "vrReportedFieldArea";
    private $description = "Area of the Field.  This value is supplied by the owner or producer of the field.";
    private $value = 0.0;

    public function __construct(
        int $id,
        string $name
    ) {
        parent::__construct($id, $name);
    }

    public function getGrower(): Grower
    {
        return $this->grower;
    }

    public function setGrower(Grower $grower): void
    {
        $this->grower = $grower;
    }

    public function getFarm(): Farm
    {
        return $this->farm;
    }

    public function setFarm(Farm $farm): void
    {
        $this->farm = $farm;
    }

    public function getFieldBoundaryId(): int
    {
        return $this->fieldBoundaryId;
    }

    public function setFieldBoundaryId(int $fieldBoundaryId): void
    {
        $this->fieldBoundaryId = $fieldBoundaryId;
    }

    public function getGuidanceGroupsIds(): array
    {
        return $this->guidanceGroupsIds;
    }

    public function setGuidanceGroupsIds(array $guidanceGroupsIds): void
    {
        $this->guidanceGroupsIds = $guidanceGroupsIds;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): void
    {
        $this->code = $code;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): void
    {
        $this->value = $value;
    }

    public function jsonSerialize(): array
    {
        $arr = parent::jsonSerialize();

        $arr['Farm'] = $this->farm;
        $arr['Grower'] = $this->grower;
        $arr['FieldBoundaryId'] = $this->fieldBoundaryId;
        $arr['GuidanceGroupsIds'] = $this->guidanceGroupsIds;
        $arr['Code'] = $this->code;
        $arr['Description'] = $this->description;
        $arr['Value'] = $this->value;

        return $arr;
    }
}
