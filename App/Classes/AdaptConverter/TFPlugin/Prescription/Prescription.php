<?php

namespace App\Classes\AdaptConverter\TFPlugin\Prescription;

use App\Classes\AdaptConverter\TFPlugin\Base\AbstractBaseModel;
use App\Classes\AdaptConverter\TFPlugin\Prescription\BoundingBox;
use App\Classes\AdaptConverter\TFPlugin\Prescription\Origin;
use App\Classes\AdaptConverter\TFPlugin\Prescription\OutOfFieldRate;
use App\Classes\AdaptConverter\TFPlugin\Prescription\RxProductLookup;

class Prescription extends AbstractBaseModel
{
    private $fieldId;
    private $boundingBox;
    private $origin;
    private $outOfFieldRate;
    private $cellHeight;
    private $cellWidth;
    private $columnCount;
    private $rowCount;
    private $rxProductLookups = [];
    private $rates = [];
    private $productIds = [];

    public function __construct(
        int $id,
        string $name
    ) {
        parent::__construct($id, $name);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getFieldId(): int
    {
        return $this->fieldId;
    }

    public function setFieldId(int $fieldId): void
    {
        $this->fieldId = $fieldId;
    }

    public function getBoundingBox(): BoundingBox
    {
        return $this->boundingBox;
    }

    public function setBoundingBox(BoundingBox $boundingBox): void
    {
        $this->boundingBox = $boundingBox;
    }

    public function getOrigin(): Origin
    {
        return $this->origin;
    }

    public function setOrigin(Origin $origin): void
    {
        $this->origin = $origin;
    }

    public function getOutOfFieldRate(): OutOfFieldRate
    {
        return $this->outOfFieldRate;
    }

    public function setOutOfFieldRate(OutOfFieldRate $outOfFieldRate): void
    {
        $this->outOfFieldRate = $outOfFieldRate;
    }

    public function getCellSize(): array
    {
        return [
            'height' => $this->cellHeight,
            'width' => $this->cellWidth
        ];
    }

    public function setCellSize(int $height, int $width): void
    {
        $this->cellHeight = $height;
        $this->cellWidth = $width;
    }

    public function getPointsGridSize(): array
    {
        return [
            'columnCount' => $this->columnCount,
            'rowCount' => $this->rowCount
        ];
    }

    public function setPointsGridSize(int $columnCount, int $rowCount): void
    {
        $this->columnCount = $columnCount;
        $this->rowCount = $rowCount;
    }

    public function getRxProductLookups(): array
    {
        return $this->rxProductLookups;
    }

    public function setRxProductLookups(array $rxProductLookups): void
    {
        $this->rxProductLookups = $rxProductLookups;
    }

    public function addRxProductLookup(RxProductLookup $rxProductLookup): void
    {
        $this->rxProductLookups[] = $rxProductLookup;
    }

    public function removeRxProductLookup(int $index): void
    {
        if (isset($this->rxProductLookups[$index])) {
            unset($this->rxProductLookups[$index]);
        }
    }

    public function getRates(): array
    {
        return $this->rates;
    }

    public function setRates(array $rates): void
    {
        $this->rates = $rates;
    }

    public function getProductIds(): array
    {
        return $this->productIds;
    }

    public function setProductIds(array $productIds): void
    {
        $this->productIds = $productIds;
    }

    public function addProductId(int $productId): void
    {
        $this->productIds[] = $productId;
    }

    public function removeProductId(int $index): void
    {
        if (isset($this->productIds[$index])) {
            unset($this->productIds[$index]);
        }
    }

    public function jsonSerialize(): array
    {
        $arr = parent::jsonSerialize();

        $arr['FieldId'] = $this->fieldId;
        $arr['BoundingBox'] = $this->boundingBox;
        $arr['Origin'] = $this->origin;
        $arr['OutOfFieldRate'] = $this->outOfFieldRate;
        $arr['CellHeight'] = $this->cellHeight;
        $arr['CellWidth'] = $this->cellWidth;
        $arr['ColumnCount'] = $this->columnCount;
        $arr['RowCount'] = $this->rowCount;
        $arr['RxProductLookups'] = $this->rxProductLookups;
        $arr['Rates'] = $this->rates;
        $arr['ProductIds'] = $this->productIds;

        return $arr;
    }
}
