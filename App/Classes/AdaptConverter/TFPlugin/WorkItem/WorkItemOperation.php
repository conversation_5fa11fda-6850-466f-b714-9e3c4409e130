<?php

namespace App\Classes\AdaptConverter\TFPlugin\WorkItem;

use JsonSerializable;

class WorkItemOperation implements JsonSerializable
{
    private $id;
    private $prescriptionId;
    private $description;

    public function __construct(int $id)
    {
        $this->id = $id;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getPrescriptionId(): int
    {
        return $this->prescriptionId;
    }

    public function setPrescriptionId(int $prescriptionId): void
    {
        $this->prescriptionId = $prescriptionId;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }


    public function jsonSerialize(): array
    {
        return [
            "Id" => $this->id,
            "Description" => $this->description,
            "PrescriptionId" => $this->prescriptionId
        ];
    }
}
