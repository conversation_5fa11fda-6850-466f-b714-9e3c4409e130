<?php

namespace App\Classes\AdaptConverter\TFPlugin;

use JsonSerializable;

class TFPlugin implements JsonSerializable
{
    private $fields = [];
    private $fieldBoundaries = [];
    private $guidance = [];
    private $prescriptions = [];
    private $loggedData = [];
    private $products = [];
    private $cropZones = [];
    private $workItems = [];
    private $referenceLayers = [];

    public function __construct()
    {
    }

    public function getFields(): array
    {
        return $this->fields;
    }

    public function setFields(array $fields): void
    {
        $this->fields = $fields;
    }

    public function getFieldBoundaries(): array
    {
        return $this->fieldBoundaries;
    }

    public function setFieldBoundaries(array $fieldBoundaries): void
    {
        $this->fieldBoundaries = $fieldBoundaries;
    }

    public function getGuidance(): array
    {
        return $this->guidance;
    }

    public function setGuidance(array $guidance): void
    {
        $this->guidance = $guidance;
    }

    public function getPrescriptions(): array
    {
        return $this->prescriptions;
    }

    public function setPrescriptions(array $prescriptions): void
    {
        $this->prescriptions = $prescriptions;
    }

    public function getLoggedData(): array
    {
        return $this->loggedData;
    }

    public function setLoggedData(array $loggedData): void
    {
        $this->loggedData = $loggedData;
    }

    public function getProducts(): array
    {
        return $this->products;
    }

    public function setProducts(array $products): void
    {
        $this->products = $products;
    }

    public function getCropZones(): array
    {
        return $this->cropZones;
    }

    public function setCropZones(array $cropZones): void
    {
        $this->cropZones = $cropZones;
    }

    public function getWorkItems(): array
    {
        return $this->workItems;
    }

    public function setWorkItems(array $workItems): void
    {
        $this->workItems = $workItems;
    }

    public function getReferenceLayers(): array
    {
        return $this->referenceLayers;
    }

    public function setReferenceLayers(array $referenceLayers): void
    {
        $this->referenceLayers = $referenceLayers;
    }


    public function jsonSerialize(): array
    {
        return [
            'Fields' => $this->fields,
            'FieldBoundaries' => $this->fieldBoundaries,
            'Guidance' => $this->guidance,
            'Prescriptions' => $this->prescriptions,
            'LoggedData' => $this->loggedData,
            'Products' => $this->products,
            'CropZones' => $this->cropZones,
            'WorkItems' => $this->workItems,
            'ReferenceLayers' => $this->referenceLayers
        ];
    }
}
