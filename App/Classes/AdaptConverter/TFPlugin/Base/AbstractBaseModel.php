<?php

namespace App\Classes\AdaptConverter\TFPlugin\Base;

use JsonSerializable;

abstract class AbstractBaseModel implements JsonSerializable
{
    protected $id;
    protected $name;

    public function __construct(int $id, string $name)
    {
        $this->id = $id;
        $this->name = $name;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function jsonSerialize(): array
    {
        return [
            'Id' => $this->id,
            'Name' => $this->name
        ];
    }
}
