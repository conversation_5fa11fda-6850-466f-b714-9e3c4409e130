<?php

namespace App\Classes\AdaptConverter\TFPlugin\Crop;

use App\Classes\AdaptConverter\TFPlugin\Base\AbstractBaseModel;
use App\Classes\AdaptConverter\TFPlugin\Crop\CropZone;

class Crop extends AbstractBaseModel
{
    private $cropZone;

    public function __construct(int $id, string $name)
    {
        parent::__construct($id, $name);
    }

    public function getCropZone(): CropZone
    {
        return $this->cropZone;
    }

    public function setCropZone(CropZone $cropZone): void
    {
        $this->cropZone = $cropZone;
    }

    public function jsonSerialize(): array
    {
        $arr = parent::jsonSerialize();
        $arr['CropZone'] = $this->cropZone;

        return $arr;
    }
}
