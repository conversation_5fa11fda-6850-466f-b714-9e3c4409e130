<?php
namespace App\Classes\Meteo;

use App\Models\UserStation;

interface IStation {
	function getHistoryData($cacheMinutes, $callback);
	function getHistoryDataFrom($cacheMinutes, $period, $feed, $callback);
	function getHistoryDataEChartsFormatted($cacheMinutes, $stationInfo, $from, $to, $feed, $sensors, $callback);
	function getCurrentTemperature($cacheMinutes);
	function getCurrentWindSpeed($cacheMinutes);
	function getStationData();
	function getCurrentSensorValues($cacheMinutes, $callback);
	function getDailySensorValues(); // This might be developed further in future.
    function getDailyDataByStationAndDate(\DateTime $date, $sensor);
    function getStationReport($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp = false);
    function getStationReportEChart($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp = false);
}

