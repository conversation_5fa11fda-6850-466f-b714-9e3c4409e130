<?php
namespace App\Classes\Meteo;

use GuzzleHttp\Client;
use GuzzleHttp\Psr7;
use GuzzleHttp\Exception\RequestException;
use Cache;
use Request;
use Response;
use Config;
use App\Helpers\Helper;
use App\Classes\Heap;

/**
* A class for Meteo Cache.
*/
class MeteoCache
{
    private $configMeteoBlue;
    private $baseUriImg;
    private $defaultQueryMeteoBlue = [];
    private $clientMeteoBlue;
    private $clientMeteoBlueImg = null;
    
    private $heap;

    /**
     * @param string MeteoCache MeteoBlue config.
     */
    public function __construct($configMeteoBlue, Heap $heap)
    {
        $this->configMeteoBlue = $configMeteoBlue;

        $this->baseUriImg = $configMeteoBlue['METEO_URI_IMG'];

        $this->defaultQueryMeteoBlue = array(
            'apikey' => $configMeteoBlue['METEO_APIKEY'],
            'tz' => $configMeteoBlue['METEO_TZ'],
            'temperature' => 'C',
            'windspeed' => 'ms-1',
            'winddirection' => 'degree',
            'precipitationamount' => 'mm',
            'timeformat' => 'iso8601',
            'format' => 'json',
            'lang' => $configMeteoBlue['METEO_LANG'],
        );

        $this->clientMeteoBlue = new Client(['base_uri' => $configMeteoBlue['METEO_BASE_URI']]);
        $this->clientMeteoBlueImg = new Client(['base_uri' => $this->baseUriImg]);

        $this->heap = $heap;
    }

    /**
     * Get Meteo Blue From Cache
     * @param  arrQuery
     * @param  feedТype
     * @param  cacheMinutes
     * @param  callback
     */
    public function getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes, $callback)
    {
        $key = $this->configMeteoBlue['METEO_BASE_URI'] . $feedТype . "?" . http_build_query($arrQuery);

        $arrQuery = array_merge($this->defaultQueryMeteoBlue, $arrQuery);
        
        //Retrieve or Store from the Cache
        return Cache::remember($key, $cacheMinutes, function () use ($arrQuery, $feedТype, $callback) {
            try {
                $response = $this->clientMeteoBlue->request('GET', $feedТype, [
                    'query' => $arrQuery
                ]);
                $content = json_decode($response->getBody()->getContents(), true);

                return $callback($content);
            } catch (\Exception $e) {
                return $callback([]);
            }
        });
    }

    /**
     * Get Meteo Blue From Cache
     * @param  arrQuery
     * @param  feedТype
     */
    public function requestMeteoBlueImg($arrQuery, $feedТype, $gid, $lang, $userId, $zoneGid)
    {
        $arrQuery = array_merge($this->defaultQueryMeteoBlue, $arrQuery);

        //File check
        $fileName = $gid . '_' . $lang . '_' . $feedТype . '.png';
        $filePath = config('meteo.METEO_PATH') . config('globals.MACHINE') . DIRECTORY_SEPARATOR . $userId . DIRECTORY_SEPARATOR . $fileName;

        if (file_exists($filePath)) {
            return array('fileName' => $fileName);
        }

        $this->heap->track($feedТype, [
            'Language' => $lang,
            'Zone GID' => $zoneGid
        ]);
        
        //Make request to MeteoBlue API - controlled by cron image deleting
        $response = $this->clientMeteoBlueImg->request('GET', $feedТype, [
            'query' => $arrQuery
        ]);
        $contentImg = $response->getBody()->getContents();

        @mkdir(dirname($filePath), 0777, true);

        if (!file_put_contents($filePath, $contentImg)) {
            return Helper::errorResponseMeteoBlue('No file saved.');
        }

        return array('fileName' => $fileName);
    }    
}
