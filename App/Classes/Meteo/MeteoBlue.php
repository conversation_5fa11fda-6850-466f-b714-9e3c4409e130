<?php

namespace App\Classes\Meteo;


use App\Models\Organization;
use DB;
use Auth;
use Config;
use Response;
use App\Models\Plot;
use App\Models\Order;
use App\Helpers\Helper;
use App\Helpers\GDDHelper;
use App\Models\UserStation;
use App\Classes\Heap;
use Carbon\Carbon;
use App\Classes\Echarts\Meteo\EChartsForecastDataFormatter;

/**
 * A class for Meteo Cache.
 */
class MeteoBlue
{
    private $meteoCache;
    private $heap;

    /**
     * @param string MeteoCache MeteoBlue config.
     */
    public function __construct(MeteoCache $meteoCache, Heap $heap)
    {
        $this->meteoCache = $meteoCache;
        $this->heap = $heap;
    }

    /**
     * getCurrentMeteo Get Current Meteo
     * @param  [int] $gid
     * @return array
     */
    public function getCurrentMeteo($gid)
    {
        $result = $this->currentMeteoCommon($gid);

        return isset($result['data_current']) ? $result['data_current'] : [];
    }

    /**
     * currentMeteoCommon Common method for Current Meteo
     * @param  [type] $gid [description]
     * @return [type]      [description]
     */
    public function currentMeteoCommon($gid)
    {
        //Get Location of the Meteo point by gid and user_id
        $result = Plot::findMeteoLocation($gid, Auth::user()->lastChosenOrganization);

        if (!$result['longitude'] || !$result['latitude']) {
            return [];
        }

        $arrQuery = array(
            'lat' => $result['latitude'],
            'lon' => $result['longitude'],
        );

        $feedТype = 'current';

        $cacheMinutes = config('meteo.METEO_CACHE_MINUTES_CURRENT');

        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes,
            function ($content) {

                return $content;
            });

        $plot = Plot::find($gid);

        //Get Temperature from Pessl or OnSite Meteo Station
        if ($plot->station_id && isset($arrContent['data_current']['temperature'])) {

            $station = $plot->station;

            if (!$station) {
                return;
            }

            $stationObject = UserStation::where('id', $station->id)->first()->getStationApi();

            $currentTemperature = $stationObject->getCurrentTemperature($cacheMinutes);

            if ($currentTemperature) {
                $arrContent['data_current']['temperature'] = $currentTemperature;
            }
        }

        return $arrContent;
    }

    /**
     * currentMeteoByCoordinates method for Current Meteo
     * @param  [type] $longitude [description]
     * @param  [type] $latitude [description]
     * @return [type] $station_id     [description]
     */
    public function currentMeteoByCoordinates($longitude, $latitude, $station_id = null)
    {
        if (!$longitude || !$latitude) {
            return [];
        }

        $arrQuery = array(
            'lat' => $latitude,
            'lon' => $longitude,
        );

        $feedТype = 'current';

        $cacheMinutes = config('meteo.METEO_CACHE_MINUTES_CURRENT');

        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes,
            function ($content) {

                return $content;
            });

        //Get Temperature from Pessl or OnSite Meteo Station
        if ($station_id) {
            $station = UserStation::find($station_id);

            if (!$station) {
                return;
            }

            /** @var IStation $stationObject */
            $stationObject = UserStation::where('id', $station->id)->first()->getStationApi();

            $temperatureFromStation = $stationObject->getCurrentTemperature($cacheMinutes);
            $windSpeedFromStation = $stationObject->getCurrentWindSpeed($cacheMinutes);

            if (is_numeric($temperatureFromStation)) {
                $arrContent['data_current']['temperature'] = $temperatureFromStation;
            }
            if (is_numeric($windSpeedFromStation)) {
                $arrContent['data_current']['windspeed'] = $windSpeedFromStation;
            }
        }

        if (!count($arrContent)) {
            return Helper::errorResponseMeteoBlue('longitude and latitude not found: ' . $longitude . ' ' . $latitude);
        }

        $temp = round($arrContent['data_current']['temperature']);

        if ($temp == -0) {
            $temp = abs($temp);
        }

        $arrResult = array(
            'temperature' => $temp,
            'windspeed' => round($arrContent['data_current']['windspeed'] ?: 0, 2),
            'pictocode' => $arrContent['data_current']['pictocode'],
        );

        return $arrResult;
    }

    /**
     * Get Agro Spraying Forecast Img
     * @param int $gid
     * @param string $lang
     * @return array
     */
    public function agroSprayingForecastImg($gid, $lang)
    {
        //Get Location of the Meteo point by gid and user_id
        $result = Plot::findMeteoLocation($gid, Auth::user()->lastChosenOrganization);

        if (!$result['longitude'] || !$result['latitude']) {
            return 'error';
        }

        $userId = Auth::user()->group_id;
        $zoneGid = $result['zone_gid'];

        $arrQuery = array(
            'lat' => $result['latitude'],
            'lon' => $result['longitude'],
            'lang' => $lang
        );

        $feedТype = 'meteogram_agroSpraying';

        return $this->meteoCache->requestMeteoBlueImg(
            $arrQuery,
            $feedТype,
            $gid,
            $lang,
            $userId,
            $zoneGid
        );
    }

    /**
     * Get Agro Forecast Img - meteogram_agro
     * @param int $gid
     * @param string $lang
     * @param object $heap
     * @return array
     */
    public function agroForecastImg($gid, $lang)
    {
        //Get Location of the Meteo point by gid and user_id
        $result = Plot::findMeteoLocation($gid, Auth::user()->lastChosenOrganization);

        if (!$result['longitude'] || !$result['latitude']) {
            return 'error';
        }

        $userId = Auth::user()->group_id;
        $zoneGid = $result['zone_gid'];

        $arrQuery = array(
            'lat' => $result['latitude'],
            'lon' => $result['longitude'],
            'lang' => $lang
        );

        $feedТype = 'meteogram_agro';

        return $this->meteoCache->requestMeteoBlueImg(
            $arrQuery,
            $feedТype,
            $gid,
            $lang,
            $userId,
            $zoneGid
        );
    }

    /**
     * Get Forecast By Day
     * @param int $gid
     * @param object $heap
     * @return array
     */
    public function forecastByDay($gid)
    {
        //Get Location of the Meteo point by gid and user_id
        $result = Plot::findMeteoLocation($gid, Auth::user()->lastChosenOrganization);

        if (!$result['longitude'] || !$result['latitude']) {
            return Helper::errorResponseMeteoBlue('longitude and latitude not found');
        }

        $arrQuery = array(
            'lat' => $result['latitude'],
            'lon' => $result['longitude'],
        );

        $feedТype = 'basic-day';

        $cacheMinutes = $this->calcCacheMinutes();
        $heap = $this->heap;

        //Retrieve or Store from the Cache
        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes,
            function ($content) use ($heap, $result) {

                $heap->track('Basic day', [
                    'Zone GID' => $result['zone_gid'],
                    'Type' => 'Forecast'
                ]);
                return $this->mapDataForecast($content, 'data_day');
            });

        return $arrContent;
    }

    /**
     * Get Forecast By Hour
     * @param float $latitude
     * @param float $longitude
     * @param string $date
     * @param array $heapOptions
     * 
     * @return array
     */
    public function forecastByHour(float $latitude, float $longitude, array $dates = [], array $heapOptions = [], int $resolution = 1, int $forecastDays = 7, bool $includeNextDayMidnight = false)
    {
        $arrQuery = [
            'lat' => $latitude,
            'lon' => $longitude,
            'forecast_days' => $forecastDays
        ];

        $feedТype = "basic-{$resolution}h";

        $cacheMinutes = $this->calcCacheMinutes();
        $heap = $this->heap;

        //Retrieve or Store from the Cache
        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes,
            function ($content) use ($heap, $heapOptions, $resolution) {

                $heap->track("Basic {$resolution} hour/s", $heapOptions);

                return $this->mapDataForecast($content, "data_{$resolution}h");
            });

        //this is out of cache
        return $this->filterForecast($arrContent, $dates, $includeNextDayMidnight);
    }

    public function mapDataForecastForEchart(array $forecastData, int $resolution = 1) 
    {
        $temperatureDataMap = [];
        $pictocodeDataMap = [];
        $precipitationDataMap = [];
        $precipitationProbabilityDataMap = [];
        $windspeedDataMap = [];
        $sprayingDataMap = [
            "highRisk" => [],
            "mediumRisk" => [],
            "lowRisk" => [],
        ];

        $temperatureKey = ($resolution <= 3 ? "temperature" : "temperature_mean");
        $windspeedKey = ($resolution <= 3 ? "windspeed" : "windspeed_mean");


        foreach ($forecastData as $forecast) {
            $timestamp = strtotime($forecast["time"]) * 1000; // milliseconds

            $temperatureDataMap[] = [$timestamp, $forecast[$temperatureKey]];
            $pictocodeDataMap[] = [$timestamp, $forecast['pictocode'], $forecast[$temperatureKey]];
            $precipitationDataMap[] = [$timestamp, $forecast['precipitation']];
            $precipitationProbabilityDataMap[] = [$timestamp, $forecast['precipitation_probability']];

            $windspeedDataMap[] = [
                $timestamp,
                $forecast[$windspeedKey]
            ];

            $sprayingRisk = $resolution <= 3 ? $this->calcSprayingHour($forecast): $this->calcSprayingDay($forecast);

            $sprayingDataMap["highRisk"][] = [$timestamp, $sprayingRisk['risk'] === 'High Risk' ? 20 : null];
            $sprayingDataMap["mediumRisk"][] = [$timestamp, $sprayingRisk['risk'] === 'Medium Risk' ? 20 : null];
            $sprayingDataMap["lowRisk"][] = [$timestamp, $sprayingRisk['risk'] === 'Low Risk' ? 20 : null];
        }

        return [
            "temperature" => $temperatureDataMap,
            "pictocode" => $pictocodeDataMap,
            "precipitation" => $precipitationDataMap,
            "precipitationProbability" => $precipitationProbabilityDataMap,
            "windspeed" => $windspeedDataMap,
            "spraying" => $sprayingDataMap
        ];
    }

    public function forecastByHourEchart(UserStation $station, int $resolution, int $forecastDays, string $date = null): EChartsForecastDataFormatter
    {
        $heapOptions = [
            'Station ID' => $station->id,
            'Type' => 'Forecast'
        ];

        $dates = [];

        if (isset($date)) {
            $dates[] = $date;
        } else {
            $begin = new \DateTime();
            $end = new \DateTime();
            $end = $end->modify("+ {$forecastDays} days");

            $dateRange = new \DatePeriod(
                $begin,
                new \DateInterval('P1D'),
                $end
            );

            foreach ($dateRange as $date) {
                $dates[] = $date->format('Y-m-d');
            }
        }

        $dataByHours = $this->forecastByHour($station->latitude, $station->longitude, $dates, $heapOptions, $resolution, $forecastDays, true);
        $dataMap = $this->mapDataForecastForEchart($dataByHours, $resolution);
        $dataMap["days"] = array_map(function ($date) {
            return strtotime($date) * 1000; // milliseconds
        }, $dates);
        
        $forecastEchartFormatter = new EChartsForecastDataFormatter();
        $forecastEchartData = $forecastEchartFormatter->loadFromTemplate($dataMap);

        return $forecastEchartData;
    }

    /**
     * Forecast By Coordinates
     * @param array $arrQuery
     * @return array
     */
    public function forecastByCoordinates($arrQuery)
    {
        $feedТype = 'basic-1h';

        $cacheMinutes = $this->calcCacheMinutes();

        //Retrieve or Store from the Cache
        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes,
            function ($content) {

                return $this->mapDataForecast($content, 'data_1h');
            });

        //this is out of cache
        return $this->filterForecast($arrContent, [$arrQuery['date']]);
    }

    /**
     * Forecast Day By Coordinates
     * @param array $arrQuery
     * @return array
     */
    public function forecastDayByCoordinates($arrQuery)
    {
        $feedТype = 'basic-day';

        $cacheMinutes = $this->calcCacheMinutes();

        //Retrieve or Store from the Cache
        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes,
            function ($content) {

                return $this->mapDataForecast($content, 'data_day');
            });

        return $arrContent;
    }

    /**
     * History Basic Day
     * @param int $gid
     * @param object $heap
     * @return array
     */
    public function historyBasicDay($gid)
    {
        $cacheMinutes = $this->calcCacheMinutes();

        $heap = $this->heap;

        $plot = Plot::find($gid);

        if (!$plot->station_id) {
            return $this->getHistoryData($gid, $cacheMinutes);
        }

        //Get History Data from Pessl or OnSite or MeteBlue
        $station = $plot->station;
        if (!$station) {
            return Response::json(array('error' => "No station found."), 403);
        }

        $stationObject = UserStation::where('id', $station->id)->first()->getStationApi();

        $data = $stationObject->getHistoryData($cacheMinutes, function () use ($gid, $heap, $station) {

            $heap->track('History Basic Day - ' . $station->type, [
                'Zone GID' => $gid,
                'Type' => 'Air Temperature'
            ]);

        });
        if (is_array($data) && isset($data['error'])) {

            return Response::json(array('error' => $data['error']), $data['http_code']);
        }

        $now = new \DateTime();
        $lastYear = clone $now;
        $lastYear->modify('-1 year');

        $diff = $lastYear->diff($now);
        $days = $diff->format('%a');

        if (count($data) < $days) {
            $meteoBlueData = $this->getHistoryData($gid, $cacheMinutes);

            if (is_array($meteoBlueData) && isset($meteoBlueData['error'])) {

                return Response::json(['error' => $meteoBlueData['error']], $meteoBlueData['http_code']);
            }

            $yearPeriod = new \DatePeriod(
                $lastYear,
                new \DateInterval('P1D'),
                $now
            );

            $maxPrecipitation = 0;
            foreach ($meteoBlueData as $each) {
                if ($each['precipitation'] > $maxPrecipitation) {
                    $maxPrecipitation = $each['precipitation'];
                }
            }

            $maxPrecipitation = round($maxPrecipitation * 1.5, 0, PHP_ROUND_HALF_UP);

            foreach ($yearPeriod as $each) {
                $meteoBlueValue = 0;
                if (!array_key_exists($each->format('Y-m-d'), $data)) {
                    $data[$each->format('Y-m-d')] = $meteoBlueData[$each->format('Y-m-d')];
                    $meteoBlueValue = $maxPrecipitation - $meteoBlueData[$each->format('Y-m-d')]['precipitation'];
                }

                $data[$each->format('Y-m-d')]['meteo_blue'] = $meteoBlueValue;
            }

            ksort($data);
        }

        return $data;
    }

    /**
     * GDD Data
     * @param int $gid
     * @param object $heap
     * @return array
     */
    public function getHistoryWeatherData($gid, $fromDate, $toDate)
    {
        $cacheMinutes = $this->calcCacheMinutes();

        $plot = Plot::find($gid);

        if (!$station = $plot->station) {
            return $this->historyDayByPeriod($gid, $fromDate, $toDate, $cacheMinutes);
        }

        $heap = $this->heap;

        //Get History Data from Pessl or OnSite or MeteBlue
        $objFromDate = new \DateTime($fromDate);

        /** @var IStation $stationObject */
        $stationObject = UserStation::where('id', $station->id)->first()->getStationApi();

        $historyPeriod = mktime(0, 0, 0, $objFromDate->format('m'), $objFromDate->format('d'),
            $objFromDate->format('Y'));

        $stationInstallDate = \DateTime::createFromFormat('Y-m-d', $station->install_date);

        if ($stationInstallDate > $objFromDate) {
            $historyPeriod = mktime(0, 0, 0, $stationInstallDate->format('m'), $stationInstallDate->format('d'), $stationInstallDate->format('Y'));
        }

        $data = $stationObject->getHistoryDataFrom($cacheMinutes, $historyPeriod, 'daily',
            function () use ($gid, $heap, $station) {
                $heap->track('History Basic Day - ' . $station->type, [
                    'Zone GID' => $gid,
                    'Type' => 'GDD'
                ]);

            });
        $now = new \DateTime();
        $diff = $objFromDate->diff($now);
        $days = $diff->format('%a');

        if (is_array($data) && isset($data['error'])) {

            return Response::json(array('error' => $data['error']), $data['http_code']);
        }

        if (count($data) < $days) {

            $nextYear = clone $objFromDate;
            $nextYear->modify('+1 year');
            $endDate = $nextYear->format('Y-m-d');

            $meteoBlueData = $this->historyDayByPeriod($gid, $fromDate, $endDate, $cacheMinutes);

            $period = new \DatePeriod(
                $objFromDate,
                new \DateInterval('P1D'),
                $nextYear
            );

            foreach ($period as $each) {
                if (!array_key_exists($each->format('Y-m-d'), $data) && isset($meteoBlueData[$each->format('Y-m-d')])) {
                    $data[$each->format('Y-m-d')] = $meteoBlueData[$each->format('Y-m-d')];
                }
            }

            ksort($data);

            $data = array_filter($data, function ($item) use ($fromDate, $endDate) {
                return ($item['time'] <= $endDate && $item['time'] >= $fromDate);
            });
        }

        return $data;
    }

    /**
     * Current For Map
     * @param Organization $organization
     * @param int $year
     * @return array
     */
    public function currentForMap(Organization $organization, $year)
    {
        $orderedPolygons = Order::orderedPolygons($organization, $year);

        $arrResult = [];

        foreach ($orderedPolygons as $key => $value) {

            $arrContent = $this->currentMeteoCommon($value->gid);

            if (!count($arrContent)) {
                return Helper::errorResponseMeteoBlue('longitude and latitude not found: plot ' . $value->gid);
            }

            $temp = round($arrContent['data_current']['temperature']);
            if ($temp == -0) {
                $temp = abs($temp);
            }

            array_push($arrResult, [
                'gid' => $value->gid,
                'lon' => $value->longitude,
                'lat' => $value->latitude,
                'temperature' => $temp,
                'pictocode' => $arrContent['data_current']['pictocode'],
                'isdaylight' => $arrContent['data_current']['isdaylight'],
            ]);
        }

        return $arrResult;
    }

    /**
     * Current For Map
     * @param int $userId
     * @param int $year
     * @return array
     */
    public function gddByCoordinates($longitude, $latitude, $sowingDate, $crop, $fromDate, $toDate, $organizationId = null)
    {
        if (!$longitude || !$latitude) {
            return Response::json(array('error' => 'longitude and latitude not found'), 500);
        }

        if ($fromDate > date('Y-m-d')) {
            return Response::json(array('error' => 'No content'), 204);
        }

        if ($sowingDate > date('Y-m-d')) {
            return Response::json(array('error' => 'No content'), 204);
        }

        //  for first day of the month
        $periodStartDate = date('Y-m-01');

        if ($fromDate) {
            $periodStartDate = $fromDate;
        }

        //  for last day of the month
        $enddate = date('Y-m-t');

        if ($toDate) {
            $enddate = $toDate;
        }

        $enddate = $enddate > date('Y-m-d') ? date('Y-m-d') : $enddate;

        $userStation = UserStation::inRadiusOfStation($longitude, $latitude, $organizationId)->first();
        $cacheMinutes = $this->calcCacheMinutes();

        $objFromDate = $this->changeFromDate($sowingDate);

        if ($userStation && $userStation->type != 'Virtual') {

            $stationObject = $userStation->getStationApi();

            $historyPeriod = mktime(0, 0, 0, $objFromDate->format('m'), $objFromDate->format('d'),
                $objFromDate->format('Y'));

            $stationInstallDate = \DateTime::createFromFormat('Y-m-d', $userStation->install_date);

            if ($stationInstallDate > $objFromDate) {
                $historyPeriod = mktime(0, 0, 0, $stationInstallDate->format('m'), $stationInstallDate->format('d'), $stationInstallDate->format('Y'));
            }

            $data = $stationObject->getHistoryDataFrom($cacheMinutes, $historyPeriod, 'daily',
                function () use ($userStation) {

                });

            $now = new \DateTime();
            $diff = $objFromDate->diff($now);
            $days = $diff->format('%a');

            if (is_array($data) && isset($data['error'])) {

                return Response::json(array('error' => $data['error']), $data['http_code']);
            }

            if (count($data) < $days) {

                $meteoBlueData = $this->getHistoryDataByCoordinates($cacheMinutes, $longitude, $latitude);
                $period = new \DatePeriod(
                    $objFromDate,
                    new \DateInterval('P1D'),
                    $now
                );

                foreach ($period as $each) {
                    if (!array_key_exists($each->format('Y-m-d'), $data)) {
                        $data[$each->format('Y-m-d')] = $meteoBlueData[$each->format('Y-m-d')];
                    }
                }
                ksort($data);
            }

            return $this->gddResultFilter($data, $crop, $periodStartDate, $enddate);
        }

        $arrQuery = array(
            'startdate' => $objFromDate->format('Y-m-d'),
            'enddate' => $enddate,
            'lat' => $latitude,
            'lon' => $longitude,
        );

        $feedТype = 'historybasic-day';

        //Retrieve or Store from the Cache
        $data = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes,
            function ($content) {
                return $this->mapDataHistory($content, 'history_day');
            });

        return $this->gddResultFilter($data, $crop, $periodStartDate, $enddate);
    }

    //Algorithm for Spraying by Day
    public function calcSprayingDay($value)
    {

        $color = config('meteo.MEDIUM_RISK_COLOR');
        $risk = config('meteo.MEDIUM_RISK_TEXT');

        if ($value['windspeed_mean'] < 2 &&
            ($value['temperature_mean'] > 5 && $value['temperature_mean'] < 30) &&
            ($value['relativehumidity_mean'] > 50 && $value['relativehumidity_mean'] < 95) &&
            $value['precipitation'] < 0.1) {

            //Low Risk
            $color = config('meteo.LOW_RISK_COLOR');
            $risk = config('meteo.LOW_RISK_TEXT');

        } else {
            if (($value['windspeed_mean'] >= 2 && $value['windspeed_mean'] <= 5) &&
                ($value['temperature_mean'] >= 25 && $value['temperature_mean'] <= 5) &&
                ($value['relativehumidity_mean'] >= 30 && $value['relativehumidity_mean'] <= 99) &&
                ($value['precipitation'] >= 0.1 && $value['precipitation'] <= 0.3)) {

                //Meduim Risk
                $color = config('meteo.MEDIUM_RISK_COLOR');
                $risk = config('meteo.MEDIUM_RISK_TEXT');

            } else {
                if ($value['windspeed_mean'] > 5 ||
                    ($value['temperature_mean'] > 30 || $value['temperature_mean'] < 0) ||
                    ($value['relativehumidity_mean'] < 26 || $value['relativehumidity_mean'] > 99) ||
                    $value['precipitation'] > 0.3) {

                    //High Risk
                    $color = config('meteo.HI_RISK_COLOR');
                    $risk = config('meteo.HI_RISK_TEXT');
                }
            }
        }

        return ['color' => $color, 'risk' => $risk];
    }

    //Algorithm for Spraying by Hour
    public function calcSprayingHour($value)
    {

        $color = config('meteo.MEDIUM_RISK_COLOR');
        $risk = config('meteo.MEDIUM_RISK_TEXT');

        if ($value['windspeed'] < 2 &&
            ($value['temperature'] > 5 && $value['temperature'] < 30) &&
            ($value['relativehumidity'] > 50 && $value['relativehumidity'] < 95) &&
            $value['precipitation'] < 0.1) {

            //Low Risk
            $color = config('meteo.LOW_RISK_COLOR');
            $risk = config('meteo.LOW_RISK_TEXT');

        } else {
            if (($value['windspeed'] >= 2 && $value['windspeed'] <= 5) &&
                ($value['temperature'] >= 25 && $value['temperature'] <= 5) &&
                ($value['relativehumidity'] >= 30 && $value['relativehumidity'] <= 99) &&
                ($value['precipitation'] >= 0.1 && $value['precipitation'] <= 0.3)) {

                //Meduim Risk
                $color = config('meteo.MEDIUM_RISK_COLOR');
                $risk = config('meteo.MEDIUM_RISK_TEXT');

            } else {
                if ($value['windspeed'] > 5 ||
                    ($value['temperature'] > 30 || $value['temperature'] < 0) ||
                    ($value['relativehumidity'] < 26 || $value['relativehumidity'] > 99) ||
                    $value['precipitation'] > 0.3) {

                    //High Risk
                    $color = config('meteo.HI_RISK_COLOR');
                    $risk = config('meteo.HI_RISK_TEXT');
                }
            }
        }

        return ['color' => $color, 'risk' => $risk];
    }

    public function getDailyDataByCoordinatesAndDate($latitude, $longitude, \DateTime $date, $sensor)
    {
        if ($date > (new \DateTime())) {
            return false;
        }

        $arrQuery = array(
            'startdate' => $date->format('Y-m-d'),
            'enddate' => $date->format('Y-m-d'),
            'lat' => $latitude,
            'lon' => $longitude,
        );

        $feedType = 'historybasic-day';
        $cacheMinutes = config('meteo.METEO_CACHE_MINUTES_CURRENT');

        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedType, $cacheMinutes,
            function ($content) {
                return $this->mapDataHistory($content, 'history_day');
            });

        $data = is_array($arrContent) ? array_filter($arrContent, function ($element) use ($date) {
            return $element['time'] === $date->format('Y-m-d');
        }) : [];

        $data = reset($data);

        $sensorCodesMappingArray = config('meteo')['sensor_codes_mapping'];
        $sensorName = substr($sensor, 0, strrpos($sensor, '_'));
        if (array_key_exists($sensorName, $sensorCodesMappingArray) && !empty($sensorCodesMappingArray[$sensorName])) {
            $sensorValue = $sensorCodesMappingArray[$sensorName];
            $aggregation = substr($sensor, strrpos($sensor, '_') + 1);
            if ($aggregation === 'avg') {
                $aggregation = 'mean';
            }

            foreach ($sensorValue['codes'] as &$code) {
                $code = $code . '_' . $aggregation;
            }

            $sensorCodes[$sensor] = $sensorValue['codes'];
        }

        $cumulativeSensors = [];


        foreach ($sensorCodes as $key => $sensor) {

            //If there is NO data for the sensor - REMOVE it from the sensors
            $foundSensorData = false;
            foreach ($sensor as $sensorCode) {
                $foundSensor = array_filter($data, function ($elem, $key) use ($sensorCode) {
                    return $key == $sensorCode;
                }, ARRAY_FILTER_USE_BOTH);

                if (empty($foundSensor)) {
                    continue;
                }

                $foundSensorData = $foundSensor[$sensorCode];
            }

            if (empty($foundSensorData)) {
                unset($sensorCodes[$key]);
                continue;
            }

            if (strpos($key, 'cumulative')) {
                $cumulativeSensors[$key] = 0;
            }
        }

        return $foundSensorData;
    }

    /**
     * Change From Date to not more 1 year period from now
     * @param content
     * @param type
     */
    private function changeFromDate($sowingDate)
    {
        $objFromDate = new \DateTime($sowingDate);

        $now = new \DateTime();
        $oneYearPeriodFromNow = clone $now;
        $oneYearPeriodFromNow->modify('-1 year');

        $diff = $objFromDate->diff($now);
        $daysToSowingDate = $diff->format('%a');

        $diff = $oneYearPeriodFromNow->diff($now);
        $daysYearAgo = $diff->format('%a');

        if ($daysToSowingDate > $daysYearAgo) {
            $objFromDate = $oneYearPeriodFromNow;
        }

        return $objFromDate;
    }

    /**
     * GDD Result Filter
     * @param content
     * @param type
     */
    private function gddResultFilter($data, $crop, $periodStartDate, $enddate)
    {
        $GDDHelper = new GDDHelper($data);
        if ($crop != '-') {
            $GDDHelper->crop = $crop;
        }

        $GDDResults = $GDDHelper->getMeteoCalculations();

        $results = array_filter($GDDResults, function ($item) use ($periodStartDate, $enddate) {
            return ($item['time'] <= $enddate && $item['time'] >= $periodStartDate);
        });

        return $results;
    }

    /**
     * Map Data History
     * @param content
     * @param type
     */
    private function historyDayByPeriod($gid, $fromDate, $toDate, $cacheMinutes)
    {
        //Get Location of the Meteo point by gid and user_id
        $result = Plot::findMeteoLocation($gid, Auth::user()->lastChosenOrganization);

        if (!$result['longitude'] || !$result['latitude']) {
            return Response::json(array('error' => 'longitude and latitude not found'), 500);
        }

        if ($fromDate > date('Y-m-d')) {
            return Response::json(array('error' => 'No content'), 204);
        }

        $endDate = $toDate > date('Y-m-d') ? date('Y-m-d') : $toDate;

        $arrQuery = array(
            'startdate' => $fromDate,
            'enddate' => $endDate,
            'lat' => $result['latitude'],
            'lon' => $result['longitude'],
        );

        $feedТype = 'historybasic-day';
        $heap = $this->heap;

        //Retrieve or Store from the Cache
        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes,
            function ($content) use ($heap, $result) {

                $heap->track('History Basic Day', [
                    'Zone GID' => $result['zone_gid'],
                    'Type' => 'GDD'
                ]);

                return $this->mapDataHistory($content, 'history_day');
            });

        return $arrContent;
    }

    /**
     * Get History Data
     * @param cacheMinutes
     */
    private function getHistoryData($gid, $cacheMinutes)
    {
        //Get Location of the Meteo point by gid and user_id
        $result = Plot::findMeteoLocation($gid, Auth::user()->lastChosenOrganization);

        if (!$result['longitude'] || !$result['latitude']) {
            return ['error' => 'longitude and latitude not found', 'http_code' => 500];
        }

        return $this->getHistoryDataByCoordinates($cacheMinutes, $result['longitude'], $result['latitude'],
            $result['zone_gid']);
    }

    /**
     * Get History Data By Coordinates
     * (requests from the Meteo app don't have gid - used in getHistoryData)
     * @param cacheMinutes
     * @param longitude
     * @param latitude
     * @param zoneGid
     */
    private function getHistoryDataByCoordinates($cacheMinutes, $longitude, $latitude, $zoneGid = null)
    {
        $historyPeriod = 1; //1 year history period from today
        $prevYear = mktime(0, 0, 0, date("m"), date("d"), date("Y") - $historyPeriod);

        $arrQuery = array(
            'lat' => $latitude,
            'lon' => $longitude,
            'startdate' => date('Y-m-d', $prevYear),
            'enddate' => date('Y-m-d'),

        );

        $feedТype = 'historybasic-day';
        $heap = $this->heap;
        //Retrieve or Store from the Cache
        $arrContent = $this->meteoCache->getMeteoBlueFromCache($arrQuery, $feedТype, $cacheMinutes,
            function ($content) use ($heap, $zoneGid) {

                $heap->track('History Basic Day', [
                    'Zone GID' => $zoneGid,
                    'Type' => 'Air Temperature'
                ]);

                return $this->mapDataHistory($content, 'history_day');
            });

        //this is out of cache
        return Helper::filterMeteoDataHistory($arrContent, $prevYear);
    }

    /**
     * Map Data History
     * @param content
     * @param type
     */
    private function mapDataHistory($content, $type)
    {
        if (empty($content) || !array_key_exists($type, $content) || (!is_array($content[$type]['time']) && !strlen($content[$type]['time']))) {
            return Helper::errorResponseMeteoBlue('No data');
        }

        $arrContent = [];

        if (!is_array($content[$type]['time'])) {

            $index = 0;
            $date = $content[$type]['time'];

            $arrContent[$date]['time'] = $date;

            foreach ($content[$type] as $key => $value) {
                if ($key != 'time') {
                    $arrContent[$date][$key] = $value[$index];
                }
            }

            return $arrContent;
        }

        foreach ($content[$type]['time'] as $index => $date) {
            foreach ($content[$type] as $key => $value) {
                $arrContent[$date][$key] = $value[$index];
            }
        }

        return $arrContent;
    }

    /**
     * Filter Forecast
     * @param arrContent
     */
    private function filterForecast($arrContent, $dates, bool $includeNextDayMidnight = false)
    {
        if (!is_array($arrContent)) {
            return Helper::errorResponseMeteoBlue('No data');
        }

        $collectContent = collect($arrContent);

        $dates = array_filter($dates, function ($date) {
            return $date !== null;
        });

        if (!isset($dates) || (isset($dates) && count($dates) === 0)) {
            //Return only present and future dates
            $prev_date = date('Y-m-d', strtotime(' -1 day'));

            $filtered = $collectContent->filter(function ($item) use ($prev_date) {

                return explode("T", $item['time'])[0] != $prev_date;
            });

            $arrFiltered = $filtered->toArray();

            return $arrFiltered;
        }

        $lastDate = end($dates);
        if ($includeNextDayMidnight) {
            $dates[] = \DateTime::createFromFormat('Y-m-d', $lastDate)->modify('+ 1 day')->format('Y-m-d');
            $lastDate = end($dates);
        }

        //Filter by date - this is out of cache
        $filtered = $collectContent->filter(function ($item) use ($dates, $lastDate, $includeNextDayMidnight) {
            $timestamp = strtotime($item['time']);
            $date = date('Y-m-d', $timestamp);
            $time = date('H:i:s', $timestamp);

            if ($includeNextDayMidnight && $date == $lastDate && $time != '00:00:00') {
                return false;
            }

            return in_array($date, $dates);
        });

        $arrFiltered = $filtered->toArray();

        return $arrFiltered;
    }

    /**
     * Map Data Forecast Day
     * @param content
     * @param type
     */
    private function mapDataForecast($content, $type)
    {

        if (!is_array($content[$type]['time'])) {
            return Helper::errorResponseMeteoBlue('No data');
        }

        $arrContent = [];
        foreach ($content[$type]['time'] as $index => $date) {
            foreach ($content[$type] as $key => $value) {
                $arrContent[$date][$key] = $value[$index];
            }
        }

        return $arrContent;
    }

    /**
     * Calculetes cache minutes from now to 01:00:00 in the night
     * @return int
     */
    private function calcCacheMinutes()
    {
        $now = Carbon::now();

        $oneMidnight = Carbon::create($now->year, $now->month, $now->day, 1, 0, 0);

        $isBeforeOne = $oneMidnight > $now;

        if (!$isBeforeOne) {
            $oneMidnight->addDays(1);
        }

        $minUntilOne = $now->diffInMinutes($oneMidnight);

        return $minUntilOne;
    }
}
