<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>nonchev
 * Date: 7/2/2020
 * Time: 4:58 PM
 */

namespace App\Classes\Meteo;


use App\Classes\Heap;
use App\Classes\Echarts\IEChartsFormattable;
use App\Classes\MeteoStationReportFormatter;
use App\Models\UserStation;
use Response;

abstract class AbstractBaseStation implements IStation, IEChartsFormattable
{
    protected $stationModel;
    private $heap;

    public function __construct(UserStation $stationModel, Heap $heap)
    {
        $this->stationModel = $stationModel;
        $this->heap = $heap;
    }

    /**
     * @param $feed
     * @param $period
     * @param $sensors
     * @param $fromDate
     * @param $toDate
     * @param bool $isTimestamp
     * @return array
     * @throws \Exception
     */
    public function getStationReportEChart($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp = false)
    {
        if (!$this->stationModel) {
            throw new \Exception('User station not found.', 404);
        }

        $stationName = $this->stationModel->name;
        $stationType = $this->stationModel->type;

        //update last communication
        $stationInfo = $this->getStationData();

        if (!is_array($stationInfo) || !isset($stationInfo['dates']) || !isset($stationInfo['position'])) {
            throw new \Exception('User station not found.', 404);
        }

        $heap = $this->heap;

        $dateRange = $this->dateRange($period, $this->stationModel->install_date, $fromDate, $toDate, $isTimestamp);
        $from = $dateRange["timestampFrom"];
        $to = $dateRange["timestampTo"];

        if (!$from) {
            throw new \Exception('Start date is required!', 400);
        }

        $totalExpextedCount = $this->totalExpectedCount($feed, $from, $to);

        $allowedRecordsCount = config('echarts.stations.allowedRecordsCount');
        if ($totalExpextedCount > $allowedRecordsCount) {
            throw new \Exception('You have selected too much data, please increase the reporting time or reduce the preview time.', 416);
        }

        $cacheMinutes = 15; //15 min.
        $chart_data = $this->getHistoryDataEChartsFormatted($cacheMinutes, $stationInfo, $from, $to, $feed,
            $sensors,
            function () use ($stationName, $heap, $stationType, $period, $feed) {
                $heap->track("History Basic {$feed} - {$period} Period - " . $stationType, [
                    'Station' => $stationName,
                    'Type' => "{$period} Period"
                ]);
            });

        if (!is_array($chart_data)) {
            throw new \Exception('Station not found.', 500);
        }

        return $chart_data;
    }

    /**
     * @param $feed
     * @param $period
     * @param $sensors
     * @param $fromDate
     * @param $toDate
     * @param bool $isTimestamp
     * @return array
     * @throws \Exception
     */
    public function getStationReport($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp = false)
    {
        $chart_data = $this->getStationReportEChart($feed, $period, $sensors, $fromDate, $toDate, $isTimestamp);

        $formatter = new MeteoStationReportFormatter();
        $stationName = $this->stationModel->custom_name ?? $this->stationModel->name;
        return $formatter->format($chart_data, $stationName);
    }

    /**
     * @param $period
     * @param $installDate
     * @param $fromDate
     * @param $toDate
     * @param $isTimestamp
     * @return array
     * @throws \Exception
     */
    protected function dateRange($period, $installDate, $fromDate, $toDate, $isTimestamp)
    {
        if ($isTimestamp) {
            return ["timestampFrom" => $fromDate, "timestampTo" => $toDate];
        }

        $timestampFrom = null;
        $timestampTo = null;

        if ($period !== "custom") {
            $now = new \DateTime('now', (new \DateTimeZone('UTC')));
            $periodStart = clone $now;
            $periodStart->modify('-' . $period);
            $stationInstallDate = \DateTime::createFromFormat('Y-m-d', $installDate, (new \DateTimeZone('UTC')));

            if ($stationInstallDate > $periodStart) {
                $periodStart = $stationInstallDate;
            }

            $periodStart->setTime(0, 0, 0);
            $timestampFrom = $periodStart->getTimestamp();
            $timestampTo = null;

            return ["timestampFrom" => $timestampFrom, "timestampTo" => $timestampTo];
        }

        if (!$fromDate || !$toDate) {
            return ["timestampFrom" => $timestampFrom, "timestampTo" => $timestampTo];
        }

        $periodStart = \DateTime::createFromFormat('Y-m-d', $fromDate, (new \DateTimeZone('UTC')))->setTime(0, 0, 0);
        $timestampFrom = $periodStart->getTimestamp();

        $periodEnd = \DateTime::createFromFormat('Y-m-d', $toDate, (new \DateTimeZone('UTC')))->setTime(0, 0, 0);
        $periodEnd->modify('+1 day');
        $timestampTo = $periodEnd->getTimestamp();

        return ["timestampFrom" => $timestampFrom, "timestampTo" => $timestampTo];
    }

    /**
     * @param $feed
     * @param $from
     * @param $to
     * @return float|int
     * @throws \Exception
     */
    protected function totalExpectedCount($feed, $from, $to)
    {
        $now = new \DateTime();
        $date = clone $now;
        $date->setTimestamp($from);

        if ($to) {
            $now->setTimestamp($to);
        }

        $diff = $date->diff($now);
        $days = intval($diff->format('%a'));

        switch ($feed) {
            case 'hourly':
                return $days * 24;
            case 'raw':
                return $days * 24 * 4;
            default:
                return $days;
        }
    }
}