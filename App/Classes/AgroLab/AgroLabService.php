<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\AgroLab;

use App\Classes\CouchDBClient;
use App\Models\SoilSampleNumber;
use Doctrine\CouchDB\Mango\MangoQuery;

class AgroLabService
{
    private $couchDBClient;

    public function __construct(CouchDBClient $couchDB)
    {
        $this->couchDBClient = $couchDB->client;
    }

    public function getRecievedSamples($batch, $fromDate, $toDate, $limit, $page, $sort)
    {
        $selector = $this->defaultSelector();

        //Filter
        $fromDateObject = new \DateTime($fromDate);
        $toDateObject = new \DateTime($toDate);
        $toDateObject->modify('+1 day');
        if($fromDate) {
            $selector['dateStamp']['$gte'] = $fromDateObject->getTimestamp();
        }
        if($toDate) {
            $selector['dateStamp']['$lte'] = $toDateObject->getTimestamp();
        }
        if($batch) {
            $selector['batch'] = $batch;
        }

        $options = ['limit'=>PHP_INT_MAX, 'sort' => [['dateStamp' => $sort]]];
        $queryTotal = new MangoQuery($selector, $options);
        $resultTotal = $this->couchDBClient->find($queryTotal);
        if(!isset($resultTotal->body['docs'])) {
            return 'error';
        }
        $totalCount = count($resultTotal->body['docs']);

        //Page limit
        $options['limit'] = $limit;
        $options['skip'] = ($page - 1)*$limit;
        $query = new MangoQuery($selector, $options);
        $result = $this->couchDBClient->find($query);

        if(!isset($result->body['docs'])) {
            return 'error';
        }

        $jsonDocs = json_encode($result->body['docs'], JSON_UNESCAPED_UNICODE);

        return ['jsonDocs' => $jsonDocs, 'totalCount' => $totalCount];
    }

    public function getCouchDbBarcodes($filter, $sort)
    {
        $batch = $filter['batch'];
        $barcodeLab = $filter['barcodeLab'];
        $barcodeSampling = $filter['barcodeSampling'];

        $selector = $this->defaultSelector();

        $haveSearch = false;
        if ($batch) {
            $selector['batch'] = $batch;
            $haveSearch = true;
        }
        if ($barcodeLab) {
            $selector['samples']['$elemMatch']['barcode'] = $barcodeLab;
            $haveSearch = true;
        }
        if ($barcodeSampling && !is_array($barcodeSampling)) {
            $selector['samples']['$elemMatch']['barcode'] = $barcodeSampling;
            $haveSearch = true;
        }
        if ($barcodeSampling && is_array($barcodeSampling)) {
            $selector['samples']['$elemMatch']['barcode']['$in'] = $barcodeSampling;
            $haveSearch = true;
        }

        if(!$haveSearch) {
            return ['jsonDocs' => '[]'];
        }

        $options = ['limit'=>PHP_INT_MAX, 'sort' => [['dateStamp' => $sort]]];
        $query = new MangoQuery($selector, $options);
        $result = $this->couchDBClient->find($query);

        if(!isset($result->body['docs'])) {
            return 'error';
        }

        $jsonDocs = json_encode($result->body['docs'], JSON_UNESCAPED_UNICODE);

        return ['jsonDocs' => $jsonDocs];
    }

    public function checkBarcodes($jsonDocs, $filter, $sort)
    {
        if($jsonDocs != '[]'){
            $result = SoilSampleNumber::joinCheckedBarcodes($jsonDocs, $filter);
            return $result;
        }

        $dbBarcodes = SoilSampleNumber::dbCheckedBarcodes($filter)->get()->toArray();

        if(count($dbBarcodes) > 0){
            $arrBarcodeSampling = array_map(function ($value) {
                return $value['barcode_sampling'];
            }, $dbBarcodes);

            $filterCheckedBarcodes = $filter;
            $filterCheckedBarcodes['barcodeSampling'] = $arrBarcodeSampling;
            $couchDbBarcodes = $this->getCouchDbBarcodes($filterCheckedBarcodes, $sort);

            $result = SoilSampleNumber::joinCheckedBarcodes($couchDbBarcodes['jsonDocs'], $filter);
            return $result;
        }

        return $dbBarcodes;
    }

    public function isFilterCheckedBarcodesEmpty($filter)
    {
        if(strlen($filter['plotName']) <= 1){
            $filter['plotName'] = '';
        }

        if(!array_filter($filter)) {
            return true;
        }
        return false;
    }

    private function  defaultSelector()
    {
        $afterDate = config('couchdb.recievedSamples.afterDate');
        $selector = [
            'type' => 'protocol-model'
        ];
        $selector['dateStamp'] = ['$gt' => $afterDate];

        return $selector;
    }
}
