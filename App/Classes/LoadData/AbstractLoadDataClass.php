<?php

namespace App\Classes\LoadData;

use App\Models\Farm;
use App\Models\File;
use App\Models\Layer;
use Config;
use DB;
use Exception;
use Illuminate\Support\Facades\File as FileSystem;
use Log;
use Schema;

/**
 * <AUTHOR> <<EMAIL>>
 * @link http://www.technofarm.bg/
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
abstract class AbstractLoadDataClass
{

    protected $fileData = null;
    protected $ID = null;
    protected $fileName = null;
    protected $dateUploaded = null;
    protected $status = null;
    protected $errors = null;
    protected $farming = null;
    protected $year = null;
    protected $userID = null;
    protected $name = null;
    protected $crs = null;
    protected $layerType = null;
    protected $definition = null;
    protected $farm = null;
    protected $groupID = null;
    protected $deviceType = null;
    protected $database = null;

    protected $filePath = null;
    protected $fileType = null;
    protected $userDir = null;
    protected $userSubDir = null;
    protected $modulesPath = null;
    protected $tmpTable = null;
    protected $processingSubDir = null;

    protected $LayersController = null;    
    protected $FarmingController = null;  
    protected $UsersDbController = null;  

    protected $shapeFile = null;  
    protected $dbfFile = null;  
    protected $shapeFlag = null;  
    protected $dbfFlag = null;  
    protected $tmpCRS = null;  

    protected $file = null; 
    
    private $pythonPath;


    const PGSQL_CREATE = '-c';
    const PGSQL_DELETE = '-d';
    const PGSQL_APPEND = '-a';
    
    public function __construct(File $file)
    {
        
        $this->file = $file;
        
        if ($this->file)
        {
            $this->initClassVariables($this->file);
        }
        
        $this->pythonPath = Config::get('globals.PYTHON_PATH');
    }


    // Методи, които трябва да бъдат дефинирани в дъщерния клас
    /**
     * startProcessing function that starts the process of loading
     * @return void
     */
    abstract protected function startProcessing();

    /**
     * copyProcess executes the process to copy the data
     * @return void
     */
    abstract protected function copyProcess();

    /**
     * initClassVariables - init Class Variables
     * @param  array $currentData - a row data from table "su_users_files"
     * @return void
     */
    public function initClassVariables($currentData) 
    {
        $this->fileData = $currentData;
        
        $this->ID = $this->fileData['id'];
        $this->fileName = $this->fileData['filename'];
        $this->dateUploaded = $this->fileData['date_uploaded'];
        $this->status = $this->fileData['status'];
        $this->errors = $this->fileData['errors'];
        $this->farming = $this->fileData['farming'];
        $this->year = $this->fileData['year'];
        $this->userID = $this->fileData['user_id'];
        $this->name = str_replace(' ', '_', $this->fileData['name']);
        $this->name = str_replace('(', '_', $this->name);
        $this->name = str_replace(')', '_', $this->name);
        $this->crs = (int) $this->fileData['crs'];
        $this->layerType = $this->fileData['shape_type'];
        $this->definition = $this->fileData['definition'];
        $this->groupID = $this->fileData['group_id'];
        $this->deviceType = $this->fileData['device_type'];

        $default = Config::get('database.default');
        $this->database = Config::get('database.connections.' . $default . '.database');

        Config::set('database.connections.userdb', Config::get('database.connections.'.$default));
        Config::set('database.connections.userdb.database', $this->database);

        $this->filePath = config('globals.LAYERS_QUEUE_PATH') . $currentData['filename'];
        $this->userDir = config('globals.LAYERS_QUEUE_PATH') . $currentData['user_id'];
        $this->userSubDir = $this->userDir.DIRECTORY_SEPARATOR.$this->userID."_".$this->name.DIRECTORY_SEPARATOR;

        $this->modulesPath = config('globals.SCRIPTS_PATH');

        if(isset($this->fileData['farm_id'])){
            $this->farm = Farm::findOrFail($this->fileData['farm_id']);    
        }
        
        //init default tmp Table
        $this->tmpTable = "tmp_satellite_".$this->ID;
    }    

    /**
     * errorLog - updates the errors field in DB of the processing file
     * @param  integer $error - code error
     * @param  string $message - error message
     * @return void
     */
    public function errorLog($error, $message = '')
    {
        //find correct error in the error message
        $error = $this->catchError($error, $message);
        
        Log::info('LoadBoundariesFile Job: ', [
            'id' => $this->ID,
            'error' => $error
        ]);
        
        $this->file->status = $error;
        
        if(strlen($message))
        {
            $this->file->errors = $message;
        }
        
        $this->file->save();

        //remove the unziped dir
        FileSystem::deleteDirectory($this->userSubDir);
    }

    /**
     * catchCorrectError - find correct error in the error message
     * @param  integer $error  
     * @param  string $message 
     * @return integer 
     */
    public function catchError($error, $message = '')
    {
        if(strlen($message))
        {
            // below we can add many checks for differnt erros
             
            if($this->findError("transform: couldn't project point", $message))
            {
                $error = File::ERROR_INVALID_CRS;                
            }

            if($this->findError("One of the geometries in the set could not be converted to GEOS", $message))
            {
                $error = File::ERROR_INVALID_GEOMETRY;                
            }

            if($this->findError("Unable to convert data", $message))
            {
                $error = File::ERROR_INCORRECT_ENCODING;                
            }

            if($this->findError("Unable to convert field name", $message))
            {
                $error = File::ERROR_INCORRECT_ENCODING_FIELD;                
            }

            if($this->findError("Geometry type (GeometryCollection)", $message))
            {
                $error = File::ERROR_GEOMETRY_COLLECTION;                
            }

            if($this->findError("Error in fread()", $message) || $this->findError("Error reading shape object", $message))
            {
                $error = File::ERROR_READING_SHAPE_OBJECT;                
            }
                
        }    
        return $error;        
    }

    /**
     * findError - find if the error contains in the error message
     * @param  string $search 
     * @param  string $message 
     * @return boolean          
     */
    public function findError($search, $message = '')
    {
        $pos = strpos($message, $search);

        if ($pos !== false)
        {
            return true;
        }else{
            return false;
        }    
    }    

    /**
     * onBeforeProcessing - we can call it before the Processing
     * @return void
     */
    public function onBeforeProcessing()
    {
        $this->file->status = File::LOADING_FILE_NOW;
        $this->file->save();
    }    

    /**
     * mainProcessing - executes the main process
     * @return void
     */
    public function mainProcessing() 
    {

        $this->onBeforeProcessing();

        //check CRS
        if (!$this->crs)
        {
            $this->errorLog(File::ERROR_RUNTIME, __METHOD__." Line:".__LINE__);
            return false;
        }

        try
        {

            if($this->status == File::LOADING_FILE && !strlen($this->definition)) {

                $this->unzipData();

                if ($this->layerType == Layer::LAYER_TYPE_SATELLITE_WORK)
                {
                    // satelite
                    $this->processSateliteItems();
                }

                if ($this->layerType == Layer::LAYER_TYPE_SOIL_GRID)
                {
                    // soil grid
                    $this->processSoilGridItems();
                    $this->checkGeometryTypes(array('ST_Polygon', 'ST_MultiPolygon'));
                }

                if ($this->layerType == Layer::LAYER_TYPE_SOIL_POINTS)
                {
                    // soil points
                    $this->processSoilPointsItems();
                    $this->checkGeometryTypes(array('ST_Point'));
                }

                if ($this->layerType == Layer::LAYER_TYPE_TRACKS)
                {
                    // tracks(arcs)
                    $this->processSoilTracksItems();
                    $this->checkGeometryTypes(array('ST_LineString'));
                }

                $this->finalActions();
            }
        } catch (Exception $e)
        {
            $this->mainException($e);
            return false;
        }

        $this->onAfterProcessing();
    }

    /**
     * mainProcessingSimple - executes a simple main process
     * @return void
     */
    public function mainProcessingSimple() 
    {

        $this->onBeforeProcessing();

        //check CRS
        if (!$this->crs)
        {
            $this->errorLog(File::ERROR_RUNTIME, __METHOD__." Line:".__LINE__);
            return false;
        }

        try
        {
            $this->unzipData();

            $this->processSimpleItems();

            $this->finalActions();            

        } catch (Exception $e)
        {
            $this->mainException($e);
            return false;
        }

        $this->onAfterProcessing();
    }

    /**
     * mainException manage errorLog
     * @param  Exception $e 
     * @return void
     */
    public function mainException($e)
    {
        $errorData = $e->getMessage();
        $erroCode = $e->getCode();

        if($erroCode != 0)
        {
            $this->errorLog($erroCode, $errorData);
        }else{
            $this->errorLog(File::ERROR_RUNTIME, $errorData);
        }    
    }        

    /**
     * onAfterProcessing - we can call it after the Processing
     * @return void
     */
    public function onAfterProcessing(){}    

    /**
     * unzipData - unzip the .zip file
     * @return void
     */
    public function unzipData()
    {
        try {

            if(mime_content_type($this->filePath) !== File::ZIP){
                return;
            }

            if(!file_exists($this->userDir))
            {
                @mkdir($this->userDir, 0777);
            }    
            
            FileSystem::deleteDirectory($this->userSubDir);
            @mkdir($this->userSubDir, 0777);

            //remove .zip file from "layers_queue" to the userDir and set $this->filePath
            if(file_exists($this->filePath))
            {
                rename($this->filePath, $this->userDir.DIRECTORY_SEPARATOR.$this->fileName);
                $this->filePath = $this->userDir.DIRECTORY_SEPARATOR.$this->fileName;
            }else {
                $this->filePath = $this->userDir.DIRECTORY_SEPARATOR.$this->fileName;
            }    

            $fileDataNoWS = str_replace(' ', '_', $this->filePath);
            rename($this->filePath, $fileDataNoWS);
            
            $this->file->filename = str_replace(' ', '_', $this->fileName);
            $this->file->save();
            
            $c = "unzip -o \"{$fileDataNoWS}\" -d {$this->userSubDir} 2>&1";
            exec($c);

            //rename file names to UTF-8
            $files = FileSystem::allFiles($this->userSubDir);

            for ($j = 0; $j < count($files); $j++)
            {
                
                $file = $this->userSubDir.$files[$j]->getFilename();

                rename($file, mb_convert_encoding($file, "UTF-8"));   
            }

        } catch (Exception $e) {
            
            throw new Exception($e->getMessage().'\n====='.$e->getTraceAsString(), File::ERROR_INVALID_ARCHIVE);
        }
    }    

    /**
     * processSoilPointsItems - Process Soil Points Items
     * @return void
     */
    public function processSoilPointsItems()
    {
        $allFiles = FileSystem::allFiles($this->userSubDir);
        
        $this->shapeFile = false;
        $this->dbfFile = false;

        $this->processingSubDir = $this->userSubDir; 

        //Проверяваме дали във архива има файлове с имена започващи с "points", ако има зареждаме тях, ако не връщаме оригиналния array с файлове
        $files = $this->filterFiles($allFiles, 'points');

        $this->setShapeAndDbf($files);
        
        //shp2pgsql common
        $this->commonSHP2PSQL();        

        $this->commonFinalActions(); 
    }

    /**
     * processSimpleItems - process a zip with 1 .shp and 1 .dbf file
     * @return void
     */
    public function processSoilGridItems()
    {
        $allFiles = FileSystem::allFiles($this->userSubDir);
        
        $this->shapeFile = false;
        $this->dbfFile = false;

        $this->processingSubDir = $this->userSubDir; 

        //Проверяваме дали във архива има файлове с имена започващи с "grid", ако има зареждаме тях, ако не връщаме оригиналния array с файлове
        $files = $this->filterFiles($allFiles, 'grid');

        $this->setShapeAndDbf($files);

        //shp2pgsql common
        $this->commonSHP2PSQL();

        $this->commonFinalActions(); 
    }

    /**
     * processSoilTracksItems - Process Soil Tracks Items
     * @return void
     */
    public function processSoilTracksItems()
    {
        $allFiles = FileSystem::allFiles($this->userSubDir);
        
        $this->shapeFile = false;
        $this->dbfFile = false;

        $this->processingSubDir = $this->userSubDir; 

        //Проверяваме дали във архива има файлове с имена започващи с "arc", ако има зареждаме тях, ако не връщаме оригиналния array с файлове
        $files = $this->filterFiles($allFiles, 'arc');

        $this->setShapeAndDbf($files);
        
        //shp2pgsql common
        $this->commonSHP2PSQL();        

        $this->commonFinalActions(); 
    }

    
    /**
     * commonFinalActions Common Final Actions
     * @return void
     */
    private function commonFinalActions()    
    {    
        $this->makeValidGeom();
        
        $this->runStDump();

        try {
            $this->transformColumnCRS();
        } catch (Exception $e) {
            throw new Exception($e->getMessage().'\n====='.$e->getTraceAsString(), File::ERROR_INVALID_GEOMETRY);
        }
    }    

    /**
     * setShapeAndDbf set Shape and Dbf Files
     * @param  array $files 
     * @return void
     */
    private function setShapeAndDbf($files)    
    {
        //set Shape and Dbf Files
        for ($j = 0; $j < count($files); $j++)
        {
            if ($files[$j] && strtolower($files[$j]->getExtension()) == 'shp')
            {
                $this->shapeFile = $files[$j]->getFilename();
            }
            if ($files[$j] && strtolower($files[$j]->getExtension()) == 'dbf')
            {
                $this->dbfFile = $files[$j]->getFilename();
            }
        }
    
        if (!$this->shapeFile)
        {
            throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_SHAPE);
        }

        if (!$this->dbfFile)
        {
            throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_DBF);
        }

        if (strtolower(strstr(".", $this->dbfFile)) !== strtolower(strstr(".", $this->shapeFile)))
        {    
            throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_ARCHIVE);
        }
    }   

    /**
     * filterFiles - проверяваме дали във архива има файлове с имена започващи с "prc_", ако има зареждаме тях, ако не хваща първите намерени
     * @param  array $files 
     * @param  string $pattern 
     * @return array
     */
    private function filterFiles($files, $pattern)    
    {
        $arrFiles = array_filter($files, function($k) use ($pattern) {
            
            $pos = strpos($k->getFilename(), $pattern);  
            
            return $pos !== false;  
        });   

        //Ако има файлове започващи с $pattern връщаме тях
        if(count($arrFiles))
        {
            foreach ($arrFiles as $key => $value)
            {
                $newArr[] = $value;
            }
            
            return $newArr;
        }else{
            //Ако не - връщаме оригиналния array с файлове
            return $files;
        }            
    }

    /**
     * processSimpleItems - process a zip with 1 .shp and 1 .dbf file
     * @return void
     */
    public function processSimpleItems()
    {
        $files = FileSystem::allFiles($this->userSubDir);
        
        $this->shapeFile = false;
        $this->dbfFile = false;

        $this->processingSubDir = $this->userSubDir; 

        //Проверяваме дали във архива има файлове с имена започващи с "prc_", ако има зареждаме тях, ако не връщаме оригиналния array с файлове
        $files = $this->filterFiles($files, 'prc_');
        
        //set Shape and Dbf Files
        for ($j = 0; $j < count($files); $j++)
        {
            if ($files[$j] && strtolower($files[$j]->getExtension()) == 'shp')
            {
                $this->shapeFile = $files[$j]->getFilename();
            }
            if ($files[$j] && strtolower($files[$j]->getExtension()) == 'dbf')
            {
                $this->dbfFile = $files[$j]->getFilename();
            }
        }
    
        if (!$this->shapeFile)
        {
            throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_SHAPE);
        }

        if (!$this->dbfFile)
        {
            throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_DBF);
        }

        if (strtolower(strstr(".", $this->dbfFile)) !== strtolower(strstr(".", $this->shapeFile)))
        {    
            throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_ARCHIVE);
        }
        
        // osz items
        if ($this->deviceType == File::DEVICE_OSZ)
        {
            $rebuildRes = [];
            exec("{$this->pythonPath} {$this->modulesPath}rebuild_shp.py \"{$this->processingSubDir}{$this->shapeFile}\" \"{$this->processingSubDir}{$this->dbfFile}\" 2>&1", $rebuildRes);
        }   

        //shp2pgsql common
        $this->commonSHP2PSQL();
    }

    /**
     * @return bool|string
     */
    public function processKML()
    {
        $cmdOGR2OGR = 'ogr2ogr -f "PostgreSQL" -t_srs EPSG:' . Config::get('globals.EPSG_PROJ') . ' -lco GEOMETRY_NAME=geom -lco FID=gid -lco SPATIAL_INDEX=YES PG:"host=' . Config::get('database.connections.' . Config::get('database.default') . '.host') . ' port=' . Config::get('database.connections.' . Config::get('database.default') . '.port') . ' user=' . Config::get('database.connections.' . Config::get('database.default') . '.username') . ' dbname=' . $this->database . ' password=' . Config::get('database.connections.' . Config::get('database.default') . '.password') . '" "' . $this->filePath . '" -nln ' . $this->tmpTable . '';
        $output = system($cmdOGR2OGR, $output);

        return $output;
    }

    public function commonOGR2OGR()
    {
        $shpEncoding = $this->getEncoding();
        
        if($shpEncoding == NULL || $shpEncoding == 'cp1251')
        {
            $shpEncoding = 'WIN1251';    
        }    

        $cmdOGR2OGR = 'export PGCLIENTENCODING='.$shpEncoding.' && ogr2ogr -skipfailures -overwrite -lco GEOMETRY_NAME=geom -lco FID=gid -lco SPATIAL_INDEX=YES -f "PostgreSQL" PG:"host='.Config::get('database.connections.'.Config::get('database.default').'.host').' user='.Config::get('database.connections.'.Config::get('database.default').'.username').' dbname='.$this->database.' password='.Config::get('database.connections.'.Config::get('database.default').'.password').'" "'.$this->processingSubDir.$this->shapeFile.'" -nln '.$this->tmpTable.'';
        $output = system($cmdOGR2OGR, $output);

        return $output;
    }    

    /**
     * Get Encoding 
     * @return string
     */
    private function getEncoding()
    {
        exec("{$this->pythonPath} {$this->modulesPath}get_enc.py \"{$this->processingSubDir}{$this->dbfFile}\"", $prjEncData);

        try {
            $prjEnc = json_decode($prjEncData[0]);
            $shpEncoding = $prjEnc->encoding;
        } catch (Exception $e) {
            $shpEncoding = 'UTF8';
        }

        return $shpEncoding;
    }    

    private function getEkatteFromFile()
    {
        $ekatte = strstr($this->shapeFile, 'G');

        if(!$ekatte)
        {
            throw new Exception(__METHOD__." Line:".__LINE__, File::INCONSISTENT_FILE_TYPE);
        }

        $ekatte = strstr($ekatte, '.', true);
        $ekatte = substr($ekatte, 1);    

        return $ekatte;
    }

    /**
     * processTopconOrMuellerItems - process Topcon Or Mueller Items
     * @return void
     */
    public function processTopconOrMuellerItems()
    {
        //check if main required directories exist
        if($this->deviceType == Config::DEVICE_MUELLER)
        {
            if (!file_exists($this->userSubDir . 'NavGuideExport'))
            {
                throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_ARCHIVE);
            }
    
            //getting client directories
            $this->processingSubDir = $this->userSubDir . 'NavGuideExport/';
        }else {
            $this->processingSubDir = $this->userSubDir;        
        }
    
        $files = $this->LayersController->File->getFilesFromDir($this->processingSubDir);
        $used_files = [];
    
        for ($j = 0; $j < count($files); $j++)
        {
            $this->shapeFile = false;
            $this->dbfFile = false;
            $k = 0;
         
            for($k = 0; $k < count($files); $k++)
            {
                if ($files[$k] && strtolower(FileSystem::extension($files[$k]['name'])) == '.shp'
                && !in_array($files[$k]['name'], $used_files))
                {
                    $this->shapeFile = $files[$k]['name'];
                    $used_files[] = $this->shapeFile;
                    $this->shapeFlag = true;
                    $this->dbfFile = strstr($files[$k]['name'], ".", true) . '.dbf';

                    if(file_exists($this->processingSubDir . $this->dbfFile))
                    {
                        $this->dbfFlag = true;
                    } else {
                        throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_DBF);
                    }
                    break;
                }
            }

            if (!$this->shapeFile)
            {
                if($j == 0)
                {
                    throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_SHAPE);
                }else {
                    break;
                }
            }

            if (!$this->dbfFile)
            {
                if($j == 0)
                {
                    throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_DBF);
                }else {
                    break;
                }
            }
    
            if ($j == 0)
            {   
                //shp2pgsql commmon
                $this->commonSHP2PSQL();
            } else
            {
                //shp2pgsql custom
                $this->commonSHP2PSQL(self::PGSQL_APPEND);
            }
        }
    }

    /**
     * processTrimbleItems - process Trimble Items
     * @return void
     */
    public function processTrimbleItems()
    {
        //check if main required directories exist
        if (!file_exists($this->userSubDir . 'AgGPS/Data'))
        {
            throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_ARCHIVE);
        }

        //creating the final directory array
        $final_dirs = [];

        //getting client directories
        $client_dirs = FileSystem::directories($this->userSubDir . 'AgGPS/Data');
        
        if (count($client_dirs))
        {
            //J is iteration for client directories
            for ($j = 0; $j < count($client_dirs); $j++)
            {
                $farming_dirs = FileSystem::directories($client_dirs[$j]);
                //if farming directories were found
                if (count($farming_dirs))
                {
                    //K is iteration for farming directories
                    for ($k = 0; $k < count($farming_dirs); $k++)
                    {

                        $plot_dirs = FileSystem::directories($farming_dirs[$k]);

                        //if plot directories were found
                        if (count($plot_dirs))
                        {
                            //L is iteration for plot directories
                            for ($l = 0; $l < count($plot_dirs); $l++)
                            {
                                $final_dirs[] = $plot_dirs[$l] . DIRECTORY_SEPARATOR;
                            }
                        }
                    }
                }
            }
        }
        //create iterations flag
        $iteration = 0;
        $this->shapeFlag = false;
        $this->dbfFlag = false;
        
        //check if any directory with correct structure was found
        if (count($final_dirs))
        {
            for ($j = 0; $j < count($final_dirs); $j++)
            {
                $this->processingSubDir = $final_dirs[$j];

                $files = FileSystem::allFiles($this->processingSubDir);
                $this->shapeFile = false;
                $this->dbfFile = false;

                //check if dbf and shp file exists
                for ($k = 0; $k < count($files); $k++)
                {
                    if ($files[$k] && strtolower($files[$k]->getExtension()) == 'shp')
                    {
                        $this->shapeFile = $files[$k]->getFilename();
                        $this->shapeFlag = true;
                    }
                    if ($files[$k] && strtolower($files[$k]->getExtension()) == 'dbf')
                    {
                        $this->dbfFile = $files[$k]->getFilename();
                        $this->dbfFlag = true;
                    }
                }

                //if shape and dbf files were found we can proceed
                if ($this->shapeFile && $this->dbfFile)
                {

                    if ($iteration == 0)
                    {
                        $output = $this->commonSHP2PSQL();
                    } 
                    else
                    {
                        $output = $this->commonSHP2PSQL(self::PGSQL_APPEND);
                    }

                    if ($output != "CREATE INDEX" && $output != "INSERT 0 1" && $output != "COMMIT")
                    {
                        throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_RUNTIME);
                    }

                    $iteration++;
                }
            }
        } else
        {
            throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_ARCHIVE);
        }
    }

    /**
     * processSateliteItems - process items in GeoScan
     * @return void
     */
    public function processSateliteItems()
    {
        if (file_exists($this->userSubDir . 'AgGPS/Data')) {
            $this->processTrimbleItems();
        }

        if ($this->fileType === File::SHP) {
            $this->processSimpleItems();
        }

        if (in_array($this->fileType, [File::KML, File::KMZ])) {
            $this->processKML();
        }
    }

    /**
     * get projection code
     * @param  string           projection info
     * @return int|bool         return the projection code or false if the shp file doesn't contain projection meta data
     */
    private function getPrjCode($output)
    {
        if (empty($output)) {
            return false;
        }

        $utmZone = Config::get('globals.DEFAULT_UTM_ZONE');
        $defaultProj = Config::get('globals.DEFAULT_DB_CRS');
        $geoPole = Config::get('globals.GEO_POLE');

        $prjUTM = "WGS_1984_UTM_Zone_" . $utmZone . "N";
        $prjUTM_HR = "WGS 84 / UTM zone " . $utmZone . "N";

        if ($geoPole == 'south') {
            $prjUTM = "WGS_1984_UTM_Zone_" . $utmZone . "S";
            $prjUTM_HR = "WGS 84 / UTM zone ".$utmZone."S";            
        }

        $prjGeoGCS = 'GEOGCS';

        if (strpos($output, $prjUTM) !== false || strpos($output, $prjUTM_HR) !== false ) {
            $prjCode = $defaultProj;
            return $prjCode;
        } else {
            if (strpos($output, $prjGeoGCS) !== false) {
                $prjCode = 4326;
                return $prjCode;
            }
        }
        
        return false;
    }

    public function getPolyFromOgrInfoExtent($ogrinfo)
    {
        $pattern = "/Extent: \\((?<xmin>[\\d]+\\.[\\d]+), (?<ymin>[\\d]+\\.[\\d]+)\\) - \\((?<xmax>[\\d]+\\.[\\d]+), (?<ymax>[\\d]+\\.[\\d]+)\\)/";
        preg_match($pattern, $ogrinfo, $extent);

        $ogrInfoOutputText = print_r($ogrinfo, true);

        if($ogrinfo && strpos($ogrInfoOutputText, 'FAILURE') !== false){

            $ogrInfoOutputText = mb_convert_encoding($ogrInfoOutputText, "UTF-8");

            throw new Exception("ogrinfo Error: ".$ogrInfoOutputText, File::ERROR_OGRINFO);
        }

        //coordinates
        $xMin = $extent['xmin'];
        $YMin = $extent['ymin'];
        $xMax = $extent['xmax'];
        $yMax = $extent['ymax'];

        return 'POLYGON((' . $xMin . ' ' . $YMin . ',' . $xMax . ' ' . $YMin . ',' . $xMax . ' ' . $yMax . ',' . $xMin . ' ' . $yMax . ',' . $xMin . ' ' . $YMin . '))';
    }

    /**
     * Get geom by text and crs
     * @param  string $polygon            Polygon in WKT format
     * @param  int $crs                    The projection code
     * @return boolean           
     */
    public function isPolyInExtent($polygon, $crs=32635) {
        $extentPolygon= array(
            $crs => Config::get('globals.EXTENT_WKT_CRS'),
            4326 => Config::get('globals.EXTENT_WKT_CRS_4326'),
        );

        $polygonA = $polygon;
        $polygonB = $extentPolygon[$crs];

        $result = DB::selectOne("SELECT ST_Intersects(ST_GeomFromText('{$polygonA}', {$crs}), ST_GeomFromText('{$polygonB}', $crs)) as in_bulgaria");

        return $result->in_bulgaria;
    }

    /**
     * get projection info from shp file
     * @param  string           $absoluteShpPath the absolute path to shp file
     * @return string           projection info
     */
    private function getPrjInfo($absoluteShpPath)
    {
        $path_parts = pathinfo($absoluteShpPath);
        $command = "ogrinfo -ro -so \"{$absoluteShpPath}\" \"{$path_parts['filename']}\"";

        exec($command, $output);
        $output = implode(' ', $output);

        return $output;
    }


    /**
     * commonSHP2PSQL - insert geoms from shapeFile to DB
     * @param  string $action - type of the action(create or delete)
     * @param  array  $pipes  - array of commands that can be executed
     * @return string      
     */
    public function commonSHP2PSQL($action=null, array $pipes = [])
    {
        //var_export('commonSHP2PSQL \n');
        $defaultPipes = ["PGPASSWORD=" . Config::get('database.connections.' . Config::get('database.default') . '.password') . " psql -w -d {$this->database} -U " . Config::get('database.connections.' . Config::get('database.default') . '.username') . " -p " . Config::get('database.connections.' . Config::get('database.default') . '.port') . " -h " . Config::get('database.connections.' . Config::get('database.default') . '.host') . ""];

        if(strlen(Config::get('globals.PGPASSFILE'))) {
            $defaultPipes = ["PGPASSFILE=" . Config::get('globals.PGPASSFILE') . " psql -w -d {$this->database} -U " . Config::get('database.connections.' . Config::get('database.default') . '.username') . " -p " . Config::get('database.connections.' . Config::get('database.default') . '.port') . " -h " . Config::get('database.connections.' . Config::get('database.default') . '.host') . ""];
        }

        $pipeStr = implode(' | ', array_merge($pipes, $defaultPipes));
        $gisIndexParam = '';
        
        if($action)
        {
            $createTmpSatCmd = $action;
        }
        else if(!Schema::hasTable($this->tmpTable))
        {
            $createTmpSatCmd = self::PGSQL_CREATE;
        }
        else
        {
            $createTmpSatCmd = self::PGSQL_DELETE;
        }

        if ($createTmpSatCmd == self::PGSQL_CREATE || $createTmpSatCmd == self::PGSQL_DELETE)
        {
            $gisIndexParam = '-I';
        }

        //absolute path to shape file
        $absoluteShpPath = $this->processingSubDir . $this->shapeFile;

        //get project info
        $prjInfo = $this->getPrjInfo($absoluteShpPath);

        $crs = $this->getPrjCode($prjInfo);

        if (!$crs)
        {
            $polygon = $this->getPolyFromOgrInfoExtent($prjInfo);
            $isPolyUTM = $this->isPolyInExtent($polygon, Config::get('globals.DEFAULT_DB_CRS'));
            
            if ($isPolyUTM)
            {
                $crs = Config::get('globals.DEFAULT_DB_CRS');
            } elseif ($this->isPolyInExtent($polygon, 4326))
            {
                $crs = 4326;
            } else {
                throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_CRS);
            }
        }
        //update file with srid
        $this->file->crs = $crs;
        $this->file->save();
        
        //try with UTF8
        //$shpEncoding = 'UTF8';
        //$shpEncoding = 'LATIN1';
        $shpEncoding = $this->getEncoding(); 
        $cmdSHP2pgsql = "shp2pgsql -I -t  2D {$gisIndexParam} {$createTmpSatCmd} -W {$shpEncoding} -s {$crs} -g geom \"{$this->processingSubDir}{$this->shapeFile}\" {$this->tmpTable}";

        //var_export($cmdSHP2pgsql);
        exec($cmdSHP2pgsql . " 2>&1", $output);
        $output = implode(' ', $output);
        
        //catch if there is error
        $error = $this->catchError(File::LOADING_FILE, $output);
        
        if($error == File::ERROR_READING_SHAPE_OBJECT || $error == File::ERROR_GEOMETRY_COLLECTION)
        {
            $output = $this->commonOGR2OGR();

            return $output;
        }

        if($error == File::ERROR_INCORRECT_ENCODING_FIELD)
        {
            throw new Exception($output, $error);
        }

        if($error != File::LOADING_FILE_NOW)
        {
            if($error == File::ERROR_INCORRECT_ENCODING)
            {
                //try with CP1251
                $shpEncoding = 'CP1251';
                $cmdSHP2pgsql = "shp2pgsql -I -g geom -t  2D {$gisIndexParam} {$createTmpSatCmd} -W {$shpEncoding} -s {$crs}  \"{$this->processingSubDir}{$this->shapeFile}\" {$this->tmpTable}";

                //var_export($cmdSHP2pgsql);
                exec($cmdSHP2pgsql . " 2>&1", $output);
                $output = implode(' ', $output);

                $error = $this->catchError(File::LOADING_FILE_NOW, $output);
                if($error != File::LOADING_FILE_NOW)
                {
                    throw new Exception($output, $error);
                }
            }
        }

        $cmdSHP2pgsql = $cmdSHP2pgsql ." | ". $pipeStr;

        //var_export($cmdSHP2pgsql);
        $output = exec($cmdSHP2pgsql);

        return $output;
    }

    /**
     * finalActions - final actions that we execute
     * @return void
     */
    public function finalActions()
    {
        if (($this->shapeFile && $this->dbfFile) || ($this->shapeFlag && $this->dbfFlag))
        {
            $this->makeValidGeom();
            
            $this->runStDump();
            
            $this->tmpCRS = DB::table($this->tmpTable)->selectRaw('ST_SRID(geom)')->value('st_srid');

            if ((int) $this->tmpCRS != (int) $this->file->crs)
            {
                throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_CRS);   
            }

            $this->copyProcess();

            //remove the unziped dir
            FileSystem::deleteDirectory($this->userSubDir);
            
            $this->file->status = File::SUCCESSFULLY_TREATED;
            $this->file->save();
        } else
        {
            throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_ARCHIVE);   
        }                
    }   

    /**
     * makeValidGeom - make Valid Geom
     * @return void
     */
    public function makeValidGeom()
    {
        $invalidGeometries = DB::table($this->tmpTable)->where(DB::raw('ST_IsValid(geom)'), false)->count();
        
        if($invalidGeometries)
        {
            $dbVersion = (float) DB::select('SHOW SERVER_VERSION')[0]->server_version;
            if($dbVersion >= 9.3){
                
                DB::statement("ALTER TABLE {$this->tmpTable} ALTER COLUMN geom SET DATA TYPE geometry(Geometry,{$this->file->crs});");
                
                DB::table($this->tmpTable)->where(DB::raw('ST_IsValid(geom)'), false)->update(['geom' => DB::raw('ST_MakeValid(geom)')]);

                $invalidGeometries = DB::table($this->tmpTable)->where(DB::raw('ST_IsValid(geom)'), false)->count();
                if($invalidGeometries)
                {
                    throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_GEOMETRY);   
                }
            }
            else
            {
                throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_GEOMETRY);
            }      
        }
    }
    
    protected function transformColumnCRS() {
        $tablename = $this->tmpTable;
        $tocrs = Config::get('globals.DEFAULT_DB_CRS');
        
        $geomColumnType = DB::table('geometry_columns')
            ->where('f_table_schema', 'public')
            ->where('f_table_name', $tablename)
            ->where('f_geometry_column', 'geom')
            ->value('type');
        
        DB::statement("ALTER TABLE {$tablename} DROP COLUMN IF EXISTS geom_new;");
        DB::statement("SELECT AddGeometryColumn('{$tablename}', 'geom_new', {$tocrs}, '{$geomColumnType}', 2);");
        DB::statement("UPDATE {$tablename} set geom_new = st_transform(geom, {$tocrs});");
        DB::statement("ALTER TABLE {$tablename} DROP COLUMN IF EXISTS geom;");
        DB::statement("ALTER TABLE {$tablename} RENAME COLUMN geom_new TO geom;");
    }
    
    protected function runStDump() {
        
        $dumpTable = $this->tmpTable . '_dump';
        DB::statement("CREATE TABLE {$dumpTable} AS (SELECT *, (ST_DUMP(geom)).geom AS geom_dump FROM {$this->tmpTable})");
        DB::statement("ALTER TABLE {$dumpTable} DROP COLUMN IF EXISTS geom;");
        DB::statement("ALTER TABLE {$dumpTable} RENAME COLUMN geom_dump TO geom;");
        DB::statement("DROP TABLE IF EXISTS {$this->tmpTable}");
        DB::statement("ALTER TABLE IF EXISTS {$dumpTable} RENAME TO {$this->tmpTable}");
    }

    /**
     * searchForExistingColumn - returns the name of the first column from a list that exists in a table
     * @param  String $tableName - the name of the table to search in
     * @param  array $potentialColumnNames - list of column names
     * @return String
     */
    protected function searchForExistingColumn($tableName, $potentialColumnNames)
    {
        $sampleIdColumn = '';

        foreach ($potentialColumnNames as $key => $checkColumnName) {

            $columnNameQuery = DB::table('information_schema.columns')->select('column_name')
            ->where('table_name', $tableName)
            ->where('column_name', 'ilike', '%'.$checkColumnName.'%')
            ->get();

            if (isset($columnNameQuery[0])) {
                $sampleIdColumn = $columnNameQuery[0]->column_name;
                break;
            }
        }

        return $sampleIdColumn;

    }

    /**
     * checkGeometryTypes - checks if the geometries in the temporary table are compatiable with the layer type
     * of the processed file
     * @param  array $allowedGeometryTypes - list of allowed geometry types for the loading
     */
    protected function checkGeometryTypes($allowedGeometryTypes)
    {
        $results = DB::table($this->tmpTable)
                    ->select("*")
                    ->whereNotIn(DB::raw('st_geometrytype(geom)'), $allowedGeometryTypes)
                    ->count();

        // the table has unsupported geometry types
        if (!empty($allowedGeometryTypes) && $results > 0 ) {
            throw new Exception(__METHOD__." Line:".__LINE__, File::ERROR_INVALID_SHAPE);
        }
    }
}

?>
