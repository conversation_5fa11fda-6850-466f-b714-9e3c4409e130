<?php

namespace App\Classes\LoadData;

use App\Models\File;
use App\Models\Plot;
use App\Models\PlotCrop;
use App\Models\Crop;
use Illuminate\Support\Facades\File as FileSystem;
use DB;
use Config;
use \Exception;
use Schema;

/**
 * @link http://www.technofarm.bg/
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class SoilTracksProcessingClass extends AbstractLoadDataClass {

    public $tmpTable = "";

    /**
     * init - init function
     * @param  mixed $file
     * @return void
     */
    public function __construct(File $file) {

        parent::__construct($file);

        $this->tmpTable = "tmp_soil_tracks_".$file->id;
    }
    
    /**
     * startProcessing
     * @return void
     */
    public function startProcessing() 
    {
        $this->mainProcessing();    
    }

    /**
     * finalActions - final actions that we execute
     * @return void
     */
    public function finalActions()
    {
        if($this->file->status == File::LOADING_FILE_NOW) 
        {
            $this->copyProcess();    
        }
    }    

    /**
     * copyProcess executes the process to copy the data
     * @return void
     */
    public function copyProcess()
    {
        $tmpTable = "tmp_soil_tracks_" . $this->ID;
        $potentialSampleIdColumns = array(
            'sampleid',
            'sample_id',
            'objectid'
            );
        $sampleIdColumn = $this->searchForExistingColumn($tmpTable, $potentialSampleIdColumns);

        if ($sampleIdColumn != '') {
            DB::statement("UPDATE su_satellite_soil_points ssp set track = st.geom 
                            FROM (SELECT * FROM " . $tmpTable . ") as st 
                            where ssp.sopr_id = " . $this->file->sopr_id ." and ssp.sample_id = st." . $sampleIdColumn);

            $this->file->status = File::SUCCESSFULLY_TREATED;
            $this->file->save();
        } else {
            $this->file->status = File::ERROR_INVALID_DBF;
            $this->file->save();
        }


        //Drop Tmp table
        Schema::dropIfExists($tmpTable);

        //remove the unziped dir
        FileSystem::deleteDirectory($this->userSubDir);
    }
}

?>