<?php

namespace App\Classes;

use GuzzleHttp\Client;
use GuzzleHttp\Psr7;
use App\Exceptions\NotFoundException;
use App\Models\Plot;


class Plot3D
{
	public function __construct($url)
    {
		$this->url = $url;

        $this->client = new Client([
            'base_uri' => $url,
            'timeout' => 300.0,
        ]);
    }

	public function downloadBinFile($geojson){
		$response =  $this->client->request('POST', "/api/generateBin", [
			'form_params' => ['geojson' => $geojson]
		]);

		$content = $response->getBody()->getContents();
		
        return $content;
	}
}
