<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\PestsDisease;


use App\Classes\Meteo\MeteoBlue;
use App\Entities\Local\CalculatedRisk;
use App\Entities\Local\Condition;
use App\Entities\Local\PestDisease;
use App\Entities\Local\Phenophase;
use App\Entities\Local\PhenophaseRepository;
use App\Entities\Local\Plot;
use App\Entities\Local\PlotCrop;
use App\Entities\Local\PlotCropRepository;
use App\Entities\Local\RiskGroup;
use App\Exceptions\PestDisease\GeneralException;
use Doctrine\Common\Util\Debug;
use Doctrine\ORM\EntityManager;
use Config;
use Auth;

class RiskCalculator
{
    /** @var MeteoBlue */
    private $meteoService;

    public function __construct(MeteoBlue $meteoService)
    {
        $this->meteoService = $meteoService;
    }

    public function calculate(Plot $plot, \DateTime $date)
    {
        $em = $this->getLocalEntityManager();
        /** @var Phenophase $phenophase */
        $phenophase = $this->findPhenophaseForDate($plot, $date);
        if(!$phenophase) {
            throw new GeneralException('Phenophase for plot couldn`t be determined.');
        }
        $pestsDiseases = $phenophase->getPestsDiseases();

        $calculatedRiskRepository = $em->getRepository(CalculatedRisk::class);

        foreach ($pestsDiseases as $pestsDisease) {
            $calculatedRisk = $calculatedRiskRepository->findOneBy([
                'date' => $date,
                'plot' => $plot,
                'pestDisease' => $pestsDisease,
                'phenophase' => $phenophase
            ]);

            if (!$calculatedRisk) {
                $calculatedRisk = new CalculatedRisk();
            }
            $calculatedRisk->setDate($date);
            $calculatedRisk->setPlot($plot);
            $calculatedRisk->setPestDisease($pestsDisease);
            $calculatedRisk->setPhenophase($phenophase);
            $calculatedRisk->setRiskLevel($this->checkPestDiseaseRisks($plot, $date, $pestsDisease));

            $em->persist($calculatedRisk);
        }
        $em->flush();

        return true;
    }

    private function findPhenophaseForDate(Plot $plot, \DateTime $date)
    {
        $em = $this->getLocalEntityManager();

        /** @var PlotCropRepository $plotCropRepository */
        $plotCropRepository = $em->getRepository(PlotCrop::class);

        /** @var PlotCrop $plotCrop */
        $plotCrop = $plotCropRepository->findCurrentByPlot($plot);
        if (!$plotCrop || !$plotCrop->getCrop() || !$plotCrop->getCrop()->getGddCollection() || $plotCrop->getCrop()->getGddCollection()->getSlug() == 'none') {
            throw new GeneralException('There is no crop for plot.');
        }


        $gddValue = $this->calculateGddValue($plot, $plotCrop, $date);
        $gddCollection = $plotCrop->getCrop()->getGddCollection();

        /** @var PhenophaseRepository $phenophaseRepository */
        $phenophaseRepository = $em->getRepository(Phenophase::class);
        $phenophase = $phenophaseRepository->findPhaseByCropAndGdd($gddCollection, $gddValue);

        return $phenophase;
    }

    private function calculateGddValue(Plot $plot, PlotCrop $plotCrop, \DateTime $date)
    {
        $organizationId = $plot->getFarm()->getOrganization()->getId();

        //Get Location of the Meteo point by gid
        $meteoLocation = \App\Models\Plot::findMeteoLocation($plot->getGid());

        $gddArray = $this->meteoService->gddByCoordinates(
            $meteoLocation['longitude'],
            $meteoLocation['latitude'],
            $plotCrop->getFromDate()->format('Y-m-d'),
            $plotCrop->getCrop()->getGddCollection()->getSlug(),
            $plotCrop->getFromDate()->format('Y-m-d'),
            $date->format('Y-m-d'),
            $organizationId);

        $gddValues = array_column($gddArray, 'GDDIndex', 'time');
        $gddOffset = $this->calculateGddOffset($plotCrop, $gddValues, $date);
        $gddValue = array_key_exists($date->format('Y-m-d'), $gddValues) ? $gddValues[$date->format('Y-m-d')] + $gddOffset : 0;

        return $gddValue;
    }

    private function calculateGddOffset(PlotCrop $plotCrop, $gddValues, \DateTime $date)
    {
        $offset = 0;
        $em = $this->getLocalEntityManager();

        /** @var PhenophaseRepository $phenophaseRepository */
        $phenophaseRepository = $em->getRepository(Phenophase::class);

        if (!empty($plotCrop->getEmergenceDate()) && $plotCrop->getEmergenceDate() <= $date) {
            $emergencePhase = $phenophaseRepository->findFirstPhaseForCrop($plotCrop->getCrop()->getGddCollection());
            $emergenceOffset = $emergencePhase->getMaxGdd() - $gddValues[$plotCrop->getEmergenceDate()->format('Y-m-d')];
            $offset += $emergenceOffset;
        }
        if (!empty($plotCrop->getPhenophaseDate()) && $plotCrop->getPhenophaseDate() <= $date) {
            /** @var Phenophase $phenophase */
            $phenophase = $phenophaseRepository->findOneBy(['frontendArrayIndex' => $plotCrop->getFrontendArrayIndex()]);
            $phenophaseOffset = $phenophase->getMaxGdd() - $gddValues[$plotCrop->getPhenophaseDate()->format('Y-m-d')];
            $offset += $phenophaseOffset;
        }

        return $offset;
    }

    private function checkPestDiseaseRisks(Plot $plot, \DateTime $date, PestDisease $pestDisease)
    {
        $riskLevel = 0;
        $riskGroups = $pestDisease->getRiskGroups();
        foreach ($riskGroups as $riskGroup) {
            if ($this->checkRiskGroup($plot, $date, $riskGroup) && $riskLevel < $riskGroup->getRiskLevel()) {
                $riskLevel = $riskGroup->getRiskLevel();
            }
        }

        return $riskLevel;
    }

    private function checkRiskGroup(Plot $plot, \DateTime $date, RiskGroup $riskGroup)
    {
        $risks = $riskGroup->getRisks();

        foreach ($risks as $risk) {
            $condition = $risk->getCondition();
            if (!$this->checkCondition($plot, $date, $condition)) {
                return false;
            }
        }

        return true;
    }

    private function checkCondition(Plot $plot, \DateTime $date, Condition $condition)
    {
        $ruleClass = $condition->getRuleClass();
        /** @var AbstractRule $rule */
        $rule = new $ruleClass;
        $rule->setMinValue($condition->getMinValue());
        $rule->setAvgValue($condition->getAvgValue());
        $rule->setMaxValue($condition->getMaxValue());
        $rule->setType($condition->getConditionType());
        $rule->setValueType($condition->getValueType());

        return $rule->check($plot, $date);
    }

    /**
     * @return EntityManager
     */
    private function getLocalEntityManager()
    {
        $country = Config::get('globals.MACHINE');
        $connection = strtoupper($country);
        $managerRegistry = app('registry');

        return $managerRegistry->getManager($connection);
    }
}
