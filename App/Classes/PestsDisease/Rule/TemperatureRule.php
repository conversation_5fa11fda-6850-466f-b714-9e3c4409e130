<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\PestsDisease\Rule;


use App\Classes\PestsDisease\ICheckable;
use App\Entities\Local\Plot;
use App;

class TemperatureRule extends AbstractMeteoRule implements ICheckable
{
    public function check(Plot $plot, \DateTime $date)
    {
        $type = $this->getType();
        $valueType = $this->getValueType();

        $meteoData = $this->getMeteoData($plot, $date,
            'air_temperature_' . ($valueType == 'average' ? 'avg' : $valueType));

        switch ($type) {
            case 'range':
                return ($meteoData > $this->getMinValue() && $meteoData <= $this->getMaxValue());
            case 'value':
                switch ($valueType) {
                    case 'min':
                        return ($meteoData === $this->getMinValue());
                    case 'max':
                        return ($meteoData === $this->getMaxValue());
                    case 'average':
                    case 'daily_average':
                        return ($meteoData === $this->getAvgValue());
                }
        }

        return false;
    }
}
