<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\PestsDisease\Rule;


use App\Classes\MeteoService;
use App\Classes\PestsDisease\AbstractRule;
use App\Entities\Local\Plot;
use App\Helpers\Helper;

abstract class AbstractMeteoRule extends AbstractRule
{
    protected function getMeteoData(Plot $plot, \DateTime $date, $sensor)
    {
        /** @var MeteoService $meteoService */
        $meteoService = Helper::resolveObject(MeteoService::class);
        $plotModel = \App\Models\Plot::where('gid', $plot->getGid())->first();
        $meteoData = $meteoService->getDailyDataByPlotAndDate($date, $plotModel, $sensor);

        return $meteoData;
    }
}
