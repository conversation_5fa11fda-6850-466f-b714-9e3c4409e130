<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\PestsDisease;



abstract class AbstractRule implements ICheckable
{
    protected $minValue;
    protected $maxValue;
    protected $avgValue;
    protected $type;
    protected $valueType;

    /**
     * @return mixed
     */
    public function getMinValue()
    {
        return $this->minValue;
    }

    /**
     * @param mixed $minValue
     * @return AbstractRule
     */
    public function setMinValue($minValue)
    {
        $this->minValue = $minValue;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getMaxValue()
    {
        return $this->maxValue;
    }

    /**
     * @param mixed $maxValue
     * @return AbstractRule
     */
    public function setMaxValue($maxValue)
    {
        $this->maxValue = $maxValue;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getAvgValue()
    {
        return $this->avgValue;
    }

    /**
     * @param mixed $avgValue
     * @return AbstractRule
     */
    public function setAvgValue($avgValue)
    {
        $this->avgValue = $avgValue;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param mixed $type
     * @return AbstractRule
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getValueType()
    {
        return $this->valueType;
    }

    /**
     * @param mixed $valueType
     * @return AbstractRule
     */
    public function setValueType($valueType)
    {
        $this->valueType = $valueType;
        return $this;
    }
}
