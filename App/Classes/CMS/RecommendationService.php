<?php

namespace App\Classes\CMS;

use App\Models\Order;
use Auth;
use Config;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;

class RecommendationService  extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * @param array $headerParams
     * @return mixed
     */
    public function getRecommendations(array $headerParams)
    {
        $organizations = Auth::user()->organizations()->where('active', true)->whereNotNull('identity_number')->get();
        $organizationIdentityNumbers = array_column($organizations->toArray(), 'identity_number');
        if (isset($headerParams['filter']['customer_identification'])) {
            $identityNumber = $headerParams['filter']['customer_identification'];
            $headerParams['filter']['customer_identification'] = json_encode([$identityNumber]);
        } else {
            $headerParams['filter']['customer_identification'] = json_encode($organizationIdentityNumbers);
        }

        return $this->getRecommendationData($headerParams);
    }

    /**
     * @param string $plotUuid
     * @param array $headerParams
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationByPlot(string $plotUuid, array $headerParams)
    {
        $url = "api/recommendations/plot/{$plotUuid}";

        try {
            $response = $this->client->request('GET', $url, [
                'headers' => $this->getHeaders(),
                'query' => $headerParams
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param int $subscriptionPackageFieldId
     *
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationLabResults(int $subscriptionPackageFieldId)
    {
        $url = "api/recommendations/{$subscriptionPackageFieldId}/lab-results";

        try {
            $response = $this->client->request('GET', $url, [
                'headers' => $this->getHeaders()
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param array $params
     *
     * @throws GuzzleException
     * @return mixed
     */
    public function storeRecommendation(array $params) {
        $url = "api/recommendations/store";

        try {
            $response = $this->client->request('POST', $url, [
                'headers' => $this->getHeaders(),
                'json' => $params
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param array $headerParams
     * @param string $status
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationsByLastChosenOrganization(array $headerParams, $status = 'Delivered')
    {
        $headerParams['filter']['customer_identification'] = json_encode([
            Auth::user()->lastChosenOrganization->identity_number
        ]);

        if (isset($headerParams['filter']['year'])) {
            $headerParams['filter']['year'] = (((int)$headerParams['filter']['year']) - 1) . '/' . $headerParams['filter']['year'];
        }
        $headerParams['filter']['status'] =json_encode([ $status]);

        return $this->getRecommendationData($headerParams);
    }

    /**
     * @param array $headerParams
     * @return array|mixed
     * @throws GuzzleException
     */
    public function getRecommendationsSoilContractList(array $headerParams)
    {
        $orderIds = Order::select(['id', 'uuid'])
            ->where('year', $headerParams['farm_year'])
            ->where('type', 'soil')
            ->where('organization_id', Auth::user()->lastChosenOrganization->id)
            ->get()->toArray();

        if (!count($orderIds)) {
            return [];
        }

        $orderIds = array_column($orderIds, 'uuid');
        $headerParams['filter']['plot_uuid'] = $headerParams['plot_uuid'];
        $headerParams['filter']['year'] = $headerParams['farm_year'];
        $headerParams['filter']['order_uuid'] = json_encode($orderIds);
        unset($headerParams['plot_uuid'], $headerParams['farm_year']);

        try {

            $response = $this->client->request('GET', 'api/recommendations/soil-analysis/contracts', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param int $id
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationById(int $id)
    {
        try {

            $response = $this->client->request('GET', 'api/recommendations/' . $id, [
                'headers' => $this->getHeaders(),
                'query' => []
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param int $id
     * @param string|null $type
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationCommentsRecommendationById(int $id, string $type = null)
    {
        try {
            $params = [];

            if ($type !== null) {
                $params = ['type' => $type];
            }
            $response = $this->client->request('GET', 'api/recommendations/' . $id . '/comments', [
                'headers' => $this->getHeaders(),
                'query' => $params
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param int $recommendationId
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationVra(int $recommendationId)
    {
        try {
            $params = [];

            $response = $this->client->request('GET', 'api/recommendations/' . $recommendationId . '/vra-orders', [
                'headers' => $this->getHeaders(),
                'query' => $params
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param int $id
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationLabElementsResultsById(int $id)
    {
        try {
            $params = [];

            $response = $this->client->request('GET', 'api/recommendations/' . $id . '/results', [
                'headers' => $this->getHeaders(),
                'query' => $params
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getCalculations(int $subscriptionPackageFieldId, array $params)
    {
        try {
            $response = $this->client->request('GET', 'api/recommendations/' . $subscriptionPackageFieldId . '/calculations', [
                'headers' => $this->getHeaders(),
                'query' => $params
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        $content = json_decode($response->getBody()->getContents(), true);

        return $content;
    }

    /**
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationModels()
    {
        try {
            $response = $this->client->request('GET', 'api/recommendations/models', [
                'headers' => $this->getHeaders()
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function update(int $id, array $params)
    {
        try {
            $response = $this->client->request('PUT', 'api/recommendations/' . $id . '/update', [
                'headers' => $this->getHeaders(),
                'json' => $params
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param int $id
     * @param string $state
     * @return mixed
     * @throws GuzzleException
     */
    public function updateState(int $id, string $state)
    {
        try {
            $response = $this->client->request('PUT', 'api/recommendations/' . $id . '/update-state', [
                'headers' => $this->getHeaders(),
                'json' => ['state' => $state]
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param $headerParams
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationData($headerParams)
    {
        try {

            $response = $this->client->request('GET', 'api/recommendations', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param $headerParams
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationReport($headerParams)
    {
        try {

            $response = $this->client->request('GET', 'api/recommendations/report', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param $headerParams
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationStatusAnalysisData($headerParams)
    {
        try {

            $response = $this->client->request('GET', 'api/recommendations/status-analysis/echarts', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param $headerParams
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationsCrops()
    {
        try {

            $response = $this->client->request('GET', 'api/recommendations/crops', [
                'headers' => $this->getHeaders(),
                'query' => []
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param array $params
     *
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationsSoilAnalyzesData(array $params = [])
    {
        try {
            $response = $this->client->request('GET', 'api/recommendations/soil-analyzes-data', [
                'headers' => $this->getHeaders(),
                'query' => $params
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param array $headerParams
     * @param string $status
     * @return mixed
     * @throws GuzzleException
     */
    public function getRecommendationsStatusAnalysisDataForCharts(array $headerParams, string $status)
    {
        $headerParams['filter']['customer_identification'] = json_encode([
            Auth::user()->lastChosenOrganization->identity_number
        ]);

        if (isset($headerParams['filter']['year'])) {
            $headerParams['filter']['year'] = (((int)$headerParams['filter']['year']) - 1) . '/' . $headerParams['filter']['year'];
        }

        if (isset($headerParams['filter']['plot_uuid'])) {
            $headerParams['filter']['plot_uuid'] = json_encode($headerParams['filter']['plot_uuid']);
        }

        $headerParams['filter']['status'] = json_encode([$status]);

        return $this->getRecommendationStatusAnalysisData($headerParams);
    }

    /**
     * @param array $params
     * @return mixed
     * @throws GuzzleException
     */
    public function printRecommendation(array $params)
    {
        try {
            $response = $this->client->request('POST', 'api/recommendations/print', [
                'headers' => $this->getHeaders(),
                'json' => $params
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }
}
