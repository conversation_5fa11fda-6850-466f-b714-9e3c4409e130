<?php

namespace App\Classes\CMS;

use App\Models\SoilPoints;
use Auth;
use Config;
use DB;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;

class AnalysisService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * @param $contractType
     * @param $returnCmsData
     * @return mixed|void
     * @throws GuzzleException
     */
    public function savePackageGridPoints($contractType, $returnCmsData)
    {
        $arrPackagePlot = $this->getGridPoints($contractType, $returnCmsData);

        if (!$arrPackagePlot) {
            return;
        }

        $arrSoprIds = array_map(function ($item) {
            return $item['sopr_id'];
        }, $arrPackagePlot);
        $arrSoilPoints = SoilPoints::getBySoprId($arrSoprIds)->get(['uuid', 'gid', 'sopr_id', 'sample_id'])->toArray();

        $arrResult = array_map(function ($soilPoint) use ($arrPackagePlot) {

            $foundData = array_filter($arrPackagePlot, function ($packagePlot) use ($soilPoint) {
                return $packagePlot['sopr_id'] == $soilPoint['sopr_id'];
            });
            $foundData = reset($foundData);
            return [
                'point_uuid' => $soilPoint['uuid'],
                'sample_id' => $soilPoint['sample_id'],
                'plot_uuid' => $foundData['plot_uuid'],
                'package_id' => $foundData['package_id'],
                'package_type' => $foundData['package_type'],
                'grid_type' => $foundData['grid_type'],
            ];
        }, $arrSoilPoints);

        try {
            $response = $this->client->request('POST', 'api/analysis/package-grid-points/', [
                'headers' => $this->getHeaders(),
                'json' => $arrResult
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $response = json_decode($response->getBody()->getContents(), true);
        return $response;
    }

    public function saveBarcodes($barcodesData)
    {
        try {
            $this->client->request('POST', 'api/analysis/package-grid-points/save-barcodes', [
                'headers' => $this->getHeaders(),
                'json' => $barcodesData
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }
    }

    /**
     * @param array $arrPlotUuids [uuid1, uuid2, ...]
     */
    public function updatePlotPointsState(array $arrPlotUuids)
    {
        try {
            $this->client->request('POST', 'api/analysis/package-grid-points/update-plot-points-state', [
                'headers' => $this->getHeaders(),
                'json' => $arrPlotUuids
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }
    }

    public function getPlotsCount(string $state)
    {
        $params = ['state' => $state];
        try {
            $response = $this->client->request('GET', 'api/analysis/lab-elements/plots-count', [
                'headers' => $this->getHeaders(),
                'query' => $params
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param array $params
     *
     * @return mixed
     * @throws GuzzleException
     */
    public function getSoilMapElements(array $params = [])
    {
        try {
            $response = $this->client->request('GET', 'api/analysis/lab-elements/soil-map', [
                'headers' => $this->getHeaders(),
                'query' => $params
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @return mixed
     * @throws GuzzleException
     */
    public function getElementsUnit()
    {
        try {
            $response = $this->client->request('GET', 'api/analysis/lab-elements/unit', [
                'headers' => $this->getHeaders(),
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function updateLabElementsResultStates($data)
    {
        try {
            $this->client->request('POST', 'api/analysis/lab-elements/results/update-states', [
                'headers' => $this->getHeaders(),
                'json' => $data
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }
    }

    public function getMetaElementsGroupsList()
    {
        try {
            $response = $this->client->request('GET', '/api/analysis/meta-elements-groups/list', [
                'headers' => $this->getHeaders()
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getLabElementsResults($params)
    {
        try {
            $response = $this->client->request('GET', '/api/analysis/lab-elements/results', [
                'headers' => $this->getHeaders(),
                'query' => $params
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param $queryParams
     *
     * @return mixed
     * @throws GuzzleException
     */
    public function getBarcodes($queryParams)
    {
        try {
            $response = $this->client->request('GET', 'api/analysis/package-grid-points/barcodes', [
                'headers' => $this->getHeaders(),
                'query' => $queryParams
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param $queryParams
     *
     * @return mixed
     * @throws GuzzleException
     */
    public function getLabNumbers($queryParams)
    {
        try {
            $response = $this->client->request('GET', 'api/analysis/package-grid-points/lab-numbers', [
                'headers' => $this->getHeaders(),
                'query' => $queryParams
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    private function getGridPoints($contractType, $returnCmsData)
    {
        $arrForGridPoints = [];
        foreach ($returnCmsData as $item) {
            foreach ($item['params'] as $value) {
                $arrForGridPoints[] = [
                    'sopr_id' => $value['sopr_id'],
                    "package_id" => $item['package_id'],
                    "plot_uuid" => $value['plot_uuid'],
                    "package_type" => $contractType,
                    "grid_type" => $value['grid_type'],
                ];
            }
        }

        return $arrForGridPoints;
    }

}
