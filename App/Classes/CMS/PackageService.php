<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\CMS;

use Auth;
use Config;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Request;

class PackageService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function list()
    {
        try {
            $response = $this->client->request('GET', 'api/packages', [
                'headers' => $this->getHeaders(),
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $data = json_decode($response->getBody()->getContents(), true);

        return $data;
    }

    /**
     * @param $organizations
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function countPackagesByCustomerIdentifications($organizations)
    {
        $organizationIdentityNumbers = array_column(json_decode(json_encode($organizations), true), 'identity_number');

        $response = [];
        $response['countAvailablePackages'] = $this->cntAvailablePackages($organizationIdentityNumbers);
        $response['countAllPackages'] = $this->cntAllPackages($organizationIdentityNumbers);

        return $response;
    }

    public function stationAdded(int $contractId) {
        try {
            $response = $this->client->request('POST', 'api/contracts/subscription-package/contract/'. $contractId.'/station-added', [
                'headers' => $this->getHeaders(),
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param $organizationIdentityNumbers
     * @return float|int
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function cntAllPackages($organizationIdentityNumbers)
    {
        $query = [
            'customer_identification' => json_encode($organizationIdentityNumbers),
            'has_packages' => 'true',
        ];

        try {
            $subscriptionPackage = $this->client->request('GET', 'api/contracts/subscription-package/count', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);

            $serviceResponse = $this->client->request('GET', 'api/contracts/service-package/count', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        $subsResult = json_decode($subscriptionPackage->getBody()->getContents(), true);
        $serviceResult = json_decode($serviceResponse->getBody()->getContents(), true);

        return array_sum([$subsResult['countPackages'], $serviceResult['countPackages']]);

    }

    /**
     * @param $organizationIdentityNumbers
     * @return float|int
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function cntAvailablePackages($organizationIdentityNumbers)
    {
        $query = [
            'customer_identification' => json_encode($organizationIdentityNumbers),
            'has_packages' => 'true',
            'type_count' => 'available',
        ];

        try {
            $subscriptionPackage = $this->client->request('GET', 'api/contracts/subscription-package/count', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);

            $serviceResponse = $this->client->request('GET', 'api/contracts/service-package/count', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        $subsResult = json_decode($subscriptionPackage->getBody()->getContents(), true);
        $serviceResult = json_decode($serviceResponse->getBody()->getContents(), true);

        return array_sum([$subsResult['countPackages'], $serviceResult['countPackages']]);
    }
}