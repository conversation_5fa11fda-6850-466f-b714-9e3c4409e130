<?php

namespace App\Classes\CMS;

use App\Models\Plot;
use Auth;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class AnalysisReportService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * @throws GuzzleException
     */
    public function getAnalysesReport(array $queryParams, bool $forExport = false)
    {
        if (isset($queryParams['filter']['plot_ids']) && count(json_decode($queryParams['filter']['plot_ids'], true))) {
            $plotGids = json_decode($queryParams['filter']['plot_ids'], true);
            $queryParams['filter']['plot_uuids'] = Plot::whereIn('gid', $plotGids)->get()->pluck('uuid');
            $queryParams['filter']['plot_uuids'] = json_encode($queryParams['filter']['plot_uuids']);
            unset($queryParams['filter']['plot_ids']);
        }

        $params = $this->getAnalysesReportFromCMS($queryParams);
        $plotWithSoilPointsAndFarms = Plot::getPlotsWithSoilGridPointsAndFarms(Auth::user()->lastChosenOrganization->id);
        $plotWithSoilPointsAndFarms = array_column($plotWithSoilPointsAndFarms, null, 'plot_uuid');

        $params['rows'] = array_map(function ($row) use ($plotWithSoilPointsAndFarms, $forExport) {
            $row['farm_name'] = $plotWithSoilPointsAndFarms[$row['plot_uuid']]['farm_name'];
            $row['plot_name'] = $plotWithSoilPointsAndFarms[$row['plot_uuid']]['plot_name'];

            if ($forExport) {
                $row['longitude'] = (float)$plotWithSoilPointsAndFarms[$row['plot_uuid']]['soil_grid_points_coordinates'][$row['sample_id']]['longitude'];
                $row['latitude'] = (float)$plotWithSoilPointsAndFarms[$row['plot_uuid']]['soil_grid_points_coordinates'][$row['sample_id']]['latitude'];
            }

            return $row;
        }, $params['rows']);

        $columns = Config('reports.ANALYSES_COLUMNS');
        foreach ($params['header'] as $element) {
            array_push($columns, ['name' => $element, 'label' => $element, 'width' => '100px', 'visible' => true]);
        }

        if ($forExport) {
            $columns = array_merge($columns, [
                ['name' => 'longitude', 'label' => 'Lab ID', 'width' => '120px', 'visible' => true],
                ['name' => 'latitude', 'label' => 'Lab ID', 'width' => '120px', 'visible' => true]
            ]);
        }

        $params['header'] = $columns;

        return $params;
    }

    /**
     * @throws GuzzleException
     */
    public function exportXLSAnalysesReport($queryParams): string
    {
        $reportData = $this->getAnalysesReport($queryParams, true);

        if ($reportData['total'] === 0) {
            throw new Exception("No data", 404);
        }

        $tmpfilePath = sys_get_temp_dir() . '/' . uniqid('analyses_report_', true) . '.xls';
        $columns = array_column($reportData['header'], 'name');
        $columns = $this->translateColumns($columns);
        $this->generateXls($reportData['rows'], $columns, $tmpfilePath);

        if (!is_file($tmpfilePath)) {
            throw new Exception("File '{$tmpfilePath}' does not exist!");
        }

        return $tmpfilePath;
    }

    /**
     * @throws GuzzleException
     */
    private function getAnalysesReportFromCMS($queryParams)
    {
        $response = $this->client->request('GET', 'api/analysis/lab-elements/report', [
            'headers' => $this->getHeaders(),
            'query' => $queryParams
        ]);

        return json_decode($response->getBody()->getContents(), true);
    }

    private function translateColumns(array $columns): array
    {
        $columnsMap = [];
        foreach ($columns as $value) {
            $columnsMap[$value] = trans('reportAnalysis' . '.' . $value);
        }

        return $columnsMap;
    }

    private function getRowData(array $row, array $columnsMap): array
    {
        $rowData = [];

        foreach ($columnsMap as $key => $v) {
            if (isset($row[$key]) && strlen($row[$key]) > 0) {
                if ($key === 'package_name') {
                    $rowData[$key] = $row[$key] . ' ' . $row['period'];
                    continue;
                }

                $rowData[$key] = $row[$key];
            } elseif (isset($row['elements']) && array_key_exists($key, $row['elements'])) {
                $rowData[$key] = $row['elements'][$key];
            } else {
                $rowData[$key] = '';
            }
        }

        return $rowData;
    }

    private function generateXls(array $reportData, array $columnsMap, string $tmpFilePath): void
    {
        $sheetName = trans('reportAnalysis' . '.sheet_title');
        $pathParts = pathinfo($tmpFilePath);

        Excel::create($pathParts['filename'], function ($excel) use ($reportData, $columnsMap, $sheetName) {
            $excel->sheet($sheetName, function ($sheet) use ($reportData, $columnsMap) {
                $sheet->setAutoSize(true);
                $currentRow = 1;

                // Table head
                $sheet->row($currentRow, $columnsMap);
                $sheet->row($currentRow, function ($row) {
                    $row->setFontWeight('bold');
                });
                $currentRow++;

                foreach ($reportData as $row) {
                    $rowData = $this->getRowData((array)$row, $columnsMap);
                    $sheet->row($currentRow, $rowData);
                    $currentRow++;
                }
            });

        })->save($pathParts['extension'], $pathParts['dirname']);
    }
}