<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\CMS;

use App\Models\Plot;
use Auth;
use Config;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Psr\Http\Message\ResponseInterface;

class ContractService extends AbstractCmsService
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function list($requestData)
    {
        $query = [
            'filter' => [
                'customerIdentification' => [
                    'type' => 'eq',
                    'x' => $requestData['organization_identity_number']
                ]
            ]
        ];


        if (isset($requestData['page']) && !empty($requestData['page'])) {
            $query['page'] = $requestData['page'];
        }

        if (isset($requestData['limit']) && !empty($requestData['limit'])) {
            $query['limit'] = (int)$requestData['limit'];
        }

        if (isset($requestData['sort']) && !empty($requestData['sort'])) {
            $query['sort'] = $requestData['sort'];
        }

        try {
            $response = $this->client->request('GET', 'api/contracts', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $data = json_decode($response->getBody()->getContents(), true);

        return $data;
    }

    /**
     * Contracts for an organization
     *
     * @param array $headerParams
     * @return mixed
     */
    public function getContractsShortDataByIdentityNumber(array $headerParams)
    {
        return $this->getContractShortData($headerParams);
    }

    /**
     * Receiving contracts and packages for multiple organizations and joining each organization to a concrete contract
     *
     * @param array $headerParams
     * @return mixed
     *
     */
    public function getContractsShortDataByOrganizations(array $headerParams)
    {
        $organizations = Auth::user()->organizations()->where('active', true)->whereNotNull('identity_number')->get()->toArray();

        if (isset($headerParams['filter']['customer_identification'])) {
            $identityNumber = $headerParams['filter']['customer_identification'];
            $organizations = (Auth::user()->organizations()->get()->filter(function ($value) use ($identityNumber) {
                return $value->identity_number == $identityNumber;
            }))->toArray();
        }

        $organizationIdentityNumbers = array_column(json_decode(json_encode($organizations), true), 'identity_number');
        $headerParams['filter']['customer_identification'] = json_encode($organizationIdentityNumbers);

        $contracts = $this->getContractShortData($headerParams);
        foreach ($organizations as $organization) {
            foreach ($contracts['items'] as $key => $contract) {
                if ($organization['identity_number'] === $contract['customer_identification']) {
                    usort($contracts['items'][$key]['packages'], function ($item1, $item2) {
                        return $item2['slugShort'] !== null;
                    });
                    $contracts['items'][$key]['organization'] = $organization;
                }
            }
        }

        return $contracts;
    }

    /**
     * Count all contracts by organizations
     *
     * @param $organizations
     * @param $headerParams array
     * @return string
     * @throws GuzzleException
     */
    public function countContracts($organizations, array $headerParams = [])
    {
        $organizationIdentityNumbers = array_column(json_decode(json_encode($organizations), true), 'identity_number');
        $headerParams['filter']['customer_identification'] = json_encode($organizationIdentityNumbers);

        try {
            $response = $this->client->request('GET', 'api/contracts/count', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    // TODO: GPS-1540 move it in PackageService
    public function addFieldsToSubscription($plots, $subscriptionId)
    {
        $subscriptionPackageFields = [];
        foreach ($plots as $plot) {
            $subscriptionPackageFields[] = [
                "plotUuid" => $plot->uuid,
                "area" => $plot->area
            ];
        }

        $params = [
            "subscriptionPackageFields" => $subscriptionPackageFields
        ];

        try {
            $response = $this->client->request('POST', 'api/contracts/subscription-package/' . $subscriptionId, [
                'headers' => $this->getHeaders(),
                'json' => $params
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $data = json_decode($response->getBody()->getContents(), true);

        return $data;

    }

    // TODO: GPS-1540 move it in PackageService this method is the same as addFieldsToSubscription()
    public function addFieldsToService($plots, $serviceId)
    {
        $servicePackageFields = [];
        foreach ($plots as $plot) {
            $servicePackageFields[] = [
                "plotUuid" => $plot->uuid,
                "area" => $plot->area
            ];
        }

        $params = [
            "servicePackageFields" => $servicePackageFields
        ];

        try {
            $response = $this->client->request('POST', 'api/contracts/service-package/' . $serviceId, [
                'headers' => $this->getHeaders(),
                'json' => $params
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $data = json_decode($response->getBody()->getContents(), true);

        return $data;
    }

    // TODO: GPS-1540 move it in PackageService
    public function listPackagesForCard($requestData, $orgIdentityNumber)
    {
        $query = [];

        if (isset($requestData['page']) && !empty($requestData['page'])) {
            $query['page'] = (int)$requestData['page'];
        }

        if (isset($requestData['limit']) && !empty($requestData['limit'])) {
            $query['limit'] = (int)$requestData['limit'];
        }

        try {
            $response = $this->client->request('GET', 'api/contracts/' . $orgIdentityNumber . '/packages', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $data = json_decode($response->getBody()->getContents(), true);

        return $data;
    }

    // TODO:: GPS-1540 rename method and move it in PackageService
    /**
     * @param string $type
     * @param array $queryParams
     * @param int|null $contractId
     * @return mixed
     * @throws GuzzleException
     */
    public function getCmsDataForPackagesByContractIdAndFilter(string $type, array $queryParams, int $contractId = null)
    {
        $url = 'api/contracts/' . $type . '/filter';

        if ($contractId) {
            $url = 'api/contracts/' . $type . '/contract/' . $contractId;
        }

        try {
            $response = $this->client->request('GET', $url, [
                'headers' => $this->getHeaders(),
                'query' => $queryParams
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $data = json_decode($response->getBody()->getContents(), true);
        return $data;
    }

    public function getAllOrdersIdByContractId($contractId, $queryParams)
    {
        $subscriptionResponse = $this->getCmsDataForPackagesByContractIdAndFilter('subscription-package-field/orders', $queryParams, $contractId);
        $serviceResponse = $this->getCmsDataForPackagesByContractIdAndFilter('service-package-field/orders', $queryParams, $contractId);

        $response = array_merge($subscriptionResponse, $serviceResponse);
        $ordersId = array_column($response, 'orderUuid');
        $ordersId = array_unique($ordersId);
        $ordersId = array_values($ordersId);
        return $ordersId;
    }

    public function getAllPlotsIdByContractId($contractId, $queryParams)
    {
        $subscriptionResponse = $this->getCmsDataForPackagesByContractIdAndFilter('subscription-package-field/plots', $queryParams, $contractId);
        $serviceResponse = $this->getCmsDataForPackagesByContractIdAndFilter('service-package-field/plots', $queryParams, $contractId);

        $response = array_merge($subscriptionResponse, $serviceResponse);
        $plotUuid = array_column($response, 'plotUuid');
        $plotUuid = array_unique($plotUuid);
        $plotUuid = array_values($plotUuid);
        return $plotUuid;
    }

    // TODO: GPS-1540 move it in PackageService
    public function getAllServiceOrSubscriptionPackagesByContractId($contractId, $queryParams)
    {
        $subscriptionResponse = $this->getCmsDataForPackagesByContractIdAndFilter('subscription-package', $queryParams, $contractId);
        $serviceResponse = $this->getCmsDataForPackagesByContractIdAndFilter('service-package', $queryParams, $contractId);

        return array_merge(($subscriptionResponse ?? []), ($serviceResponse ?? []));
    }

    // TODO: GPS-1540 move it in PackageFieldService

    /**
     * @param string $contractType
     * @param array $packageFieldData
     * @return mixed|ResponseInterface
     * @throws GuzzleException
     */
    public function savePackageFields(string $contractType, array $packageFieldData)
    {
        try {
            $response = $this->client->request('POST', 'api/contracts/' . $contractType . '-package-field/', [
                'headers' => $this->getHeaders(),
                'json' => $packageFieldData
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        $response = json_decode($response->getBody()->getContents(), true);
        return $response;
    }

    // TODO: GPS-1540 move it in PackageFieldService

    /**
     * @param string $contractType
     * @param array $plotsData
     * @return mixed
     * @throws GuzzleException
     */
    public function changePackageFieldState(string $contractType, array $plotsData)
    {
        try {
            $response = $this->client->request('POST', 'api/contracts/' . $contractType . '-package-field/change_state', [
                'headers' => $this->getHeaders(),
                'json' => $plotsData
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    // TODO: GPS-1540 move it in PackageFieldService

    /**
     * @param string $contractType
     * @param array $data ['plots' => [number, number, ...], 'orders' => [number, number, ...]]
     */
    public function updateSubscriptionPackageFieldState(string $contractType, array $data)
    {
        try {
            $this->client->request('POST', 'api/contracts/' . $contractType . '-package-field/synced-to-mobile', [
                'headers' => $this->getHeaders(),
                'json' => $data
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }
    }

    // TODO:: GPS-1540 this method is not used anywhere
    public function getContractById(string $id)
    {
        try {
            $response = $this->client->request('GET', 'api/contracts/' . $id, [
                'headers' => $this->getHeaders(),
            ]);
        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getContractShortData($headerParams)
    {
        try {
            $response = $this->client->request('GET', 'api/contracts/short', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getCountAmountsForContracts($headerParams)
    {
        try {
            $response = $this->client->request('GET', 'api/contracts/count/amount', [
                'headers' => $this->getHeaders(),
                'query' => $headerParams
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function hasPackage($contractId, $status, $slugShort)
    {
        $query = ['status' => $status, 'slugShort' => $slugShort];
        try {
            $response = $this->client->request('GET', 'api/contracts/' . $contractId . '/has-package', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getStationsAmountByOrganizations($customerIdentifications, $withPagination, $offset, $limit)
    {
        $query = [
            "customer_identifications" => $customerIdentifications,
            "withPagination" => $withPagination,
            "offset" => $offset,
            "limit" => $limit
        ];

        try {
            $response = $this->client->request('GET', 'api/contracts/stations/amount', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getStationContractsByOrganizations($customerIdentifications)
    {
        $query = [
            "customer_identifications" => $customerIdentifications
        ];


        try {
            $response = $this->client->request('GET', 'api/stations/contracts', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getCardsData($organizationIdentityNumbers) {
        $params['main']['filter'] = [
            'customer_identification' => $organizationIdentityNumbers
        ];

        // Add-plots card params
        $params['add_plots_card']['filter'] = [
            'packages_state' => '["In progress","Waiting for plots"]',
            'ratio_contract_area_fields_area' => '1',
            'contain_fields' => '1',
            'status' => '["Active"]'
        ];

        // Manage weather stations card params
        $params['manage_weather_stations_card']['filter'] = [
            'has_station' => 1,
            'contain_fields' => 0
        ];

        try {
            $response = $this->client->request('GET', 'api/contracts/cards', [
                'headers' => $this->getHeaders(),
                'query' => $params
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getIntegrationCardList($packageSlug, $offset, $limit, $customerIdentifications)
    {
        $query = [
            "package_slug" => $packageSlug,
            "offset" => $offset,
            "limit" => $limit,
            "customer_identifications" => $customerIdentifications,
        ];

        try {
            $response = $this->client->request('GET', 'api/contracts/integration/card-list', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getOverlapContracts($contractId, $status, $hasStation = true)
    {
        $query = ['status' => $status, 'has_station' => $hasStation];
        try {
            $response = $this->client->request('GET', 'api/contracts/' . $contractId . '/get-overlap', [
                'headers' => $this->getHeaders(),
                'query' => $query
            ]);

        } catch (ClientException $exception) {
            throw $exception;
        }

        return json_decode($response->getBody()->getContents(), true);

    }

    public function getPlotsOverlapBySubscriptionContract(array $plotUuids, int $contractId, int $organizationId, string $lang = 'en'): array
    {
        $queryParams['filter'] = [
            'is_full_sampling' => true
        ];

        $contractPlotUuids = $this->getCmsDataForPackagesByContractIdAndFilter('subscription-package-field/plots', $queryParams, $contractId);
        $contractPlotUuids = array_column($contractPlotUuids, 'plotUuid');
        $contractPlotUuids = array_values(array_unique($contractPlotUuids));

        $contractOrderUuids = $this->getCmsDataForPackagesByContractIdAndFilter('subscription-package-field/orders', $queryParams, $contractId);
        $contractOrderUuids = array_column($contractOrderUuids, 'orderUuid');
        $contractOrderUuids = array_values(array_unique($contractOrderUuids));

        $plots = Plot::getPlotsOverlapByContract($plotUuids, $contractPlotUuids, $contractOrderUuids, $organizationId, $lang);

        return $plots;
    }
}
