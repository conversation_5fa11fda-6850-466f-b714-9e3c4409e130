<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\Echarts;
use App\Classes\Echarts\Meteo\EChartsMeteoDataFormatter;


class EChartsFormatter
{
    public static function format($data)
    {
        $chartData = new EChartsMeteoDataFormatter();
        $chartData->setGrid($data['grid']);
        $chartData->setXAxis($data['xAxis']);
        $chartData->setYAxis($data['yAxis']);
        $chartData->setSeries($data['series']);
        $chartData->setLegend($data['legend']);
        $chartData->setDataZoom($data['dataZoom']);

        return $chartData->toJson();
    }
}
