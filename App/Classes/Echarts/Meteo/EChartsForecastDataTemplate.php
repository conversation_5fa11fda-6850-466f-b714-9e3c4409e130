<?php
namespace App\Classes\Echarts\Meteo;


class EChartsForecastDataTemplate
{

    private $days;
    private $temperature;
    private $precipitation;
    private $precipitationProbability;
    private $pictocode;
    private $windspeed;
    private $spraying;

    public function __construct(
        array $days,
        array $temperature,
        array $pictocode,
        array $precipitation,
        array $precipitationProbability,
        array $windspeed,
        array $spraying
    ) {   
        $this->days = $days;
        $this->temperature = $temperature;
        $this->pictocode = $pictocode;
        $this->precipitation = $precipitation;
        $this->precipitationProbability = $precipitationProbability;
        $this->windspeed = $windspeed;
        $this->spraying = $spraying;
    }

    /**
     * @return array
     */
    public function getDays(): array
    {
        return $this->days;
    }

    /**
     * @param array $days
     * @return EChartsForecastDataTemplate
     */
    public function setDays(array $days)
    {
        $this->days = $days;
        return $this;
    }

    /**
     * @return array
     */
    public function getTemperature(): array
    {
        return $this->temperature;
    }

    /**
     * @param array $temperature
     * @return EChartsForecastDataTemplate
     */
    public function setTemperature(array $temperature)
    {
        $this->temperature = $temperature;
        return $this;
    }

    /**
     * @return array
     */
    public function getPictocode(): array
    {
        return $this->pictocode;
    }

    /**
     * @param array $pictocode
     * @return EChartsForecastDataTemplate
     */
    public function setPictocode(array $pictocode)
    {
        $this->pictocode = $pictocode;
        return $this;
    }

    /**
     * @return array
     */
    public function getPrecipitation(): array
    {
        return $this->precipitation;
    }

    /**
     * @param array $precipitation
     * @return EChartsForecastDataTemplate
     */
    public function setPrecipitation(array $precipitation)
    {
        $this->precipitation = $precipitation;
        return $this;
    }

    /**
     * @return array
     */
    public function getPrecipitationProbability(): array
    {
        return $this->precipitationProbability;
    }

    /**
     * @param array $precipitationProbability
     * @return EChartsForecastDataTemplate
     */
    public function setPrecipitationProbability(array $precipitationProbability)
    {
        $this->precipitationProbability = $precipitationProbability;
        return $this;
    }

    /**
     * @return array
     */
    public function getWindspeed(): array
    {
        return $this->windspeed;
    }

    /**
     * @param array $windspeed
     * @return EChartsForecastDataTemplate
     */
    public function setWindspeed(array $windspeed)
    {
        $this->windspeed = $windspeed;
        return $this;
    }

    /**
     * @return array
     */
    public function getSpraying(): array
    {
        return $this->spraying;
    }

    /**
     * @param array $spraying
     * @return EChartsForecastDataTemplate
     */
    public function setSpraying(array $spraying)
    {
        $this->spraying = $spraying;
        return $this;
    }

    /**
     * @return array
     */
    public function getEchartsData(): array
    {
        return  [
            'temperature' => [
                'title' => [
                    'text' => trans('echarts.forecast'),
                    'top' => 0,
                    'left' => 22,
                    'padding' => 0,
                    'textStyle' => [
                        'color' => '#353535',
                        'fontWeight' => 600,
                        'fontSize' => 12,
                        'lineHeight' => 18,
                    ],
                ],
        
                'grid' => [
                    'top' => 34,
                    'right' => 22,
                    'bottom' => 0,
                    'left' => 22,
                    'containLabel' => true,
                ],
        
                'tooltip'=> [
                    'trigger' => 'axis',
                    'backgroundColor' => '#12141D',
                    'borderWidth' => 0,
                    'extraCssText' => 'border-radius: 4px',
                    'textStyle' => [
                        'color' => '#fff',
                        'fontSize' => 10,
                        'textShadowColor' => 'transparent',
                    ],
                    'padding' => [6, 12]
                ],

                'xAxis' => [
                    [
                        'type' => 'time',
                        'minInterval' => 3600 * 1 * 1000,
                        'maxInterval' => 3600 * 24 * 1000,
                        'axisLine' => [
                            'show' => false,
                        ],
                        'splitArea' => [
                            'show' => false,
                        ],
                        'splitLine' => [
                            'show' => true,
                            'lineStyle' => [
                                'width' => 1,
                                'type' => 'dashed',
                                'color' => '#D9D9D9'
                            ],
                        ],
                        'axisLabel' => [
                            'show' => false,
                        ],
                        'axisTick' => [
                            'show' => false,
                        ],
                        'minorTick' => [
                            'show' => false,
                            'splitNumber' => 4
                        ],
                        'minorSplitLine' => [
                            'show' => true,
                            'lineStyle' => [
                                'width' => 1,
                                'type' => 'dashed',
                                'color' => '#D9D9D9',
                            ],
                        ],
                        'boundaryGap' => ['-0.75%', '-0.85%'],
                        'zlevel' => 1
                    ],
                    [
                        'type' => 'category',
                        'position' => 'bottom',
                        'offset' => 0,
                        'axisLine' => [
                            'show' => false,
                        ],
                        'axisTick' => [
                            'show' => false,
                        ],
                        'axisLabel' => [
                            'show' => true,
                            'color' => '#777',
                            'fontSize' => 10,
                            'lineHeight' => 14,
                            'rich' => [
                                'day' => [
                                    'fontSize' => 10,
                                    'fontWeight' => 'bold',
                                ],
                            ],
                        ],
                        'axisPointer' => [
                            'show' => false
                        ],
                        'splitArea' => [
                            'show' => true,
                            'areaStyle' => [
                                'color' => ['#FFFFFF', '#F3F3F3'],
                            ],
                        ],
                        'zlevel' => 0,
                        'data' => $this->days, // dataMap.days
                    ],
                ],
                'yAxis' => [
                    [
                        'type' => 'value',
                        'boundaryGap' => [0, '100%'],
                        'splitLine' => [
                            'show' => false,
                        ],
                        'axisLabel' => [
                            'show' => false
                        ],
                    ],
                    [
                        'type' => 'value',
                        'boundaryGap' => [0, '100%'],
                        'splitLine' => [
                            'show' => false,
                        ],
                        'axisLabel' => [ 'show' => false ],
                    ],
                ],
                'series' => [
                    [
                        'name' => 'Precipitation',
                        'type' => 'bar',
                        'yAxisIndex' => 0,
                        'smooth' => true,
                        'symbol' => 'none',
                        'itemStyle' => [
                            'borderRadius' => [5, 5, 0, 0],
                            'color' => [ 
                                'type' => 'linear',
                                'x' => 0,
                                'y' => 0,
                                'x2' => 0,
                                'y2' => 1,
                                'colorStops' => [
                                    [
                                        'offset' => 0,
                                        'color' => '#0569FF',
                                    ],
                                    [
                                        'offset' => 0.7,
                                        'color' => 'rgba(43, 166, 255, 0.4)',
                                    ],
                                    [
                                        'offset' => 1,
                                        'color' => 'rgba(74, 179, 255, 0)',
                                    ],
                                ],
                            ],
                            'opacity' => 0.7,
                        ],
                        'data' => $this->precipitation // dataMap.precipitation
                    ],
                    [
                        'name' => 'T',
                        'type' => 'line',
                        'yAxisIndex' => 1,
                        'stack' => 'temp',
                        'smooth' => true,
                        'lineStyle' => [
                            'color' => '#FF2608',
                            'width' => 1,
                        ],
                        'showSymbol' => false,
                        'symbol' => 'circle',
                        'symbolSize' => 4,
                        'itemStyle' => [
                            'color' => '#FF2608',
                        ],
                        'data' => $this->temperature // dataMap.temperature,
                    ],
                    [
                        'type' => 'custom',
                        'name' => 'Weather',
                        'yAxisIndex' => 1,
                        'zlevel' => 2,
                        'data' => $this->pictocode // dataMap.pictocode,
                    ],
                    [
                        'type' => 'custom',
                        'name' => 'Precipitation probability',
                        'zlevel' => 2,
                        'data' => $this->precipitationProbability // dataMap.precipitationProbability,
                    ]
                ],
            ],
        
        
            'wind' => [
                'title' => [
                    'text' => trans('echarts.wind'),
                    'top' => 0,
                    'left' => 22,
                    'padding' => 0,
                    'textStyle' => [
                        'color' => '#353535',
                        'fontWeight' => 600,
                        'fontSize' => 12,
                        'lineHeight' => 18,
                    ],
                ],
                'grid' => [
                    'top' => 34,
                    'right' => 22,
                    'bottom' => 0,
                    'left' => 22,
                    'containLabel' => true,
                ],
                'tooltip' => [
                    'trigger' => 'axis',
                    'backgroundColor' => '#12141D',
                    'borderWidth' => 0,
                    'extraCssText' => 'border-radius: 4px',
                    'textStyle' => [
                        'color' => '#fff',
                        'fontSize' => 10,
                        'textShadowColor' => 'transparent',
                    ],
                    'padding' => [6, 12],
                ],
                'xAxis' => [
                    [
                        'type' => 'time',
                        'minInterval' => 3600 * 1 * 1000,
                        'maxInterval' => 3600 * 24 * 1000,
                        'axisLine' => [
                            'show' => false,
                        ],
                        'splitArea' => [
                            'show' => false,
                        ],
                        'splitLine' => [
                            'show' => true,
                            'lineStyle' => [
                                'width' => 1,
                                'type' => 'dashed',
                                'color' => '#D9D9D9'
                            ],
                        ],
                        'axisLabel' => [
                            'show' => false,
                        ],
                        'axisTick' => [
                            'show' => false,
                        ],
                        'minorTick' => [
                             'show' => false,
                             'splitNumber' => 4
                        ],
                        'minorSplitLine' => [
                            'show' => true,
                            'lineStyle' => [
                                'width' => 1,
                                'type' => 'dashed',
                                'color' => '#D9D9D9',
                            ],
                        ],
                        'zlevel' => 1
                    ],
                    [
                        'type' => 'category',
                        'position' => 'bottom',
                        'offset' => 0,
                        'axisLine' => [
                            'show' => false,
                        ],
                        'axisTick' => [
                            'show' => false,
                        ],
                        'axisLabel' => [
                            'color' => '#777',
                            'fontSize' => 10,
                            'lineHeight' => 14,
                            'rich' => [
                                'day' => [
                                    'fontSize' => 10,
                                    'fontWeight' => 'bold',
                                ],
                            ],
                        ],
                        'axisPointer' => [
                            'show' => false
                        ],
                        'splitArea' => [
                            'show' => true,
                            'areaStyle' => [
                                'color' => ['#FFFFFF', '#F3F3F3']
                            ],
                        ],
                        'zlevel' => 0,
                        'data' => $this->days // dataMap.days,
                    ],
                ],
                'yAxis' => [
                    'type' => 'value',
                    'boundaryGap' => [0, '100%'],
                    'splitLine' => [
                        'show' => false,
                    ],
                    'axisLabel' => [
                        'show' => false
                    ],
                ],
                'series' => [
                    [
                        'name' => 'Wind speed',
                        'type' => 'line',
                        'smooth' => true,
                        'showSymbol' => false,
                        'symbol' => 'circle',
                        'symbolSize' => 4,
                        'data' => $this->windspeed, // dataMap.data,
                        'lineStyle' => [
                            'color' => '#ADA192',
                            'width' => 1,
                        ],
                        'itemStyle' => [
                            'color' => '#ADA192'
                        ],
                    ],
                ],
            ],
        
        
            'spraying' => [
                'title' => [
                    'text' => trans('echarts.agroSprayingForecast'),
                    'top' => 0,
                    'left' => 22,
                    'padding' => 0,
                    'textStyle' => [
                        'color' => '#353535',
                        'fontWeight' => 600,
                        'fontSize' => 12,
                        'lineHeight' => 18,
                    ],
                ],
                'grid' => [
                    'top' => 34,
                    'right' => 22,
                    'bottom' => 0,
                    'left' => 22,
                    'containLabel' => true,
                ],
                'tooltip' => [
                    'trigger' => 'axis',
                    'backgroundColor' => '#12141D',
                    'borderWidth' => 0,
                    'extraCssText' => 'border-radius: 4px',
                    'textStyle' => [
                        'color' => '#fff',
                        'fontSize' => 10,
                        'textShadowColor' => 'transparent',
                    ],
                    'padding' => [6, 12]
                ],
                'xAxis' => [
                    [
                        'type' => 'time',
                        'minInterval' => 3600 * 1 * 1000,
                        'maxInterval' => 3600 * 24 * 1000,
                        'axisLine' => [
                            'show' => false,
                        ],
                        'splitArea' => [
                            'show' => false,
                        ],
                        'splitLine' => [
                            'show' => true,
                            'lineStyle' => [
                                'width' => 1,
                                'type' => 'dashed',
                                'color' => '#D9D9D9'
                            ],
                        ],
                        'axisLabel' => [
                            'show' => false,
                        ],
                        'axisTick' => [
                            'show' => false,
                        ],
                        'minorTick' => [
                            'show' => false,
                            'splitNumber' => 4
                        ],
                        'minorSplitLine' => [
                            'show' => true,
                            'lineStyle' => [
                                'width' => 1,
                                'type' => 'dashed',
                                'color' => '#D9D9D9',
                            ],
                        ],
                        'boundaryGap' => ['-1.2%', '-1.25%'],
                        'zlevel' => 1
                    ],
                    [
                        'type' => 'category',
                        'position' => 'bottom',
                        'offset' => 0,
                        'axisLine' => [
                            'show' => false,
                        ],
                        'axisTick' => [
                            'show' => false,
                        ],
                        'axisLabel' => [
                            'color' => '#777',
                            'fontSize' => 10,
                            'lineHeight' => 14,
                            'rich' => [
                                'day' => [
                                    'fontSize' => 10,
                                    'fontWeight' => 'bold',
                                ],
                            ],
                        ],
                        'axisPointer' => [
                            'show' => false
                        ],
                        'splitArea' => [
                            'show' => true,
                            'areaStyle' => [
                                'color' => ['#FFFFFF', '#F3F3F3'],
                            ],
                        ],
                        'zlevel' => 0,
                        'data' => $this->days // dataMap.days,
                    ],
                ],
                'yAxis' => [
                    'type' => 'value',
                    'boundaryGap' => [0, '100%'],
                    'splitLine' => [
                        'show' => false,
                    ],
                    'axisLabel' => [ 'show' => false ],
                    'max' => 30,
                    'min' => -10,
                ],
                'series' => [
                    [
                        'name' => 'High risk',
                        'type' => 'bar',
                        'stack' => 'spraying',
                        'barCategoryGap' => 0,
                        'itemStyle' => [
                            'color' => '#DC0748',
                            'opacity' => 0.7,
                        ],
                        'emphasis' => [
                            'itemStyle' => [
                                'opacity' => 1,
                            ],
                        ],
                        'data' =>  $this->spraying['highRisk'] // dataMap.data,
                    ],
                    [
                        'name' => 'Medium risk',
                        'type' => 'bar',
                        'stack' => 'spraying',
                        'barCategoryGap' => 0,
                        'itemStyle' => [
                            'color' => '#FFD600',
                            'opacity' => 0.7,
                        ],
                        'emphasis' => [
                            'itemStyle' => [
                                'opacity' => 1,
                            ],
                        ],
                        'data' => $this->spraying['mediumRisk'], // dataMap.data,
                    ],
                    [
                        'name' => 'Low risk',
                        'type' => 'bar',
                        'stack' => 'spraying',
                        'barCategoryGap' => 0,
                        'itemStyle' => [
                            'color' => '#B0C90A',
                            'opacity' => 0.7,
                        ],
                        'emphasis' => [
                            'itemStyle' => [
                                'opacity' => 1,
                            ],
                        ],
                        'data' => $this->spraying['lowRisk'] // dataMap.data,
                    ],
                ],
            ],
        ];
    }
}
