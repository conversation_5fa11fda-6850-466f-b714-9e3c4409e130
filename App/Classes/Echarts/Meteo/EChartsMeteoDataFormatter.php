<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Classes\Echarts\Meteo;
use App\Classes\Echarts\IEChartsDataFormatter;
use JsonSerializable;

class EChartsMeteoDataFormatter implements IEChartsDataFormatter, JsonSerializable
{
    private $title;
    private $grid;
    private $xAxis;
    private $yAxis;
    private $series;
    private $tooltip;
    private $legend;
    private $dataZoom;

    /**
     * @return mixed
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param mixed $title
     * @return EChartsMeteoDataFormatter
     */
    public function setTitle($title)
    {
        $this->title = $title;
        return $this;
    }
    /**
     * @return mixed
     */
    public function getXAxis()
    {
        return $this->xAxis;
    }

    /**
     * @param mixed $xAxis
     * @return EChartsMeteoDataFormatter
     */
    public function setXAxi<PERSON>($xAxis)
    {
        $this->xAxis = $xAxis;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getYAxis()
    {
        return $this->yAxis;
    }

    /**
     * @param mixed $yAxis
     * @return EChartsMeteoDataFormatter
     */
    public function setYAxis($yAxis)
    {
        $this->yAxis = $yAxis;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getSeries()
    {
        return $this->series;
    }

    /**
     * @param mixed $series
     * @return EChartsMeteoDataFormatter
     */
    public function setSeries($series)
    {
        $this->series = $series;
        return $this;
    }

     /**
     * @return mixed
     */
    public function getTooltip()
    {
        return $this->tooltip;
    }

    /**
     * @param mixed $tooltip
     * @return EChartsMeteoDataFormatter
     */
    public function setTooltip($tooltip)
    {
        $this->tooltip = $tooltip;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getGrid()
    {
        return $this->grid;
    }

    /**
     * @param mixed $series
     * @return EChartsMeteoDataFormatter
     */
    public function setGrid($grid)
    {
        $this->grid = $grid;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getLegend()
    {
        return $this->legend;
    }

    /**
     * @param mixed $legend
     * @return EChartsMeteoDataFormatter
     */
    public function setLegend($legend)
    {
        $this->legend = $legend;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getDataZoom()
    {
        return $this->dataZoom;
    }

    /**
     * @param mixed $dataZoom
     * @return EChartsMeteoDataFormatter
     */
    public function setDataZoom($dataZoom)
    {
        $this->dataZoom = $dataZoom;
        return $this;
    }

    /**
     * @param mixed $arrContent
     * @param mixed $configSensorCodes
     * @return ECharts Grid
     */
    public function createGrid($arrContent)
    {
        $gridConfig = config('echarts.stations.grid');

        if(!isset($arrContent['groupWithData'])){
            $arrContent['grid'] = [];
            return $arrContent;
        }

        $topStart = $gridConfig['topStart'];
        $arrLegendData = $arrContent['legend']['data'];
        $itemsPerRow = $arrContent['legend']['itemsPerRow'];
        $countLegendData = count($arrLegendData);
        
        $top = intval(ceil($countLegendData/$itemsPerRow))*$topStart;

        $arrGridIndex = array_keys($arrContent['groupWithData']);   

        foreach ($arrGridIndex as $key => $value) {
            
            if($key == 0){

                $arrContent['grid'][] = [
                    'left' => $gridConfig['left'], 
                    'right' => $gridConfig['right'], 
                    'top' => $top.'px', 
                    'height' => $gridConfig['height']
                ];    

                continue;    
            }

            $top = $top + $gridConfig['topStep'];
            
            $arrContent['grid'][] = [
                'left' => $gridConfig['left'], 
                'right' => $gridConfig['right'], 
                'top' => $top.'px', 
                'height' => $gridConfig['height']
            ];        
        }

        return $arrContent;
    }

    /**
     * @param mixed $arrContent
     * @param mixed $configSensorCodes
     * @return ECharts Grid
     */
    public function createDataZoom($arrContent)
    {
        if(!isset($arrContent['groupWithData'])){
            $arrContent['dataZoom'] = [];
            return $arrContent;
        }

        $groupedSensors = array_keys($arrContent['groupWithData']);  

        $xAxisIndex = [];
        foreach ($groupedSensors as $key => $value) {
            $xAxisIndex[] = $key;
        }

        $dataZoomConfig = config('echarts.stations.dataZoom');
        $dataZoomConfig = array_map(function($value) use ($xAxisIndex) {

            $value['xAxisIndex'] = $xAxisIndex;

            return $value;
        }, $dataZoomConfig);

        $arrContent['dataZoom'] = $dataZoomConfig;

        return $arrContent;
    }

    /**
     * @param mixed $arrContent
     * @param mixed $axisData
     * @return ECharts xAxis
     */
    public function createXAxis($arrContent, $axisData)
    {
        if(!isset($arrContent['groupWithData'])){
            $arrContent['xAxis'] = [];
            return $arrContent;
        }

        $arrXAxis = array_map(function($value) {
            return isset($value['xAxis']) ? $value['xAxis'] : [];
        }, $axisData);

        $arrXAxis = array_filter($arrXAxis, function($value) {
            return count($value) > 0;
        });

        $arrResult = array_map(function($value) use ($arrContent) {

            $value['data'] = $arrContent['xAxisData'];
            return $value;
        }, $arrXAxis);

        $arrxAxis = array_values($arrResult);

        // Build temporary array for array_unique
        $аррТmp = array();
        foreach($arrxAxis as $k => $v)
            $аррТmp[$k] = $v['gridIndex'];

        // Find duplicates in temporary array
        $аррТmp = array_unique($аррТmp);

        // Remove the duplicates from original array
        foreach($arrxAxis as $k => $v)
        {
            if (!array_key_exists($k, $аррТmp))
                unset($arrxAxis[$k]);
        }

        $arrContent['xAxis'] = array_values($arrxAxis);

        unset($arrContent['xAxisData']);

        return $arrContent;
    }

    /**
     * @param mixed $arrContent
     * @param mixed $configSensorCodes
     * @return ECharts yAxis
     */
    public function createYAxis($arrContent, $axisData)
    {
        if(!isset($arrContent['groupWithData'])){
            $arrContent['yAxis'] = [];
            return $arrContent;
        }

        $arrResult = array_map(function($value) {
            return isset($value['yAxis']) ? $value['yAxis'] : [];
        }, $axisData);

        $arrResult = array_values($arrResult);

        $arrResult = array_filter($arrResult, function($value) {
            return count($value) > 0;
        });

        $arrResult = array_values($arrResult);

        $arrContent['yAxis'] = $arrResult;

        return $arrContent;
    }    

    public function toJson()
    {
        $properties = $this->getProperties();
        $object = new \stdClass();
        foreach ($properties as $name => $value) {
            $object->$name = $value;
        }
        return json_encode($object);
    }

    public function mapSensors($sensors, $sensorCodesMappingArray, $property = 'codes')
    {
        $sensorCodes = [];
        if(in_array("all", $sensors)){

            $arrNewKey = array_map(function ($key, $value) use($property) {
                return [$key . '_' . $value['default'] => $value[$property]];
            }, array_keys($sensorCodesMappingArray), $sensorCodesMappingArray);

            foreach ($arrNewKey as $key => $value) {
                $sensorCodes[array_keys($value)[0]] = array_values($value)[0];
            }

            return $sensorCodes;
        }
        
        foreach ($sensors as $sensor) {
            $sensorName = substr($sensor, 0, strrpos($sensor, '_'));
            if (array_key_exists($sensorName, $sensorCodesMappingArray) && !empty($sensorCodesMappingArray[$sensorName])) {
                $sensorValue = $sensorCodesMappingArray[$sensorName];
                $sensorCodes[$sensor] = $sensorValue[$property];
            }
        }

        return $sensorCodes;
    }

    public function setAxisIndexes($arrContent)
    {
        $echartOptions = config('echarts.stations.options');

        $arrChanged = [];
        $gridIndex = 0;

        foreach ($arrContent['groupWithData'] as $group => $value) {

            $foundSensors = array_filter($arrContent['series'], function($value) use ($group) {
                return $value['group'] == $group;
            });

            //Process nodes
            if(strpos($group, 'node') !== false) {

                $sensorCounter = 0;
                foreach ($foundSensors as $key => $arrSensor) {        

                    $arrOption = [];
                    $arrOption = $this->dynamicOptions($gridIndex, $arrSensor, $key, $foundSensors);

                    $arrChanged[$key] = $arrOption;

                    if($sensorCounter != 0){
                        unset($arrChanged[$key]['yAxis']);
                    }

                    $sensorCounter = $sensorCounter + 1;
                }    

                $gridIndex = $gridIndex + 1;
                continue;
            }

            //Process not nodes
            foreach ($foundSensors as $key => $arrSensor) {
                
                $sensorName = substr($key, 0, strrpos($key, '_'));

                $arrOption = $echartOptions[$sensorName];

                if(isset($arrOption['xAxis'])){
                    $arrOption['xAxis']['gridIndex'] = $gridIndex;
                }

                if(isset($arrOption['yAxis'])){
                    $arrOption['yAxis']['gridIndex'] = $gridIndex;
                }

                if(isset($arrOption['serie'])){
                    $arrOption['serie']['xAxisIndex'] = $gridIndex;
                }

                $arrChanged[$key] = $arrOption;
            }

            $gridIndex = $gridIndex + 1;
        }

        return $arrChanged;
    }

    public function setSeriesAxisIndexes($arrContent, $axisData)
    {
        if(!isset($arrContent['series'])){
            return $arrContent;
        }   

        //Set series - xAxisIndex and yAxisIndex
        $arrChangedSeries = [];
        foreach ($arrContent['series'] as $key => $arrSerie) {

            if(!isset($axisData[$key]['serie'])){
                continue;
            }

            $correctSerie = $axisData[$key]['serie'];

            $xAxisIndex = $correctSerie['xAxisIndex'];

            $foundYAxis = array_filter($arrContent['yAxis'], function($value) use ($xAxisIndex) {
                return $value['gridIndex'] == $xAxisIndex;
            });

            //Set xAxisIndex
            $arrSerie['xAxisIndex'] = $xAxisIndex;

            //Update the color of itemStyle
            $arrSerie['itemStyle'] = $correctSerie['itemStyle'];

            $arrFoundKeys = array_keys($foundYAxis);

            if(count($arrFoundKeys) > 1){

                $group = $arrSerie['group'];

                $foundSeries = array_filter($arrContent['series'], function($value) use ($group) {
                    return $value['group'] == $group;
                });

                $arrFoundSeries = array_values($foundSeries);

                $foundCurrentSerie = array_filter($arrFoundSeries, function($value) use ($arrSerie) {
                    return $value['name'] == $arrSerie['name'];
                });

                $currentKey = current(array_keys($foundCurrentSerie));
                $yAxisIndex = $arrFoundKeys[$currentKey];

                //Set yAxisIndex
                $arrSerie['yAxisIndex'] = $yAxisIndex;
                $arrChangedSeries[] = $arrSerie;
                continue;
            }

            $yAxisIndex = reset($arrFoundKeys);

            //Set yAxisIndex
            $arrSerie['yAxisIndex'] = $yAxisIndex;

            $arrChangedSeries[] = $arrSerie;
        }   

        $arrContent['series'] = $arrChangedSeries;

        return $arrContent;
    }

    public function setCommonData($mappedData, $sensorCodesMappingArray)
    {
        $echartOptions = config('echarts.stations.options');

        $mappedData['series'] = [];
        $legend = config('echarts.stations.legend');
        $legend['data'] = [];

        $arrGrouped = [];

        foreach ($mappedData['seriesData'] as $key => $data) {

            // avg, min, max ...
            $dataType = substr($key, -3);
            $dataType = ($dataType === 'min' || $dataType === 'max') ? ' '.$dataType : '';

            $sensorName = substr($key, 0, strrpos($key, '_'));

            if(!isset($sensorCodesMappingArray[$sensorName]) || !$data){
                continue;
            }

            $arrSensor = $sensorCodesMappingArray[$sensorName];

            $baseGroupName = $sensorCodesMappingArray[$sensorName]['group'];

            $arrSerie = $echartOptions[$sensorName]['serie'];
            //translate
            $baseName = trans('general.'.$sensorCodesMappingArray[$sensorName]['name']);

            //Process nodes
            if(isset($data['nodes'])){            
                $sensorNodes = $data['nodes'];

                foreach ($sensorNodes as $keyNode => $arrChannels) {

                    $groupName = $baseGroupName . '_node_' . $keyNode;                    

                    foreach ($arrChannels as $keyChannel => $arrChannelData) {

                        $arrSerie['data'] = $arrChannelData;

                        $newName = $baseName. $dataType .' ['.$keyChannel.']';

                        $arrSerie['name'] = $newName;
                        $arrSerie['group'] = $groupName;    
                        //Sensor Unit (°C, L ...)
                        $arrSerie['unit'] = $this->getSensorUnit($mappedData, $key, $keyNode, $keyChannel);    

                        $newKey = $keyChannel . '_' . $key;
                        $mappedData['series'][$newKey] = $arrSerie;    

                        $legend['data'][] = $newName;

                        if(!isset($arrGrouped[$groupName])) {
                            $arrGrouped[$groupName] = 0;                
                        }
                        $arrGrouped[$groupName] = $arrGrouped[$groupName] + (count($arrChannelData) > 0 ? 1 : 0);
                    }   
                }
                continue;
            } 

            //Process as normal
            $arrSerie['data'] = $data;

            $arrSerie['name'] = $baseName . $dataType;

            $arrSerie['group'] = $arrSensor['group'];           
            
            $mappedData['series'][$key] = $arrSerie;

            $legend['data'][] = $arrSerie['name'];

            if(!isset($arrGrouped[$baseGroupName])) {
                $arrGrouped[$baseGroupName] = 0;                
            }
            $arrGrouped[$baseGroupName] = $arrGrouped[$baseGroupName] + (count($data) > 0 ? 1 : 0);
        }

        $groupWithData = array_filter($arrGrouped, function($value) {
            return $value > 0;
        });

        $mappedData['groupWithData'] = $groupWithData;    

        unset($mappedData['seriesData']);

        $mappedData['legend'] = $legend;

        return $mappedData;
    }
    
    public function jsonSerialize()
    {
        $data = [
            'title' => $this->title,
            'grid' => $this->grid,
            'xAxis' => $this->xAxis,
            'yAxis' => $this->yAxis,
            'series' => $this->series
        ];

        if (isset($this->tooltip)) {
            $data['tooltip'] = $this->tooltip;
        }

        if (isset($this->legend)) {
            $data['legend'] = $this->legend;
        }

        if (isset($this->dataZoom)) {
            $data['dataZoom'] = $this->dataZoom;
        }

        return $data;
    }

    private function getSensorUnit($mappedData, $key, $keyNode, $keyChannel)
    {
        if(!isset($mappedData['stationSensors'])) {
            return '';
        }            

        $stationSensors = $mappedData['stationSensors'];

        $arrNodes = $stationSensors[$key]['nodes'][$keyNode];

        $foundNode = array_filter($arrNodes, function($value) use($keyChannel) {
            return $value['ch'] == $keyChannel;
        });

        $foundNode = reset($foundNode);

        if(!isset($foundNode['unit'])) {
            return '';
        }            

        return $foundNode['unit'];
    }        

    private function dynamicOptions($gridIndex, $arrSensor, $key, $foundSensors)
    {
        $sensorUnit = isset($arrSensor['unit']) ? $arrSensor['unit'] : '';
        $sensorName = isset($arrSensor['name']) ? $arrSensor['name'] : '';
        $axisLabelFormatter = '{value} ' . $sensorUnit;

        //Get predefined color by group
        $itemStyleColor = $this->getItemStyleColor($arrSensor, $key, $foundSensors);            

        $arrOptions = [
            'xAxis' => [
                'gridIndex' => $gridIndex,
                'type' => 'category',
                'boundaryGap' => true,
                'axisLine' => [ 
                    'onZero' => false, 
                    'show' => true, 
                    'lineStyle' => ['color' => '#979aa0'],
                    ],    
                'axisTick' => ['show' => false],
                'data' => []
            ],
            'yAxis' => [
                'gridIndex' => $gridIndex,
                'type' => 'value',
                'position' => 'left',
                'axisTick' => ['show' => false],
                'axisLine' => [
                    'lineStyle' => [
                        'color' => '#777777',
                    ],
                    'show' => true,
                ],
                'axisLabel' => [
                    'formatter' => $axisLabelFormatter,
                ],
                'splitLine' => ['show' => false],
            ],
            'serie' => [
                'name' => $sensorName,
                'type' => 'line',
                'smooth' => 0.5,
                'xAxisIndex' => $gridIndex,
                'yAxisIndex' => 0,
                'symbolSize' => 8,
                'itemStyle' => ['color' => $itemStyleColor],
                'hoverAnimation' => true,
                'data' => [],
            ]
        ];

        return $arrOptions;
    }

    private function getItemStyleColor($arrSensor, $key, $foundSensors)
    {
        $itemStyleColor = '#000000';

        if(!isset($arrSensor['group'])){
            return $itemStyleColor;
        }

        $arrKeysSensors = array_keys($foundSensors);

        $keyFound = array_search($key, $arrKeysSensors);

        $group = explode('_', $arrSensor['group'])[0];

        $nodeColors = config('echarts.stations.nodeColors');

        if(isset($nodeColors[$group][$keyFound])){
            $itemStyleColor = $nodeColors[$group][$keyFound];
        }

        return $itemStyleColor;    
    }    

    private function getProperties()
    {
        return get_object_vars($this);
    }
}
