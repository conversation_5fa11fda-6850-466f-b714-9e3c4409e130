<?php
/**
 * Created by PhpStorm.
 * User: <PERSON>.nonchev
 * Date: 7/2/2020
 * Time: 2:45 PM
 */

namespace App\Classes;


class MeteoStationReportFormatter
{
    /**
     * @param $data
     * @param $stationName
     * @return array
     */
    public function format($data, $stationName)
    {
        $report = [];
        $report['stationName'] = $stationName;
        $report['precipitationSum'] = $this->precipitationSum($data);
        $report['precipitationSumAbove'] = $this->precipitationSumAbove(5, $data);
        $airTemperature = $this->airTemperature($data);
        $report['airTemperatureMax'] = $airTemperature['Max'];
        $report['airTemperatureMin'] = $airTemperature['Min'];

        $soilTemperature = $this->getSoilMinMaxData('Soil temperature', 'soilTemperature', $data);
        foreach ($soilTemperature as $key => $value) {
            $report[$key] = $value;
        }

        $fieldCapacity = $this->getMinMaxData('Field capacity', 'soilMoisture', $data);
        foreach ($fieldCapacity as $key => $value) {
            $report[$key] = $value;
        }

        return $report;
    }

    /**
     * @param $data
     * @return array|float|\Illuminate\Contracts\Translation\Translator|string|null
     */
    private function precipitationSum($data)
    {
        $data = $this->findData('Precipitation', $data);

        $noData = trans('general.no_data');
        if ($data === $noData) {
            return $noData;
        }

        return round(array_sum($data), 1);
    }

    /**
     * @param $value
     * @param $data
     * @return array|float|\Illuminate\Contracts\Translation\Translator|string|null
     */
    private function precipitationSumAbove($value, $data)
    {
        $data = $this->findData('Precipitation', $data);

        $noData = trans('general.no_data');
        if ($data === $noData) {
            return $noData;
        }

        $result = array_filter($data, function ($valuePrecipitation) use ($value) {
            return $valuePrecipitation > $value;
        });

        return round(array_sum($result), 1);
    }

    /**
     * @param $param
     * @param $data
     * @param bool $strict
     * @return array|\Illuminate\Contracts\Translation\Translator|mixed|string|data|null
     */
    private function findData($param, $data, $strict = true)
    {
        $noData = trans('general.no_data');
        if (!isset($data['series'])) {
            return $noData;
        }

        $found = array_filter($data['series'], function ($value) use ($param, $strict) {
            if ($strict) {
                return $value['name'] === trans('general.' . $param);
            }
            return (strpos($value['name'], trans('general.' . $param)) !== false);
        });

        if (!$found) {
            return $noData;
        }

        if (!$strict) {
            return $found;
        }

        $found = reset($found);

        return $found['data'];
    }

    /**
     * @param $data
     * @return array
     */
    private function airTemperature($data)
    {
        $dataMin = $this->findData('Air temperature min', $data);
        $dataMax = $this->findData('Air temperature max', $data);
        $noData = trans('general.no_data');

        return ['Max' => is_array($dataMax) ? max($dataMax) : $noData, 'Min' => is_array($dataMin) ? min($dataMin) : $noData];
    }

    /**
     * @param $param
     * @param $property
     * @param $data
     * @return array
     */
    private function getMinMaxData($param, $property, $data)
    {
        $dataValues = $this->findData($param, $data, false);

        $noData = trans('general.no_data');
        $maxCount = 6;
        $minMaxData = [];
        for ($i = 1; $i <= $maxCount; ++$i) {
            $minMaxData[$property . 'Max' . $i] = $noData;
            $minMaxData[$property . 'Min' . $i] = $noData;
        }

        if ($dataValues === $noData) {
            return $minMaxData;
        }

        $index = 1;
        foreach ($dataValues as $value) {

            if (!isset($value['data'])) {
                $minMaxData[$property . 'Max' . $index] = $noData;
                $minMaxData[$property . 'Min' . $index] = $noData;
                $index = $index + 1;
                continue;
            }

            $values = $value['data'];
            $minMaxData[$property . 'Max' . $index] = max($values);
            $minMaxData[$property . 'Min' . $index] = min($values);
            $index = $index + 1;
        }

        return $minMaxData;
    }

    /**
     * @param $param
     * @param $property
     * @param $data
     * @return array
     */
    private function getSoilMinMaxData($param, $property, $data)
    {
        $dataValuesMin = $this->findData($param . ' min', $data, false);
        $dataValuesMax = $this->findData($param . ' max', $data, false);

        $noData = trans('general.no_data');
        $maxCount = 6;
        $minMaxData = [];
        for ($i = 1; $i <= $maxCount; ++$i) {
            $minMaxData[$property . 'Max' . $i] = $noData;
            $minMaxData[$property . 'Min' . $i] = $noData;
        }

        if ($dataValuesMin === $noData || $dataValuesMax === $noData) {
            return $minMaxData;
        }

        $index = 1;
        foreach ($dataValuesMin as $value) {

            if (!isset($value['data'])) {
                $minMaxData[$property . 'Min' . $index] = $noData;
                $index = $index + 1;
                continue;
            }

            $values = $value['data'];
            $minMaxData[$property . 'Min' . $index] = min($values);
            $index = $index + 1;
        }

        $index = 1;
        foreach ($dataValuesMax as $value) {

            if (!isset($value['data'])) {
                $minMaxData[$property . 'Max' . $index] = $noData;
                $index = $index + 1;
                continue;
            }

            $values = $value['data'];
            $minMaxData[$property . 'Max' . $index] = max($values);
            $index = $index + 1;
        }

        return $minMaxData;
    }
}