<?php

namespace App\Listeners;

use App\Events\IGeneratable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class GenerateListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  IGeneratable  $event All events for generating map files must implement IGeneratable interface.
     * @return void
     */
    public function handle(IGeneratable $event)
    {
        $event->generate();
    }

    
}
