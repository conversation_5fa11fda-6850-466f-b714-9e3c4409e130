<?php

namespace App\Listeners;

use App\Events\SoilOrderPushNotificationEvent;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

use App\Classes\GCM\GCM;
use App\Classes\APN\APN;
use App\Models\API\DeviceKey;
use DB;

class SoilOrderPushNotificationListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SoilOrderPushNotificationEvent  $event
     * @return void
     */
    public function handle(SoilOrderPushNotificationEvent $event)
    {
        $groupId = $event->groupId;
        $title = trans('notifications.new_tasks_title');
        $message = trans('notifications.new_tasks_text');
        $data['notId'] = time();
        $data['collapse_key'] = 'new_orders';
        
        //send notifications to android devices
        $gcm_device_keys = DB::
                table('su_users_device_keys')
                ->select('device_key')
                ->where('device_platform', '=', 'android')
                ->where('group_id', '=', $groupId)
                ->get()->toArray();
        
        if (!empty($gcm_device_keys)) {
            $gcm_devices = array_map(function($el) {
                return $el->device_key;
            }, $gcm_device_keys);

            $gcm_push = new GCM();
            $gcm_push->setDevices($gcm_devices);
            $gcm_push->send($title, $message, $data);
        }
        
        //send notifications to ios devices
        $apn_device_keys = DB::
                table('su_users_device_keys')
                ->select('device_key')
                ->where('device_platform', '=', 'ios')
                ->where('group_id', '=', $groupId)
                ->get()->toArray();
        
        if (!empty($apn_device_keys)) {

            $apn_devices = array_map(function($el) {
                return $el->device_key;
            }, $apn_device_keys);
            
            $apn_push = new APN();
            $apn_push->setDevices($apn_devices);
            $apn_push->send($title, $message);
        }
    }
}
