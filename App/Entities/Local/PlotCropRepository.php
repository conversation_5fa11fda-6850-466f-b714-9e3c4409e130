<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;


use Doctrine\ORM\EntityRepository;

class PlotCropRepository extends EntityRepository
{
    public function findCurrentByPlot(Plot $plot)
    {
        $qb = $this->createQueryBuilder('plotCrop')
            ->where('plotCrop.plot = :plot')->setParameter('plot', $plot)
            ->andWhere('plotCrop.fromDate <= :today')
            ->andWhere('plotCrop.toDate >= :today')->setParameter('today', (new \DateTime()))
        ;

        return $qb->getQuery()->getOneOrNullResult();
    }
}
