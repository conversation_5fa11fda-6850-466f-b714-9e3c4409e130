<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class Risk
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="risks")
 */
class Risk
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var Condition
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Condition")
     * @ORM\JoinColumn(name="conditions_id", referencedColumnName="id")
     *
     */
    protected $condition;

    /**
     * @var RiskGroup
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\RiskGroup", inversedBy="risks")
     * @ORM\JoinColumn(name="risk_groups_id", referencedColumnName="id")
     */
    protected $riskGroup;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return Condition
     */
    public function getCondition(): Condition
    {
        return $this->condition;
    }

    /**
     * @param Condition $condition
     * @return Risk
     */
    public function setCondition(Condition $condition): Risk
    {
        $this->condition = $condition;
        return $this;
    }

    /**
     * @return RiskGroup
     */
    public function getRiskGroup(): RiskGroup
    {
        return $this->riskGroup;
    }

    /**
     * @param RiskGroup $riskGroup
     * @return Risk
     */
    public function setRiskGroup(RiskGroup $riskGroup): Risk
    {
        $this->riskGroup = $riskGroup;
        return $this;
    }
}
