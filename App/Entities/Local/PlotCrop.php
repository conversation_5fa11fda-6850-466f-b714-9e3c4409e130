<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class PlotCrop
 * @package App\Entities\Local
 *
 * @ORM\Entity(repositoryClass="App\Entities\Local\PlotCropRepository")
 * @ORM\Table(name="su_satellite_plots_crops")
 */
class PlotCrop
{
    /**
     * @var int
     *
     * @ORM\Column(name="id")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var Plot
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Plot", inversedBy="plotCrops")
     * @ORM\JoinColumn(name="plot_id", referencedColumnName="gid")
     */
    protected $plot;

    /**
     * @var Crop
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Crop", inversedBy="plotCrops")
     * @ORM\JoinColumn(name="crop_id", referencedColumnName="id")
     */
    protected $crop;

    /**
     * @var int
     *
     * @ORM\Column(name="year", type="integer")
     */
    protected $year;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="from_date", type="date")
     */
    protected $fromDate;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="to_date", type="date")
     */
    protected $toDate;

    /**
     * @var boolean
     *
     * @ORM\Column(name="is_primary", type="boolean")
     */
    protected $isPrimary;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="emergence_date", type="date", nullable=true)
     */
    protected $emergenceDate;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="phenophase_date", type="date", nullable=true)
     */
    protected $phenophaseDate;

    /**
     * This column value represents the index of the array from frontend :(
     *
     * @var integer
     *
     * @ORM\Column(name="gdd_phenophase_id", type="integer")
     */
    protected $frontendArrayIndex;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return Plot
     */
    public function getPlot(): Plot
    {
        return $this->plot;
    }

    /**
     * @param Plot $plot
     * @return PlotCrop
     */
    public function setPlot(Plot $plot): PlotCrop
    {
        $this->plot = $plot;
        return $this;
    }

    /**
     * @return Crop
     */
    public function getCrop(): Crop
    {
        return $this->crop;
    }

    /**
     * @param Crop $crop
     * @return PlotCrop
     */
    public function setCrop(Crop $crop): PlotCrop
    {
        $this->crop = $crop;
        return $this;
    }

    /**
     * @return int
     */
    public function getYear(): int
    {
        return $this->year;
    }

    /**
     * @param int $year
     * @return PlotCrop
     */
    public function setYear(int $year): PlotCrop
    {
        $this->year = $year;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getFromDate(): \DateTime
    {
        return $this->fromDate;
    }

    /**
     * @param \DateTime $fromDate
     * @return PlotCrop
     */
    public function setFromDate(\DateTime $fromDate): PlotCrop
    {
        $this->fromDate = $fromDate;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getToDate(): \DateTime
    {
        return $this->toDate;
    }

    /**
     * @param \DateTime $toDate
     * @return PlotCrop
     */
    public function setToDate(\DateTime $toDate): PlotCrop
    {
        $this->toDate = $toDate;
        return $this;
    }

    /**
     * @return bool
     */
    public function isPrimary(): bool
    {
        return $this->isPrimary;
    }

    /**
     * @param bool $isPrimary
     * @return PlotCrop
     */
    public function setIsPrimary(bool $isPrimary): PlotCrop
    {
        $this->isPrimary = $isPrimary;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getEmergenceDate(): ?\DateTime
    {
        return $this->emergenceDate;
    }

    /**
     * @param \DateTime $emergenceDate
     * @return PlotCrop
     */
    public function setEmergenceDate(\DateTime $emergenceDate): PlotCrop
    {
        $this->emergenceDate = $emergenceDate;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getPhenophaseDate(): ?\DateTime
    {
        return $this->phenophaseDate;
    }

    /**
     * @param \DateTime $phenophaseDate
     * @return PlotCrop
     */
    public function setPhenophaseDate(\DateTime $phenophaseDate): PlotCrop
    {
        $this->phenophaseDate = $phenophaseDate;
        return $this;
    }

    /**
     * @return int
     */
    public function getFrontendArrayIndex(): int
    {
        return $this->frontendArrayIndex;
    }

    /**
     * @param int $frontendArrayIndex
     * @return PlotCrop
     */
    public function setFrontendArrayIndex(int $frontendArrayIndex): PlotCrop
    {
        $this->frontendArrayIndex = $frontendArrayIndex;
        return $this;
    }
}
