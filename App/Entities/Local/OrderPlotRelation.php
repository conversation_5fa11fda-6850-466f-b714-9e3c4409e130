<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class OrderPlotRelation
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="su_satellite_orders_plots_rel")
 */
class OrderPlotRelation
{
    /**
     * @TODO: add all fields
     */


    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var Order
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Order", inversedBy="orderPlotRelations")
     */
    protected $order;

    /**
     * @var Plot
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Plot")
     * @ORM\JoinColumn(name="plot_id", referencedColumnName="gid")
     */
    protected $plot;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return Order
     */
    public function getOrder(): Order
    {
        return $this->order;
    }

    /**
     * @param Order $order
     * @return OrderPlotRelation
     */
    public function setOrder(Order $order): OrderPlotRelation
    {
        $this->order = $order;
        return $this;
    }

    /**
     * @return Plot
     */
    public function getPlot(): Plot
    {
        return $this->plot;
    }

    /**
     * @param Plot $plot
     * @return OrderPlotRelation
     */
    public function setPlot(Plot $plot): OrderPlotRelation
    {
        $this->plot = $plot;
        return $this;
    }
}
