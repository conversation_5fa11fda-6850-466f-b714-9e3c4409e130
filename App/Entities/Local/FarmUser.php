<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class FarmUser
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="su_farms_users")
 */
class FarmUser
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var Farm
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Farm")
     * @ORM\JoinColumn(name="farm_id", referencedColumnName="id")
     */
    protected $farm;

    /**
     * @var User
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\User")
     * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
     */
    protected $user;

    /**
     * @var boolean
     *
     * @ORM\Column(name="is_visible", type="boolean")
     */
    protected $isVisible;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return Farm
     */
    public function getFarm(): Farm
    {
        return $this->farm;
    }

    /**
     * @param Farm $farm
     * @return FarmUser
     */
    public function setFarm(Farm $farm): FarmUser
    {
        $this->farm = $farm;
        return $this;
    }

    /**
     * @return User
     */
    public function getUser(): User
    {
        return $this->user;
    }

    /**
     * @param User $user
     * @return FarmUser
     */
    public function setUser(User $user): FarmUser
    {
        $this->user = $user;
        return $this;
    }

    /**
     * @return bool
     */
    public function isVisible(): bool
    {
        return $this->isVisible;
    }

    /**
     * @param bool $isVisible
     * @return FarmUser
     */
    public function setIsVisible(bool $isVisible): FarmUser
    {
        $this->isVisible = $isVisible;
        return $this;
    }
}
