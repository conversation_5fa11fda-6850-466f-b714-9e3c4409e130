<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class User
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="su_users")
 */
class User
{
    /**
     * @TODO: add all fields
     */

    /**
     * @var integer
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var Organization
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Organization")
     * @ORM\JoinColumn(name="last_chosen_organization_id", referencedColumnName="id")
     */
    protected $lastChosenOrganization;

    /**
     * @var Organization[]
     *
     * @ORM\ManyToMany(targetEntity="App\Entities\Local\Organization", mappedBy="users")
     */
    protected $organizations;

    /** @var FarmUser
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\FarmUser", mappedBy="user")
     */
    protected $farmUsers;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return User
     */
    public function setId(int $id): User
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return Organization
     */
    public function getLastChosenOrganization(): Organization
    {
        return $this->lastChosenOrganization;
    }

    /**
     * @param Organization $lastChosenOrganization
     * @return User
     */
    public function setLastChosenOrganization(Organization $lastChosenOrganization): User
    {
        $this->lastChosenOrganization = $lastChosenOrganization;
        return $this;
    }

    /**
     * @return Organization[]
     */
    public function getOrganizations(): array
    {
        return $this->organizations;
    }

    /**
     * @param Organization[] $organizations
     * @return User
     */
    public function setOrganizations(array $organizations): User
    {
        $this->organizations = $organizations;
        return $this;
    }
}
