<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class Condition
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="conditions")
 */
class Condition
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="rule_class", type="string")
     */
    protected $ruleClass;

    /**
     * @var string
     *
     * @ORM\Column(name="min_value", type="string")
     */
    protected $minValue;

    /**
     * @var string
     *
     * @ORM\Column(name="max_value", type="string")
     */
    protected $maxValue;

    /**
     * @var string
     *
     * @ORM\Column(name="avg_value", type="string")
     */
    protected $avgValue;

    /**
     * @var string
     *
     * @ORM\Column(name="condition_type", type="string", length=255)
     */
    protected $conditionType;

    /**
     * @var string
     *
     * @ORM\Column(name="value_type", type="string", length=255)
     */
    protected $valueType;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getRuleClass(): string
    {
        return $this->ruleClass;
    }

    /**
     * @param string $ruleClass
     * @return Condition
     */
    public function setRuleClass(string $ruleClass): Condition
    {
        $this->ruleClass = $ruleClass;
        return $this;
    }

    /**
     * @return string
     */
    public function getMinValue(): string
    {
        return $this->minValue;
    }

    /**
     * @param string $minValue
     * @return Condition
     */
    public function setMinValue(string $minValue): Condition
    {
        $this->minValue = $minValue;
        return $this;
    }

    /**
     * @return string
     */
    public function getMaxValue(): string
    {
        return $this->maxValue;
    }

    /**
     * @param string $maxValue
     * @return Condition
     */
    public function setMaxValue(string $maxValue): Condition
    {
        $this->maxValue = $maxValue;
        return $this;
    }

    /**
     * @return string
     */
    public function getAvgValue(): string
    {
        return $this->avgValue;
    }

    /**
     * @param string $avgValue
     * @return Condition
     */
    public function setAvgValue(string $avgValue): Condition
    {
        $this->avgValue = $avgValue;
        return $this;
    }

    /**
     * @return string
     */
    public function getConditionType(): string
    {
        return $this->conditionType;
    }

    /**
     * @param string $conditionType
     * @return Condition
     */
    public function setConditionType(string $conditionType): Condition
    {
        $this->conditionType = $conditionType;
        return $this;
    }

    /**
     * @return string
     */
    public function getValueType(): string
    {
        return $this->valueType;
    }

    /**
     * @param string $valueType
     * @return Condition
     */
    public function setValueType(string $valueType): Condition
    {
        $this->valueType = $valueType;
        return $this;
    }
}
