<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class RiskGroup
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="risk_groups")
 */
class RiskGroup
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string")
     */
    protected $name;

    /**
     * @var integer
     *
     * @ORM\Column(name="risk_level", type="integer")
     */
    protected $riskLevel;

    /**
     * @var PestDisease[]
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\PestD<PERSON>ase", inversedBy="riskGroups")
     * @ORM\JoinColumn(name="pests_diseases_id", referencedColumnName="id")
     */
    protected $pestDisease;

    /**
     * @var Risk[]
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\Risk", mappedBy="riskGroup")
     */
    protected $risks;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return RiskGroup
     */
    public function setName(string $name): RiskGroup
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return int
     */
    public function getRiskLevel(): int
    {
        return $this->riskLevel;
    }

    /**
     * @param int $riskLevel
     * @return RiskGroup
     */
    public function setRiskLevel(int $riskLevel): RiskGroup
    {
        $this->riskLevel = $riskLevel;
        return $this;
    }

    /**
     * @return PestDiseasePhenophase
     */
    public function getPestDisease(): PestDiseasePhenophase
    {
        return $this->pestDisease;
    }

    /**
     * @param PestDiseasePhenophase $pestDisease
     * @return RiskGroup
     */
    public function setPestDisease(PestDiseasePhenophase $pestDisease): RiskGroup
    {
        $this->pestDisease = $pestDisease;
        return $this;
    }

    /**
     * @return Risk[]
     */
    public function getRisks()
    {
        return $this->risks;
    }

    /**
     * @param Risk[] $risks
     * @return RiskGroup
     */
    public function setRisks(array $risks): RiskGroup
    {
        $this->risks = $risks;
        return $this;
    }
}
