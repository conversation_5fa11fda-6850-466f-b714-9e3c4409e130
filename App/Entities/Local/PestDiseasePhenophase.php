<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class PestDiseasePhenophase
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="pests_diseases_phenophases")
 */
class PestDiseasePhenophase
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var PestDisease
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\PestDisease")
     * @ORM\JoinColumn(name="pests_diseases_id", referencedColumnName="id")
     */
    protected $pestDisease;

    /**
     * @var Phenophase
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Phenophase")
     * @ORM\JoinColumn(name="phenophases_id", referencedColumnName="id")
     */
    protected $phenophase;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return PestDisease
     */
    public function getPestDisease(): PestDisease
    {
        return $this->pestDisease;
    }

    /**
     * @param PestDisease $pestDisease
     * @return PestDiseasePhenophase
     */
    public function setPestDisease(PestDisease $pestDisease): PestDiseasePhenophase
    {
        $this->pestDisease = $pestDisease;
        return $this;
    }

    /**
     * @return Phenophase
     */
    public function getPhenophase(): Phenophase
    {
        return $this->phenophase;
    }

    /**
     * @param Phenophase $phenophase
     * @return PestDiseasePhenophase
     */
    public function setPhenophase(Phenophase $phenophase): PestDiseasePhenophase
    {
        $this->phenophase = $phenophase;
        return $this;
    }
}
