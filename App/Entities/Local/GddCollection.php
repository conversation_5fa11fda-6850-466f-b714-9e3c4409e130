<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class GddCollection
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="gdd_collections")
 */
class GddCollection
{
    /**
     * @var integer
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="slug", type="string")
     */
    protected $slug;

    /**
     * @var float
     *
     * @ORM\Column(name="t_base", type="float")
     */
    protected $tBase;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getSlug(): string
    {
        return $this->slug;
    }

    /**
     * @param string $slug
     * @return GddCollection
     */
    public function setSlug(string $slug): GddCollection
    {
        $this->slug = $slug;
        return $this;
    }

    /**
     * @return float
     */
    public function getTBase(): float
    {
        return $this->tBase;
    }

    /**
     * @param float $tBase
     * @return GddCollection
     */
    public function setTBase(float $tBase): GddCollection
    {
        $this->tBase = $tBase;
        return $this;
    }
}
