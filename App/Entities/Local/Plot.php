<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class Plot
 * @package App\Entities\Local
 *
 * @ORM\Entity(repositoryClass="App\Entities\Local\PlotRepository")
 * @ORM\Table(name="su_satellite_plots")
 */
class Plot
{
    /**
     * @var int
     *
     * @ORM\Column(name="gid", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $gid;

    /**
     * @var float
     *
     * @ORM\Column(name="area", type="float")
     */
    protected $area;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    protected $name;

    /**
     * @var geom
     *
     * @ORM\Column(name="geom", type="geometry")
     */
    protected $geom;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="upload_date", type="datetime")
     */
    protected $uploadDate;

    /**
     * @var Farm
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Farm")
     * @ORM\JoinColumn(name="farm_id", referencedColumnName="id")
     */
    protected $farm;

    /**
     * @var PlotCrop[]
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\PlotCrop", mappedBy="plot")
     */
    protected $plotCrops;

    /**
     * @return int
     */
    public function getGid(): int
    {
        return $this->gid;
    }

    /**
     * @return float
     */
    public function getArea(): float
    {
        return $this->area;
    }

    /**
     * @param float $area
     * @return Plot
     */
    public function setArea(float $area): Plot
    {
        $this->area = $area;
        return $this;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return Plot
     */
    public function setName(string $name): Plot
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return geom
     */
    public function getGeom()
    {
        return $this->geom;
    }

    /**
     * @param geom $geom
     * @return Plot
     */
    public function setGeom($geom): Plot
    {
        $this->geom = $geom;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getUploadDate(): \DateTime
    {
        return $this->uploadDate;
    }

    /**
     * @param \DateTime $uploadDate
     * @return Plot
     */
    public function setUploadDate(\DateTime $uploadDate): Plot
    {
        $this->uploadDate = $uploadDate;
        return $this;
    }

    /**
     * @return Farm
     */
    public function getFarm(): Farm
    {
        return $this->farm;
    }

    /**
     * @param Farm $farm
     * @return Plot
     */
    public function setFarm(Farm $farm): Plot
    {
        $this->farm = $farm;
        return $this;
    }

    /**
     * @return PlotCrop[] | PlotCrop
     */
    public function getPlotCrops()
    {
        return $this->plotCrops;
    }

    /**
     * @param PlotCrop[] $plotCrops
     * @return Plot
     */
    public function setPlotCrops(array $plotCrops): Plot
    {
        $this->plotCrops = $plotCrops;
        return $this;
    }
}
