<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class Farm
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="su_farms")
 */
class Farm
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    protected $name;

    /**
     * @var string
     *
     * @ORM\Column(name="comment", type="string", length=255)
     */
    protected $comment;

    /**
     * @var Organization
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Organization")
     * @ORM\Jo<PERSON>(name="organization_id", referencedColumnName="id")
     */
    protected $organization;

    /**
     * @var FarmUser
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\FarmUser", mappedBy="farm")
     */
    protected $farmUsers;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="created", type="datetime")
     */
    protected $created;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return Farm
     */
    public function setName(string $name): Farm
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return string
     */
    public function getComment(): string
    {
        return $this->comment;
    }

    /**
     * @param string $comment
     * @return Farm
     */
    public function setComment(string $comment): Farm
    {
        $this->comment = $comment;
        return $this;
    }

    /**
     * @return Organization
     */
    public function getOrganization(): Organization
    {
        return $this->organization;
    }

    /**
     * @param Organization $organization
     * @return Farm
     */
    public function setOrganization(Organization $organization): Farm
    {
        $this->organization = $organization;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getCreated(): \DateTime
    {
        return $this->created;
    }

    /**
     * @param \DateTime $created
     * @return Farm
     */
    public function setCreated(\DateTime $created): Farm
    {
        $this->created = $created;
        return $this;
    }
}
