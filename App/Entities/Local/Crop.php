<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class Crop
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="su_crop_codes")
 */
class Crop
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="crop_code", type="string", length=6)
     */
    protected $cropCode;

    /**
     * @var string
     *
     * @ORM\Column(name="crop_name", type="string", length=100)
     */
    protected $cropName;

    /**
     * @var GddCollection
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\GddCollection")
     * @ORM\JoinColumn(name="gdd_collection_id", referencedColumnName="id")
     */
    protected $gddCollection;

    /**
     * @var PlotCrop[]
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\PlotCrop", mappedBy="crop")
     */
    protected $plotCrops;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getCropCode(): string
    {
        return $this->cropCode;
    }

    /**
     * @param string $cropCode
     * @return Crop
     */
    public function setCropCode(string $cropCode): Crop
    {
        $this->cropCode = $cropCode;
        return $this;
    }

    /**
     * @return string
     */
    public function getCropName(): string
    {
        return $this->cropName;
    }

    /**
     * @param string $cropName
     * @return Crop
     */
    public function setCropName(string $cropName): Crop
    {
        $this->cropName = $cropName;
        return $this;
    }

    /**
     * @return GddCollection
     */
    public function getGddCollection()
    {
        return $this->gddCollection;
    }

    /**
     * @param GddCollection $gddCollection
     * @return Crop
     */
    public function setGddCollection(GddCollection $gddCollection): Crop
    {
        $this->gddCollection = $gddCollection;
        return $this;
    }

    /**
     * @return PlotCrop[]
     */
    public function getPlotCrops(): array
    {
        return $this->plotCrops;
    }

    /**
     * @param PlotCrop[] $plotCrops
     * @return Crop
     */
    public function setPlotCrops(array $plotCrops): Crop
    {
        $this->plotCrops = $plotCrops;
        return $this;
    }
}
