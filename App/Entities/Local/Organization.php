<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class Organization
 * @package App\Entities\Local
 *
 * @ORM\Entity()
 * @ORM\Table(name="su_organizations")
 */
class Organization
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id();
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    protected $name;

    /**
     * @var string
     *
     * @ORM\Column(name="iso_alpha_2_code", type="string", length=2)
     */
    protected $isoAlpha2Code;

    /**
     * @var string
     *
     * @ORM\Column(name="address", type="string", length=255)
     */
    protected $address;

    /**
     * @var string
     *
     * @ORM\Column(name="email", type="string", length=255)
     */
    protected $email;

    /**
     * @var string
     *
     * @ORM\Column(name="phone", type="string", length=127)
     */
    protected $phone;

    /**
     * @var boolean
     *
     * @ORM\Column(name="active", type="boolean")
     */
    protected $isActive;

    /**
     * @var User[]
     *
     * @ORM\ManyToMany(targetEntity="App\Entities\Local\User", inversedBy="organizations")
     * @ORM\JoinTable(name="su_organizations_users")
     */
    protected $users;

    /**
     * @var Order[]
     *
     * @ORM\OneToMany(targetEntity="App\Entities\Local\Order", mappedBy="organization")
     */
    protected $orders;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return Organization
     */
    public function setName(string $name): Organization
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return string
     */
    public function getIsoAlpha2Code(): string
    {
        return $this->isoAlpha2Code;
    }

    /**
     * @param string $isoAlpha2Code
     * @return Organization
     */
    public function setIsoAlpha2Code(string $isoAlpha2Code): Organization
    {
        $this->isoAlpha2Code = $isoAlpha2Code;
        return $this;
    }

    /**
     * @return string
     */
    public function getAddress(): string
    {
        return $this->address;
    }

    /**
     * @param string $address
     * @return Organization
     */
    public function setAddress(string $address): Organization
    {
        $this->address = $address;
        return $this;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @param string $email
     * @return Organization
     */
    public function setEmail(string $email): Organization
    {
        $this->email = $email;
        return $this;
    }

    /**
     * @return string
     */
    public function getPhone(): string
    {
        return $this->phone;
    }

    /**
     * @param string $phone
     * @return Organization
     */
    public function setPhone(string $phone): Organization
    {
        $this->phone = $phone;
        return $this;
    }

    /**
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->isActive;
    }

    /**
     * @param bool $isActive
     * @return Organization
     */
    public function setIsActive(bool $isActive): Organization
    {
        $this->isActive = $isActive;
        return $this;
    }

    /**
     * @return User[]
     */
    public function getUsers(): array
    {
        return $this->users;
    }

    /**
     * @param User[] $users
     * @return Organization
     */
    public function setUsers(array $users): Organization
    {
        $this->users = $users;
        return $this;
    }
}
