<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;

use Doctrine\ORM\Mapping as ORM;

/**
 * Class CalculatedRisk
 * @package App\Entities\Local
 *
 * @ORM\Entity(repositoryClass="App\Entities\Local\CalculatedRiskRepository")
 * @ORM\Table(name="calculated_risks")
 */
class CalculatedRisk
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id()
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected $id;

    /**
     * @var PestDisease
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\PestDisease")
     * @ORM\JoinColumn(name="pests_diseases_id", referencedColumnName="id")
     */
    protected $pestDisease;


    /**
     * @var Plot
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Plot")
     * @ORM\JoinColumn(name="plot_id", referencedColumnName="gid")
     */
    protected $plot;

    /**
     * @var Phenophase
     *
     * @ORM\ManyToOne(targetEntity="App\Entities\Local\Phenophase")
     * @ORM\JoinColumn(name="phenophases_id", referencedColumnName="id")
     */
    protected $phenophase;

    /**
     * @var float
     *
     * @ORM\Column(name="risk_level", type="float")
     */
    protected $riskLevel;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="date", type="date")
     */
    protected $date;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return PestDisease
     */
    public function getPestDisease(): PestDisease
    {
        return $this->pestDisease;
    }

    /**
     * @param PestDisease $pestDisease
     * @return CalculatedRisk
     */
    public function setPestDisease(PestDisease $pestDisease): CalculatedRisk
    {
        $this->pestDisease = $pestDisease;
        return $this;
    }

    /**
     * @return Plot
     */
    public function getPlot(): Plot
    {
        return $this->plot;
    }

    /**
     * @param Plot $plot
     * @return CalculatedRisk
     */
    public function setPlot(Plot $plot): CalculatedRisk
    {
        $this->plot = $plot;
        return $this;
    }

    /**
     * @return Phenophase
     */
    public function getPhenophase(): Phenophase
    {
        return $this->phenophase;
    }

    /**
     * @param Phenophase $phenophase
     * @return CalculatedRisk
     */
    public function setPhenophase(Phenophase $phenophase): CalculatedRisk
    {
        $this->phenophase = $phenophase;
        return $this;
    }

    /**
     * @return float
     */
    public function getRiskLevel(): float
    {
        return $this->riskLevel;
    }

    /**
     * @param float $riskLevel
     * @return CalculatedRisk
     */
    public function setRiskLevel(float $riskLevel): CalculatedRisk
    {
        $this->riskLevel = $riskLevel;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getDate(): \DateTime
    {
        return $this->date;
    }

    /**
     * @param \DateTime $date
     * @return CalculatedRisk
     */
    public function setDate(\DateTime $date): CalculatedRisk
    {
        $this->date = $date;
        return $this;
    }
}
