<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;


use Doctrine\Common\Util\Debug;
use Doctrine\ORM\EntityRepository;

class PhenophaseRepository extends EntityRepository
{
    public function findPhaseByCropAndGdd(GddCollection $gddCollection, $gddValue)
    {
        $qb = $this->createQueryBuilder('phenophase');
        $qb->where('phenophase.gddCollection = :gddCollection')->setParameter('gddCollection', $gddCollection)
            ->andWhere('phenophase.minGdd <= :gddValue')
            ->andWhere('phenophase.maxGdd > :gddValue')
            ->setParameter('gddValue', $gddValue);

        return $qb->getQ<PERSON>()->getOneOrNullResult();
    }

    /**
     * @param GddCollection $gddCollection
     * @return Phenophase | null
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function findFirstPhaseForCrop(GddCollection $gddCollection)
    {
        $subQb = $this->createQueryBuilder('minGdd')
            ->select('min(minGdd.sumGdd)')
            ->where('minGdd.gddCollection = :gddCollection');


        $qb = $this->createQueryBuilder('phenophase');
        $qb->where('phenophase.gddCollection = :gddCollection')
            ->setParameter('gddCollection', $gddCollection)
            ->andWhere("phenophase.sumGdd = ({$subQb->getDQL()})");

        return $qb->getQuery()->getOneOrNullResult();
    }
}
