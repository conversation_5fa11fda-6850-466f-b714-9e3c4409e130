<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @company TechnoFarm Ltd., https://www.technofarm.bg/
 * @copyright Copyright (c) 2019 TechnoFarm Ltd.
 */

namespace App\Entities\Local;


use Doctrine\ORM\EntityRepository;

class OrderRepository extends EntityRepository
{
    public function findAllActiveMeteo()
    {
        $qb = $this->createQueryBuilder('orders');
        $qb->where('orders.type = :type')->setParameter('type', 'meteo')
            ->andWhere('orders.fromDate <= :now')
            ->andWhere('orders.toDate >= :now')->setParameter('now', (new \DateTime()))
            ->andWhere('orders.status = :status')->setParameter('status', 'processed');

        return $qb->getQuery()->getResult();
    }
}
