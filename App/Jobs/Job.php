<?php

namespace App\Jobs;

use App\Models\Country;
use Illuminate\Bus\Queueable;
use Config;
use Illuminate\Database\Eloquent\Model;
use App;
use App\Models\ConfigParamValue;

abstract class Job
{
    /*
    |--------------------------------------------------------------------------
    | Queueable Jobs
    |--------------------------------------------------------------------------
    |
    | This job base class provides a central location to place any logic that
    | is shared across all of your jobs. The trait included with the class
    | provides access to the "onQueue" and "delay" queue helper methods.
    |
    */

    use Queueable;

    protected function loadCountrySpecificConfigs($countryIsoAlpha2Code)
    {
        $country = Country::where('iso_alpha_2_code', strtoupper($countryIsoAlpha2Code))->first();

        $this->loadConfigParams($country);
        $this->setDBConnection($country);
    }

    private function loadConfigParams($country)
    {
        /** @var ConfigParamValue[] $configParams */
        $country->configParams->loadMissing('configParam');
        $configParams = $country->configParams;
        foreach ($configParams as $each) {
            Config::set($each->configParam->domain. '.' . $each->configParam->name, $each->value);
        }

        App::useStoragePath(Config::get('globals.STORAGE_PATH'));
    }

    private function setDBConnection($country)
    {
        Model::getConnectionResolver()->setDefaultConnection(strtoupper($country->iso_alpha_2_code));
    }
}
