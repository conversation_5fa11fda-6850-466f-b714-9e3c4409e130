<?php

namespace App\Jobs;

use App\Services\Irrigation\IrrigationDataRawService;
use App\Services\Wialon\ReportService;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Services\Irrigation\IrrigationEventsService;
use DB;

class IrrigationPerDayReport extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;

    private $reportService;
    private $irrigationDataRawService;
    private $serverName;
    private $reportId;
    private $params;
    private $url;
    private $token;
    private $organizationId;
    private $date;
    private $wialonUnitId;

    /**
     * IrrigationPerDayReport constructor.
     *
     * @param ReportService $reportService
     * @param string $serverName
     * @param int $reportId
     * @param array $params
     * @param string $url
     * @param string $token
     * @param int $organizationId
     * @param IrrigationDataRawService $irrigationDataRawService
     * @param string $date
     * @param int|null $wialonUnitId
     */

    public function __construct(ReportService $reportService, string $serverName, int $reportId, array $params, string $url, string $token, int $organizationId,
                                IrrigationDataRawService $irrigationDataRawService, string $date, ?int $wialonUnitId)

    {
        $this->reportService = $reportService;
        $this->irrigationDataRawService = $irrigationDataRawService;
        $this->serverName = $serverName;
        $this->reportId = $reportId;
        $this->params = $params;
        $this->url = $url;
        $this->token = $token;
        $this->organizationId = $organizationId;
        $this->date = $date;
        $this->wialonUnitId = $wialonUnitId;
        $this->connection = 'database_' . $serverName;
    }

    /**
     * * Execute the job.
     */
    public function handle()
    {
        $this->loadCountrySpecificConfigs($this->serverName);
        $this->reportService->integrationReport($this->url, $this->params, $this->reportId, $this->token);

        DB::beginTransaction();
        try {
            $this->irrigationDataRawService->deleteIrrDataRaw($this->date, $this->wialonUnitId);
            IrrigationEventsService::deleteEvents($this->organizationId, $this->date, $this->wialonUnitId);

            $this->irrigationDataRawService->addContent($this->reportId, $this->organizationId);
            $newIrrigationEventIds = $this->irrigationDataRawService->addIrrigationEvents();
            $this->irrigationDataRawService->addContentEventsPlots($newIrrigationEventIds);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
