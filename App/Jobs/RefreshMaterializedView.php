<?php

namespace App\Jobs;

use App\Jobs\Job;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

use DB;

class RefreshMaterializedView extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    protected $viewName;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($viewName="")
    {
        $this->viewName = $viewName;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if($this->viewName && strlen($this->viewName)){

            //REFRESH MATERIALIZED VIEW
            DB::statement('refresh materialized view ' . $this->viewName);    
        }
        
    }
}
