<?php

namespace App\Jobs;

use App\Jobs\Job;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

use App\Classes\LoadData\SoilPointsProcessingClass;

use App\Models\File;

class LoadSoilPointsSHP extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;
    
    protected $file;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(File $file)
    {
        $this->file = $file;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $processor = new SoilPointsProcessingClass($this->file);
        $processor->startProcessing();
    }
}
