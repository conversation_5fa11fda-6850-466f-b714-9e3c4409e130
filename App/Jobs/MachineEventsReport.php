<?php

namespace App\Jobs;

use App\Services\Machine\MachineEventService;
use App\Services\Wialon\ReportService;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use DB;

class MachineEventsReport extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 600;

    private $reportService;
    private $machineEventService;
    private $serverName;
    private $reportId;
    private $params;
    private $url;
    private $token;
    private $organizationId;
    private $wialonUnitId;
    private $date;

    /**
     * IrrigationPerDay constructor.
     *
     * MachineEventsReport constructor.
     * @param ReportService $reportService
     * @param string $serverName
     * @param int $reportId
     * @param array $params
     * @param string $url
     * @param string $token
     * @param int $organizationId
     * @param MachineEventService $machineEventService
     * @param string $date
     * @param int|null $wialonUnitId
     */

    public function __construct(ReportService $reportService, string $serverName, int $reportId, array $params, string $url, string $token,
                                int $organizationId, MachineEventService $machineEventService, string $date, ?int $wialonUnitId)

    {
        $this->reportService = $reportService;
        $this->machineEventService = $machineEventService;
        $this->serverName = $serverName;
        $this->reportId = $reportId;
        $this->params = $params;
        $this->url = $url;
        $this->token = $token;
        $this->organizationId = $organizationId;
        $this->wialonUnitId = $wialonUnitId;
        $this->date = $date;

        $this->connection = 'database_' . $serverName;
    }

    /**
     * Execute the job.
     *
     * @throws \Exception
     */
    public function handle()
    {
        $this->loadCountrySpecificConfigs($this->serverName);
        $this->reportService->integrationReport($this->url, $this->params, $this->reportId, $this->token);

        DB::beginTransaction();
        try {
            $this->machineEventService->deleteEvents($this->organizationId, $this->date, $this->wialonUnitId);
            $this->machineEventService->storeMachineEventsReport($this->reportId, $this->organizationId);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
