<?php

namespace App\Jobs;

use App\Jobs\Job;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

use App\Classes\LoadData\ProcessBoundariesClass;

use App\Models\File;

class LoadBoundariesFile extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;
    
    protected $file;
    protected $fileType;

    /**
     * Create a new job instance.
     *
     * LoadBoundariesFile constructor.
     * @param File $file
     * @param string $fileType
     */
    public function __construct(File $file, ?string $fileType = null)
    {
        $this->file = $file;
        $this->fileType = $fileType;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $processor = new ProcessBoundariesClass($this->file, $this->fileType);
        $processor->startProcessing();
    }
}
