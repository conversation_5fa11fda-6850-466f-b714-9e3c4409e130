<?php

namespace App\Events;

use App\Helpers\Helper;
use App\Models\Order;
use App\Models\Plot;
use App\Models\ServiceProvider;
use App\Models\User;
use App\Services\System\SystemService;
use Config;
use DB;
use File;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Queue\SerializesModels;
use View;

class GenerateStaticMapFilesEvent implements IGeneratable
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     */
    public function __construct()
    {
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {
        $this->generateGeoScanMapFile();
        $this->generateImagesByDateMapFiles();
        $this->generateShp2ImgMapFile();
        $this->generatePlotsPreviewMapFile();
        $this->generateVraMapFile();
        $this->generateSoilMapFile();
        $this->generateSoilMapFileOld();
        $this->generateWaterPoundsMapFile();
        $this->generateIndexMapFile();
        $this->generateSoilGridMapFile();
        $this->generateOrdersMapFile();
        $this->generateOrdersMapFileOld();
        $this->generatePlotsByCropMapFile();
        $this->generateLayerCmsPlotsMapFile();
        $this->generateLayerTileIdx();
        $this->generateLayerTileIdxMask();
        $this->generateIndexLayerFile();
        $this->generateIndexWaterLayerFile();
        $this->generateLayerUploadPlotsBoundaries();
        $this->generateTmpPlotsBoundaryLayerFile();
        $this->generateTmpPlotsBoundaryOverlapsLayerFile();
        $this->generateExistingPlotsBoundaryLayerFile();
        $this->generateLayerEditPlotsBoundaries();
    }

    private function generateGeoScanMapFile()
    {
        $data = [
            'base_path' => base_path(),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'osm_connection' => Config::get('globals.OSM_CONNECTION')
        ];

        $mapStr = View::make('maps.static.geo-scan', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'geo_scan.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function generatePlotsByCropMapFile()
    {
        $areaCoef = Config::get('globals.M2_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

        $orderedPolygonsQuery = Plot::selectRaw("
                DISTINCT ON (su_satellite_plots.gid) su_satellite_plots.gid, scc.crop_name_%lang% as culture, su_satellite_plots.geom, scc.crop_color,
                round((ST_Area(su_satellite_plots.geom)*{$areaCoef})::numeric, 3) AS area
            ")
            ->join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->join(
                DB::raw("dblink('" . $mainDBConnectionString . "', $$
                select su.id, su.old_id 
                from su_users su
                join oauth_access_tokens as oat on su.id = oat.user_id 
                where oat.id = (select (payload::json)->>'jti' as global_user_id from verify('%access_token%', 'secret'))
            $$) as global_user(id int, old_id int)"), 'global_user.old_id', '=', 'su.id')
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
            ->join('su_satellite_orders so', 'so.id', '=', 'sopr.order_id')
            ->leftJoin('su_satellite_plots_crops AS sspc', function ($q) {
                $q->on('sspc.plot_id', '=', 'su_satellite_plots.gid');
                $q->on('sspc.year', DB::raw('%year%'));
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'sspc.crop_id')
            ->where([
                ['so.type', '\'index\'']
            ])
            ->where(function ($query) {
                $query->where('sspc.is_primary', 'true');
                $query->orWhere('sspc.is_primary', null);
            })
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%farm_ids%)'));
                $query->orWhereIn('su_satellite_plots.farm_id', ['%farm_ids%']);
            })
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%plot_ids%)'));
                $query->orWhereIn('su_satellite_plots.gid', ['%plot_ids%']);
            })
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%year%)'));
                $query->orWhereIn('so.year', ['%year%']);
            })
            ->where(function ($query) {
                $query->where('sspc.crop_id', DB::raw('any(array %crop_ids%::integer[])'));
                $query->orWhere(DB::raw('array_length(array %crop_ids%::integer[], 1)'), null);
                $query->orWhere(DB::raw('(
                    SELECT bool_or(a is null)
                    FROM unnest(array %crop_ids%::integer[]) s(a)
                    WHERE sspc.crop_id = any(array %crop_ids%::integer[])
                    OR sspc.crop_id is null
                )'), 'true');
            })
            ->groupBy('su_satellite_plots.gid', 'scc.crop_name_%lang%', 'sspc.irrigated', 'scc.crop_color')
            ->orderBy('su_satellite_plots.gid', 'ASC')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_plots_by_crop',
            'utf' => false,
            'maxextent' => false,
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$orderedPolygonsQuery}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . "",
            'gml_include_items' => ["gid", "crop_color"],
            'transparency' => "100",
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
            'tag_label' => "area",
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'validation' => true,
            'params' => [
                [
                    "name" => "lang",
                    'regular_expression' => '[a-zA-Z]+'
                ],
                [
                    "name" => "access_token",
                    'regular_expression' => '^.+*$' // match jwt
                ],
                [
                    "name" => "farm_ids",
                    'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$' // match comma separated numbers
                ],
                [
                    "name" => "default_farm_ids",
                    'regular_expression' => 'null'
                ],
                [
                    "name" => "plot_ids",
                    'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$' // match comma separated numbers
                ],
                [
                    "name" => "default_plot_ids",
                    'regular_expression' => 'null'
                ],
                [
                    "name" => "crop_ids",
                    'regular_expression' => '^\[([0-9]+|null)*((,|,\s)([0-9]+|null))*\]$' // match array, example '[123, null, 456]'
                ],
                [
                    "name" => "default_crop_ids",
                    'regular_expression' => '[]'
                ],
                [
                    "name" => "year",
                    'regular_expression' => '[0-9]+'
                ],
            ],
            'classes' => [
                [
                    "name" => "layer_satellite_order_ready",
                    "border_color" => '0 131 201',
                    "color" => '[crop_color]',
                    "tags" => true,
                    "size" => 10,
                    "width" => 2,
                    "label_text" => "'[culture] - [area] {$areaLabel}'"
                ]
            ]
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        @unlink(Config::get('globals.WMS_MAP_PATH') . "layer_plots_by_crop.map");
        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_plots_by_crop.map", 'w');
        $res = fwrite($fp, $layerStr);
        fclose($fp);
    }

    private function generateOrdersMapFile()
    {
        $areaCoef = Config::get('globals.M2_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

        $orderedPolygonsQuery = Plot::selectRaw("
                DISTINCT ON (su_satellite_plots.gid) su_satellite_plots.gid, 
                scc.crop_name_%lang% as crop,
                su_satellite_plots.geom,
                round((ST_Area(su_satellite_plots.geom)*{$areaCoef})::numeric, 3) AS area,
                sspc.from_date as sowing_date,
                sspc.to_date as harvest_date,
                sspc.irrigated
            ")
            ->join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->join('su_satellite_orders_plots_rel AS sopr', 'sopr.plot_uuid', 'su_satellite_plots.uuid')
            ->join('su_satellite_orders AS so', function ($join) {
                $join->on('so.uuid', '=', 'sopr.order_uuid');
                $join->where('so.type', '=', '\'index\'');
            })
            ->join(
                DB::raw("dblink('" . $mainDBConnectionString . "', $$
                select su.id, su.old_id 
                from su_users su
                join oauth_access_tokens as oat on su.id = oat.user_id 
                where oat.id = (select (payload::json)->>'jti' as global_user_id from verify('%access_token%', 'secret'))
            $$) as global_user(id int, old_id int)"), 'global_user.old_id', '=', 'su.id')
            ->leftJoin('su_satellite_plots_crops AS sspc', function ($join) {
                $join->on('sspc.plot_id', '=', 'su_satellite_plots.gid');
                $join->where('sspc.is_primary', DB::raw('true'));
                $join->where(function ($query) {
                    $query->whereNull(DB::raw('COALESCE(%year%)'));
                    $query->orWhere('sspc.year', '%year%');
                });
            })
            ->leftJoin('su_crop_codes AS scc', 'scc.id', '=', 'sspc.crop_id')
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%plot_ids%)'));
                $query->orWhereIn('su_satellite_plots.gid', ['%plot_ids%']);
            })
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%farm_ids%)'));
                $query->orWhereIn('su_satellite_plots.farm_id', ['%farm_ids%']);
            })
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%year%)'));
                $query->orWhere('so.year', '%year%');
            })
            ->where(function ($query) {
                $query->where('sspc.crop_id', DB::raw('any(array %crop_ids%::integer[])'));
                $query->orWhere(DB::raw('array_length(array %crop_ids%::integer[], 1)'), null);
                $query->orWhere(DB::raw('(
                    SELECT bool_or(a is null)
                    FROM unnest(array %crop_ids%::integer[]) s(a)
                    WHERE sspc.crop_id = any(array %crop_ids%::integer[])
                    OR sspc.crop_id is null
                )'), 'true');
            })
            ->groupBy('su_satellite_plots.gid', 'scc.crop_name_%lang%', 'sspc.id')
            ->orderBy('su_satellite_plots.gid', 'ASC')
            ->orderBy('sspc.is_primary', 'DESC')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_satellite_orders',
            'utf' => false,
            'maxextent' => false,
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$orderedPolygonsQuery}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . "",
            'gml_include_items' => ["gid"],
            'transparency' => "100",
            'epsg_code' => Config::get('globals.DEFAULT_DB_CRS'),
            'tag_label' => "area",
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'validation' => true,
            'params' => [
                [
                    "name" => "lang",
                    'regular_expression' => '[a-zA-Z]+'
                ],
                [
                    "name" => "access_token",
                    'regular_expression' => '^.+*$' // match jwt
                ],
                [
                    "name" => "farm_ids",
                    'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$' // match comma separated numbers
                ],
                [
                    "name" => "default_farm_ids",
                    'regular_expression' => 'null'
                ],
                [
                    "name" => "plot_ids",
                    'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$' // match comma separated numbers
                ],
                [
                    "name" => "default_plot_ids",
                    'regular_expression' => 'null'
                ],
                [
                    "name" => "crop_ids",
                    'regular_expression' => '^\[([0-9]+|null)*((,|,\s)([0-9]+|null))*\]$' // match array, example '[123, null, 456]'
                ],
                [
                    "name" => "default_crop_ids",
                    'regular_expression' => '[]'
                ],
                [
                    "name" => "year",
                    'regular_expression' => '[0-9]+'
                ]
            ],
            'classes' => [
                [
                    "name" => "layer_satellite_order_ready",
                    "border_color" => '0 131 201',
                    "label_color" => '0 0 0',
                    "tags" => true,
                    "size" => 10,
                    "width" => 2,
                    "label_text" => "'[crop] - [area] {$areaLabel}'"
                ]
            ]
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        @unlink(Config::get('globals.WMS_MAP_PATH') . "layer_satellite_orders.map");
        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_satellite_orders.map", 'w');
        $res = fwrite($fp, $layerStr);
        fclose($fp);
    }

    private function generateOrdersMapFileOld()
    {
        $areaCoef = Config::get('globals.M2_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

        $orderedPolygonsQuery = Order::selectRaw("DISTINCT sp.gid, cc.crop_name_%lang% as culture, sp.geom,
                    round((ST_Area(sp.geom)*{$areaCoef})::numeric, 3) AS area")
            ->join('su_satellite_orders_plots_rel sopr', 'su_satellite_orders.id', '=', 'sopr.order_id')
            ->join('su_satellite_plots sp', 'sp.gid', '=', 'sopr.plot_id')
            ->leftJoin('su_satellite_plots_crops AS spc', function ($join) {
                $join->on('spc.plot_id', '=', 'sp.gid')
                    ->on('spc.year', '=', 'su_satellite_orders.year')
                    ->where('spc.is_primary', '=', 'true');
            })
            ->leftJoin('su_crop_codes cc', 'spc.crop_id', '=', 'cc."id"')
            ->join('su_farms_users as fu', 'fu.farm_id', '=', 'sp.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'fu.user_id')
            ->join(
                DB::raw("dblink('" . $mainDBConnectionString . "', $$
                select su.id, su.old_id 
                from su_users su
                join oauth_access_tokens as oat on su.id = oat.user_id 
                where oat.id = (select (payload::json)->>'jti' as global_user_id from verify('%access_token%', 'secret'))
            $$) as global_user(id int, old_id int)"), 'global_user.old_id', '=', 'su.id')
            ->join('su_farms f', 'f.id', '=', 'fu.farm_id')
            ->join('su_organizations as o', 'o.id', '=', 'f.organization_id')
            ->whereRaw("su_satellite_orders.status <> '?'", ['canceled'])
            ->where('o.id', '%organization_id%')
            ->where('fu.is_visible', 'true')
            ->where('su_satellite_orders.year', '%year%')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_satellite_orders_old',
            'utf' => false,
            'maxextent' => false,
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$orderedPolygonsQuery}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . "",
            'gml_include_items' => ["gid"],
            'gid' => "gid",
            'transparency' => "100",
            'tag_label' => "area",
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'validation' => true,
            'params' => [
                [
                    "name" => "lang",
                    'regular_expression' => '[a-zA-Z]+'
                ],
                [
                    "name" => "organization_id",
                    'regular_expression' => '[0-9]+'
                ],
                [
                    "name" => "year",
                    'regular_expression' => '[0-9]+'
                ],
                [
                    "name" => "access_token",
                    'regular_expression' => '^.+*$' // match jwt
                ],
            ],
            'classes' => [
                [
                    "name" => "layer_satellite_order_ready",
                    "border_color" => '0 131 201',
                    "label_color" => '0 0 0',
                    "tags" => true,
                    "size" => 10,
                    "width" => 2,
                    "label_text" => "'[culture] - [area] {$areaLabel}'"
                ]
            ]
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_satellite_orders_old.map", 'w');
        fwrite($fp, $layerStr);
        fclose($fp);
    }

    private function generateSoilMapFile()
    {
        $defaultDb = Config::get('database.default');
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} dbname={$mainDB['database']} user={$mainDB['username']} password={$mainDB['password']} port={$mainDB['port']}";

        $data = [
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'main_db_connection' => $mainDBConnectionString,
            'base_path' => base_path(),
            'storage_path' => storage_path(),
            'machine' => Config::get('globals.MACHINE'),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'srid' => Config::get('globals.DEFAULT_DB_CRS')
        ];

        $mapStr = View::make('maps.static.soil', $data)->render();

        $mapStr = $this->layerSoilElement($mapStr);

        $mapFile = Config::get('globals.WMS_MAP_PATH') . "soil.map";
        @unlink($mapFile);
        $fp = fopen($mapFile, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function generateSoilMapFileOld()
    {

        $defaultDb = Config::get('database.default');
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} dbname={$mainDB['database']} user={$mainDB['username']} password={$mainDB['password']} port={$mainDB['port']}";

        $data = [
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'base_path' => base_path(),
            'main_db_connection' => $mainDBConnectionString,
            'storage_path' => storage_path(),
            'machine' => Config::get('globals.MACHINE'),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'srid' => Config::get('globals.DEFAULT_DB_CRS')
        ];

        $mapStr = View::make('maps.static.soil_old', $data)->render();

        $mapStr = $this->layerSoilElement($mapStr);

        $mapFile = Config::get('globals.WMS_MAP_PATH') . "soil_old.map";
        @unlink($mapFile);
        $fp = fopen($mapFile, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    /**
     * @param string $mapStr
     * @return string
     * @throws GuzzleException
     */
    private function layerSoilElement(string $mapStr): string
    {
        $serviceProviders = ServiceProvider::select('id', 'slug')->get()->toArray();
        $systemService = new SystemService(new Request());

        foreach ($serviceProviders as $provider) {
            $soilData = $systemService->getSoilMapElements($provider['id']);

            if (!$soilData) {
                continue;
            }

            foreach ($soilData as $elementName => $elementData) {
                $elementData = array_map(function ($data) {
                    $data['color'] = Helper::convertHexColorToRGB($data['color']);
                    return $data;
                }, $elementData);

                $layerName = "soil_by_element_" . $provider['slug'] . '_' . $elementName;
                $layerOpts = array(
                    'name' => $layerName,
                    'tile_index' => 'soil_by_element_tile_idx',
                    'scale' => $elementData,
                    'mask' => 'ordered_plots'
                );

                $mapStr .= View::make('map_soil_by_element_layer', $layerOpts)->render();
                $mapStr .= "\n";
            }
        }

        $mapStr .= "END\r\n";
        return $mapStr;
    }

    private function generateVraMapFile()
    {
        $defaultDb = Config::get('database.default');
        $data = [
            'base_path' => base_path(),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
        ];

        $mapStr = View::make('maps.static.vra', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'vra.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);

        $mapSoilStr = View::make('maps.static.vra_soil', $data)->render();

        $mapSoilFilePath = Config::get('globals.WMS_MAP_PATH') . 'vra_soil.map';
        @unlink($mapSoilFilePath);
        $fp = fopen($mapSoilFilePath, 'w');
        fwrite($fp, $mapSoilStr);
        fclose($fp);
    }

    private function generateImagesByDateMapFiles()
    {
        $defaultDb = Config::get('database.default');

        $data = [
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
        ];

        $mapStrRapideye = View::make('maps.static.rapideye-by-date', $data)->render();

        $mapFilePathRapideye = Config::get('globals.WMS_MAP_PATH') . 'rapideye_by_date.map';
        @unlink($mapFilePathRapideye);
        $fpRapideye = fopen($mapFilePathRapideye, 'w');
        fwrite($fpRapideye, $mapStrRapideye);
        fclose($fpRapideye);

        $mapStrSentinel = View::make('maps.static.sentinel-by-date', $data)->render();

        $mapFilePathSentinel = Config::get('globals.WMS_MAP_PATH') . 'sentinel_by_date.map';
        @unlink($mapFilePathSentinel);
        $fpSentinel = fopen($mapFilePathSentinel, 'w');
        fwrite($fpSentinel, $mapStrSentinel);
        fclose($fpSentinel);

        $mapStrLandsat = View::make('maps.static.landsat-by-date', $data)->render();

        $mapFilePathLandsat = Config::get('globals.WMS_MAP_PATH') . 'landsat_by_date.map';
        @unlink($mapFilePathLandsat);
        $fpLandsat = fopen($mapFilePathLandsat, 'w');
        fwrite($fpLandsat, $mapStrLandsat);
        fclose($fpLandsat);
    }

    private function generateShp2ImgMapFile()
    {
        $defaultDb = Config::get('database.default');
        $data = [];
        $data['wms_path'] = Config::get('globals.WMS_MAP_PATH');
        $data['public_path'] = public_path();
        $data['host'] = Config::get('database.connections.' . $defaultDb . '.host');
        $data['dbname'] = Config::get('database.connections.' . $defaultDb . '.database');
        $data['username'] = Config::get('database.connections.' . $defaultDb . '.username');
        $data['password'] = Config::get('database.connections.' . $defaultDb . '.password');
        $data['port'] = Config::get('database.connections.' . $defaultDb . '.port');
        $data['machine'] = Config::get('globals.MACHINE');
        $data['epsgProj'] = Config::get('globals.DEFAULT_DB_CRS');

        $layerStr = $this->layerShp2ImgSoilElement();
        $layerStr .= $this->layerPoints('shp2img_layer_soil_points', $defaultDb, "sample_id");

        $data['soil_layers'] = $layerStr;

        $mapStr = View::make('maps.static.shp2img', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'shp2img.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function layerShp2ImgSoilElement(): string
    {
        $layerStr = '';

        $serviceProviders = ServiceProvider::select('id', 'slug')->get()->toArray();
        $systemService = new SystemService(new Request());

        foreach ($serviceProviders as $provider) {
            $soilData = $systemService->getSoilMapElements($provider['id']);

            if (!$soilData) {
                continue;
            }

            foreach ($soilData as $elementName => $elementData) {
                $elementData = array_map(function ($data) {
                    $data['color'] = Helper::convertHexColorToRGB($data['color']);
                    return $data;
                }, $elementData);

                $layerName = "soil_by_element_" . $provider['slug'] . '_soil_' . $elementName;
                $layerOpts = array(
                    'name' => $layerName,
                    'scale' => $elementData,
                    'raster' => false,
                    'group' => false,
                );

                $layerStr .= View::make('map_soil_layer', $layerOpts)->render();
                $layerStr .= "\n";
            }
        }

        return $layerStr;
    }

    private function layerPoints($layerNamePoints, $defaultDb, $tagLabel)
    {

        $layerStr = '';
        $dataPoints = [
            'layername' => $layerNamePoints,
            'utf' => false,
            'type' => 'POINT',
            'group' => false,
            'maxextent' => false,
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'epsgProj' => '
                "proj=merc"
                "a=6378137"
                "b=6378137"
                "lat_ts=0.0"
                "lon_0=0.0"
                "x_0=0.0"
                "y_0=0"
                "k=1.0"
                "units=m"
                "nadgrids=@null"
                "wktext"
                "no_defs"
            ',
            'query' => "",
            'gid' => "gid",
            'transparency' => "100",
            'tag_label' => $tagLabel,
            'classitem' => false,
            'classes' => [
                [
                    "name" => $layerNamePoints . "_ready",
                    "symbol" => [
                        "name" => "circle",
                        "size" => "10",
                        "color" => "'#2aec09'",
                        "outlinecolor" => false,
                        "angle" => false,
                    ],
                    "border_color" => '0 131 201',
                    "label_color" => '0 0 0',
                    "tags" => true,
                    "size" => 10,
                    "width" => 2,
                    "label_text" => "'[" . $tagLabel . "]'"
                ]
            ]
        ];

        $layerStr .= View::make('maps.soil-grid', $dataPoints)->render();
        $layerStr .= "\n";
        $layerStr .= "
            SYMBOL
              NAME 'circle'
              TYPE ellipse
              FILLED false
              POINTS
                5 5
              END
            END\r\n";

        return $layerStr;
    }

    private function generatePlotsPreviewMapFile()
    {
        $defaultDb = Config::get('database.default');
        $data = [];
        $data['host'] = Config::get('database.connections.' . $defaultDb . '.host');
        $data['dbname'] = Config::get('database.connections.' . $defaultDb . '.database');
        $data['username'] = Config::get('database.connections.' . $defaultDb . '.username');
        $data['password'] = Config::get('database.connections.' . $defaultDb . '.password');
        $data['port'] = Config::get('database.connections.' . $defaultDb . '.port');
        $data['connection'] = Config::get('globals.WMS_SERVER') . '?map=' . Config::get('globals.WMS_MAP_PATH') . 'geo_scan.map&';

        $mapStr = View::make('maps.static.plot-preview', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'plot_preview.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function generateWaterPoundsMapFile()
    {

        $defaultDb = Config::get('database.default');
        $data = [];
        $data['base_path'] = base_path();
        $data['host'] = Config::get('database.connections.' . $defaultDb . '.host');
        $data['dbname'] = Config::get('database.connections.' . $defaultDb . '.database');
        $data['username'] = Config::get('database.connections.' . $defaultDb . '.username');
        $data['password'] = Config::get('database.connections.' . $defaultDb . '.password');
        $data['port'] = Config::get('database.connections.' . $defaultDb . '.port');
        $data['wms_server'] = Config::get('globals.WMS_SERVER');

        $mapStr = View::make('maps.static.water-pounds', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'water_pounds.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function generateIndexMapFile()
    {
        $defaultDb = Config::get('database.default');
        $data = [
            'base_path' => base_path(),
            'storage_path' => storage_path(),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'machine' => Config::get('globals.MACHINE'),
            'epsg' => Config::get('globals.DEFAULT_DB_CRS')
        ];

        $mapStr = View::make('maps.static.index', $data)->render();

        //Create Adjustable Layers
        $mapStr = $this->createAdjustableLayers($mapStr);

        $mapStr .= '
        END';

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'index.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function createAdjustableLayers($mapStr)
    {
        $elementClasses = Config::get('globals.ELEMENT_CLASSES');
        $originalClasses = $elementClasses['satellite']['NDVI_N'][10];

        $arrFirstClass = reset($originalClasses);
        $lowestLimitOfLowestClass = reset($arrFirstClass);

        $adjustableParams = Config::get('globals.INDEX_ADJUSTABLE_PARAMS');

        $arrAdjParams = json_decode($adjustableParams, true);

        $minValue = $arrAdjParams['min'];
        $maxValue = $arrAdjParams['max'];
        $step = $arrAdjParams['step'];

        $arrOriginalClasses = array_values($originalClasses);

        $arrDataAdj = [];
        for ($i = $minValue; $i <= $maxValue; $i = $i + $step) {
            $arrRecalc = $this->recalcАlgorithm($i, $originalClasses);

            $arrClasses = [];
            foreach ($arrRecalc as $key => $value) {

                if ($key == $lowestLimitOfLowestClass) {
                    $arrClasses[] = [
                        'firstRange' => $key,
                        'secondRange' => $value,
                        'colorFirst' => $arrOriginalClasses[$key][2],
                        'colorSecond' => $arrOriginalClasses[$key + 1][2],
                    ];
                    continue;
                }

                $colorSecond = $arrOriginalClasses[$key][2];
                if (isset($arrOriginalClasses[$key + 1])) {
                    $colorSecond = $arrOriginalClasses[$key + 1][2];
                }

                $arrClasses[] = [
                    'firstRange' => $arrRecalc[$key - 1],
                    'secondRange' => $arrRecalc[$key],
                    'colorFirst' => $arrOriginalClasses[$key][2],
                    'colorSecond' => $colorSecond,
                ];
            }

            $sliderValue = '_' . abs($i) . '_';
            $sign = 'plus';
            if ($i < 0) {
                $sign = 'minus';
            }

            $layerName = 'adjustable_index' . $sliderValue . $sign;

            $arrDataAdj[] = [
                'layer_name' => $layerName,
                'classes' => $arrClasses,
            ];
        }

        foreach ($arrDataAdj as $key => $arrData) {
            $mapStr .= View::make('maps.static.index-adjustable-layers', $arrData)->render();
        }

        return $mapStr;
    }

    private function recalcАlgorithm($currValue, $originalClasses)
    {

        $arrOut = [];
        switch (true) {
            case ($currValue > 0):
                //горната граница на най-ниския клас (ex. 10+40=50)
                $arrHighetClass = end($originalClasses);
                $highetLimitOfHighetClass = $arrHighetClass[1];

                $arrFirstClass = reset($originalClasses);
                $start = reset($arrFirstClass);

                $highestLimitOfLowestClass = $arrFirstClass[1];
                $changedValue = $highestLimitOfLowestClass + $currValue;

                $step = ($highetLimitOfHighetClass - $changedValue) / (count($originalClasses) - 1);

                foreach ($originalClasses as $key => $value) {
                    if ($start == $key) {
                        $arrOut[] = $changedValue;
                        $start = $changedValue;
                        continue;
                    }

                    $nextValue = $start + $step;
                    $arrOut[] = $nextValue;

                    $start = $nextValue;
                }
                break;
            case ($currValue < 0):
                //долната граница на най-високия клас (ex. 100-20=80)
                $arrFirstClass = reset($originalClasses);
                $start = reset($arrFirstClass);

                $arrHighetClass = end($originalClasses);

                $lowestLimitOfHighetClass = reset($arrHighetClass);
                $highetLimitOfHighetClass = $arrHighetClass[1];

                $changedValue = $lowestLimitOfHighetClass + $currValue;

                $step = $changedValue / (count($originalClasses) - 1);

                foreach ($originalClasses as $key => $value) {
                    $nextValue = $start + $step;

                    if ($key == $lowestLimitOfHighetClass) {
                        $nextValue = $highetLimitOfHighetClass;
                    }

                    $arrOut[] = $nextValue;

                    $start = $nextValue;
                }
                break;
            default:
                //when $currValue is 0
                foreach ($originalClasses as $key => $value) {
                    $arrOut[] = $value[1];
                }

                break;
        }

        return $arrOut;
    }

    private function generateSoilGridMapFile()
    {
        $defaultDb = Config::get('database.default');
        $data = [
            'base_path' => base_path(),
            'storage_path' => storage_path(),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'espg' => Config::get('globals.DEFAULT_DB_CRS'),
        ];


        $mapStr = View::make('maps.static.soil-grid', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'soil-grid.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function generateLayerCmsPlotsMapFile()
    {
        $defaultDb = Config::get('database.default');

        $data = [
            'base_path' => base_path(),
            'wms_server' => Config::get('globals.WMS_SERVER'),
            'host' => Config::get('database.connections.' . $defaultDb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultDb . '.database'),
            'username' => Config::get('database.connections.' . $defaultDb . '.username'),
            'password' => Config::get('database.connections.' . $defaultDb . '.password'),
            'port' => Config::get('database.connections.' . $defaultDb . '.port'),
            'srid' => Config::get('globals.DEFAULT_DB_CRS'),
        ];

        $mapStr = View::make('maps.static.layer-cms-plots', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'layer_cms_plots.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function generateLayerTileIdx()
    {
        {
            $mainDB = Config::get('database.connections.main');
            $mainDBConnectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

            $tileIdxQuery = User::selectRaw("
                sof.path as location,
                ST_Force2d(st_transform(ST_SetSRID(st_extent(ssp.geom), Find_SRID('public', 'su_satellite_plots', 'geom')), 3857)) as geom
            ")
                ->join(
                    DB::raw("dblink('" . $mainDBConnectionString . "', $$
                                select su.id, su.old_id 
                                from su_users su
                                join oauth_access_tokens as oat on su.id = oat.user_id 
                                where oat.id = (select (payload::json)->>'jti' as global_user_id from verify('%access_token%', 'secret'))
                                $$) as global_user(id int, old_id int)"), 'global_user.old_id', '=', 'su_users.id')
                ->join('su_farms_users as sfu', 'sfu.user_id', '=', 'su_users.id')
                ->join('su_satellite_orders AS sso', function ($join) {
                    $join->on('sso.organization_id', '=', 'su_users.last_chosen_organization_id')
                        ->on('sso.type', '=', "'index'")
                        ->on('sso.status', '=', "'processed'")
                        ->on('sso.year', '=', '%year%');
                })
                ->join('su_satellite_orders_plots_rel as ssopr', 'ssopr.order_id', '=', 'sso.id')
                ->join('su_satellite_orders_files AS sof', function ($join) {
                    $join->on('sof.order_id', '=', 'sso.id')
                        ->on('sof.date', '=', "'%date%'")
                        ->on('sof.type', '=', "'TIFF'")
                        ->on('sof.satellite_type ', '=', "'sentinel'")
                        ->on('sof.layer_type', '=', "'%type%'");
                })
                ->join('su_satellite_plots AS ssp', function ($join) {
                    $join->on('ssp.gid', '=', 'ssopr.plot_id')
                        ->on('sfu.farm_id', '=', 'ssp.farm_id');
                })
                ->groupBy('sof.path')
                ->toSqlWithBindings();

            $defaultdb = Config::get('database.default');

            $data = [
                'layername' => 'layer_tile_idx',
                'utf' => false,
                'maxextent' => false,
                'host' => Config::get('database.connections.' . $defaultdb . '.host'),
                'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
                'username' => Config::get('database.connections.' . $defaultdb . '.username'),
                'password' => Config::get('database.connections.' . $defaultdb . '.password'),
                'port' => Config::get('database.connections.' . $defaultdb . '.port'),
                'query' => "({$tileIdxQuery}) AS subquery USING UNIQUE geom USING srid=3857",
                'gml_include_items' => ["geom"],
                'transparency' => "100",
                'epsg_code' => 3857,
                'tag_label' => "location",
                'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
                'geo_pole' => Config::get('globals.GEO_POLE'),
                'validation' => true,
                'params' => [
                    [
                        "name" => "date",
                        'regular_expression' => '[0-9-]+'
                    ],
                    [
                        "name" => "access_token",
                        'regular_expression' => '^.+*$' // match jwt
                    ],
                    [
                        "name" => "farm_ids",
                        'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$' // match comma separated numbers
                    ],
                    [
                        "name" => "default_farm_ids",
                        'regular_expression' => 'null'
                    ],
                    [
                        "name" => "plot_ids",
                        'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$' // match comma separated numbers
                    ],
                    [
                        "name" => "default_plot_ids",
                        'regular_expression' => 'null'
                    ],
                    [
                        "name" => "crop_ids",
                        'regular_expression' => '^\[([0-9]+|null)*((,|,\s)([0-9]+|null))*\]$' // match array, example '[123, null, 456]'
                    ],
                    [
                        "name" => "default_crop_ids",
                        'regular_expression' => '[]'
                    ],
                    [
                        "name" => "year",
                        'regular_expression' => '[0-9]+'
                    ],
                    [
                        "name" => "type",
                        'regular_expression' => '[a-zA-Z]+'
                    ],
                ],
                'classes' => [
                    [
                        "name" => "layer_tile_idx_ready",
                        "tags" => false
                    ]
                ]
            ];

            $layerStr = View::make('maps.layer', $data)->render();

            @unlink(Config::get('globals.WMS_MAP_PATH') . "layer_tile_idx.map");
            $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_tile_idx.map", 'w');
            fwrite($fp, $layerStr);
            fclose($fp);
        }
    }

    private function generateLayerTileIdxMask()
    {
        {
            $mainDB = Config::get('database.connections.main');
            $mainDBConnectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

            $tileIdxQuery = User::selectRaw("
                sslp.plot_id as plot_gid,
                ST_Force_2d(st_transform(ST_SetSRID(ssp.geom, Find_SRID('public', 'su_satellite_plots', 'geom')), 3857)) as geom
            ")
                ->join(
                    DB::raw("dblink('" . $mainDBConnectionString . "', $$
                                select su.id, su.old_id 
                                from su_users su
                                join oauth_access_tokens as oat on su.id = oat.user_id 
                                where oat.id = (select (payload::json)->>'jti' as global_user_id from verify('%access_token%', 'secret'))
                                $$) as global_user(id int, old_id int)"), 'global_user.old_id', '=', 'su_users.id')
                ->join('su_farms_users as sfu', 'sfu.user_id', '=', 'su_users.id')
                ->join('su_satellite_orders AS sso', function ($join) {
                    $join->on('sso.organization_id', '=', 'su_users.last_chosen_organization_id')
                        ->on('sso.type', '=', "'index'")
                        ->on('sso.status', '=', "'processed'")
                        ->on('sso.year', '=', '%year%');
                })
                ->join('su_satellite_orders_plots_rel as ssopr', 'ssopr.order_id', '=', 'sso.id')
                ->join('su_satellite_plots AS ssp', function ($join) {
                    $join->on('ssp.gid', '=', 'ssopr.plot_id')
                        ->on('sfu.farm_id', '=', 'ssp.farm_id');
                })
                ->join('su_satellite_layers_plots AS sslp', function ($join) {
                    $join->on('sslp.order_id', '=', 'sso.id')
                        ->on('sslp.type', '=', "'%type%'")
                        ->on('sslp.date', '=', "'%date%'")
                        ->on('sslp.plot_id', '=', 'ssopr.plot_id')
                        ->on('sslp.satellite_type ', '=', "'sentinel'");
                })
                ->join('su_satellite_plots_crops AS sspc', function ($join) {
                    $join->on('sspc.plot_id', '=', 'ssp.gid')
                        ->on('sspc.year', '=', '%year%');
                })
                ->where('sfu.is_visible', '=', 'true')
                ->where(function ($query) {
                    $query->where('sspc.is_primary', 'true');
                    $query->orWhere('sspc.is_primary', null);
                })
                ->where(function ($query) {
                    $query->whereNull(DB::raw('COALESCE(%farm_ids%)'));
                    $query->orWhereIn('ssp.farm_id', ['%farm_ids%']);
                })
                ->where(function ($query) {
                    $query->whereNull(DB::raw('COALESCE(%plot_ids%)'));
                    $query->orWhereIn('ssp.gid', ['%plot_ids%']);
                })
                ->where(function ($query) {
                    $query->where('sspc.crop_id', DB::raw('any(array %crop_ids%::integer[])'));
                    $query->orWhere(DB::raw('array_length(array %crop_ids%::integer[], 1)'), null);
                    $query->orWhere(DB::raw('(
                        SELECT bool_or(a is null)
                        FROM unnest(array %crop_ids%::integer[]) s(a)
                        WHERE sspc.crop_id = any(array %crop_ids%::integer[])
                        OR sspc.crop_id is null
                    )'), 'true');
                })
                ->toSqlWithBindings();

            $defaultdb = Config::get('database.default');

            $data = [
                'layername' => 'layer_tile_idx_mask',
                'utf' => false,
                'maxextent' => false,
                'host' => Config::get('database.connections.' . $defaultdb . '.host'),
                'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
                'username' => Config::get('database.connections.' . $defaultdb . '.username'),
                'password' => Config::get('database.connections.' . $defaultdb . '.password'),
                'port' => Config::get('database.connections.' . $defaultdb . '.port'),
                'query' => "({$tileIdxQuery}) AS subquery USING UNIQUE geom USING srid=3857",
                'gml_include_items' => ["geom"],
                'transparency' => "100",
                'epsg_code' => 3857,
                'tag_label' => "plot_gid",
                'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
                'geo_pole' => Config::get('globals.GEO_POLE'),
                'validation' => true,
                'params' => [
                    [
                        "name" => "date",
                        'regular_expression' => '[0-9-]+'
                    ],
                    [
                        "name" => "access_token",
                        'regular_expression' => '^.+*$' // match jwt
                    ],
                    [
                        "name" => "farm_ids",
                        'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$' // match comma separated numbers
                    ],
                    [
                        "name" => "default_farm_ids",
                        'regular_expression' => 'null'
                    ],
                    [
                        "name" => "plot_ids",
                        'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$' // match comma separated numbers
                    ],
                    [
                        "name" => "default_plot_ids",
                        'regular_expression' => 'null'
                    ],
                    [
                        "name" => "crop_ids",
                        'regular_expression' => '^\[([0-9]+|null)*((,|,\s)([0-9]+|null))*\]$' // match array, example '[123, null, 456]'
                    ],
                    [
                        "name" => "default_crop_ids",
                        'regular_expression' => '[]'
                    ],
                    [
                        "name" => "year",
                        'regular_expression' => '[0-9]+'
                    ],
                    [
                        "name" => "type",
                        'regular_expression' => '[a-zA-Z]+'
                    ],

                ],
                'classes' => [
                    [
                        "name" => "layer_tile_idx_mask_ready",
                        "color" => '0 0 0',
                        "tags" => false
                    ]
                ]
            ];

            $layerStr = View::make('maps.layer', $data)->render();

            @unlink(Config::get('globals.WMS_MAP_PATH') . "layer_tile_idx_mask.map");
            $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_tile_idx_mask.map", 'w');
            fwrite($fp, $layerStr);
            fclose($fp);
        }
    }

    private function generateIndexLayerFile()
    {
        $data = [];

        $mapStr = View::make('maps.static.layer_index', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'layer_index.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function generateIndexWaterLayerFile()
    {
        $data = [];

        $mapStr = View::make('maps.static.layer_index_water', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'layer_index_water.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }


    private function generateLayerUploadPlotsBoundaries()
    {
        $data = [
            'srid' => Config::get('globals.DEFAULT_DB_CRS'),
        ];

        $mapStr = View::make('maps.static.layer_upload_plots_boundaries', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'layer_upload_plots_boundaries.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function generateTmpPlotsBoundaryLayerFile()
    {
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

        $query = DB::table('tmp_satellite_%file_id%')
            ->select('gid', 'geom')
            ->leftJoin(
                DB::raw("dblink('" . $mainDBConnectionString . "', $$
                select su.id, su.old_id 
                from su_users su
                join oauth_access_tokens as oat on su.id = oat.user_id 
                where oat.id = (select (payload::json)->>'jti' as global_user_id from verify('%access_token%', 'secret'))
            $$) as global_user(id int, old_id int)"), DB::raw('true'), DB::raw('true'))
            ->whereRaw('ST_IsValid(geom)')
            ->whereNotNull('global_user.id')
            ->toSql();
        $query = str_replace(['%', '?', '"'], ['%', '%s', ''], $query);

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_tmp_plots_boundary',
            'utf' => false,
            'maxextent' => false,
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$query}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . "",
            'gml_include_items' => ["geom"],
            'transparency' => "100",
            'tag_label' => "",
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'validation' => true,
            'params' => [
                [
                    "name" => "file_id",
                    'regular_expression' => '[0-9]+'
                ],
                [
                    "name" => "access_token",
                    'regular_expression' => '^.+*$' // match jwt
                ]
            ],
            'classes' => [
                [
                    "name" => "for_tmp_plots_boundary",
                    "border_color" => '255 220 46',
                    "color" => '255 220 46',
                    "opacity" => '70',
                    "tags" => false
                ]
            ]
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_upload_plots_boundaries.map", 'a');
        fwrite($fp, $layerStr);
        fclose($fp);
    }

    private function generateTmpPlotsBoundaryOverlapsLayerFile()
    {
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

        $query = DB::table('tmp_satellite_%file_id% AS tmp')
            ->selectRaw('coalesce (ST_Intersection(tmp.geom, ssp.geom), ST_Intersection(tmp.geom, tmp_copy.geom)) as geom')
            ->leftJoin(
                DB::raw("dblink('" . $mainDBConnectionString . "', $$
                select su.id, su.old_id 
                from su_users su
                join oauth_access_tokens as oat on su.id = oat.user_id 
                where oat.id = (select (payload::json)->>'jti' as global_user_id from verify('%access_token%', 'secret'))
            $$) as global_user(id int, old_id int)"), DB::raw('true'), DB::raw('true'))
            ->leftJoin('tmp_satellite_%file_id% AS tmp_copy', function ($join) {
                $join->on(DB::raw('ST_Touches(tmp.geom, tmp_copy.geom)'), DB::raw('false::boolean'))
                    ->on(DB::raw('ST_Intersects(tmp.geom, tmp_copy.geom)'), DB::raw('true::boolean'))
                    ->on('tmp.gid', '<>', 'tmp_copy.gid');
            })
            ->leftJoin('su_farms as sf', 'sf.organization_id', '=', DB::raw('%organization_id%'))
            ->leftJoin('su_satellite_plots AS ssp', function ($join) {
                $join->on(DB::raw('ST_Touches(tmp.geom, ssp.geom)'), DB::raw('false::boolean'))
                    ->on(DB::raw('ST_Intersects(tmp.geom, ssp.geom)'), DB::raw('true::boolean'))
                    ->on('ssp.farm_id', '=', 'sf.id');
            })
            ->whereNotNull('global_user.id')
            ->whereRaw('ST_IsValid(tmp.geom)')
            ->whereRaw('(ssp.gid notnull OR tmp_copy.gid notnull)')
            ->orderBy('tmp.gid', 'sf.id', 'ssp.gid desc nulls last', 'tmp_copy.gid desc nulls last')
            ->toSql();

        $query = str_replace(['%', '?', '"'], ['%', '%s', ''], $query);

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_tmp_plots_boundary_overlaps',
            'utf' => false,
            'maxextent' => false,
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$query}) AS subquery USING UNIQUE geom USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . "",
            'gml_include_items' => ["geom"],
            'transparency' => "100",
            'tag_label' => "",
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'validation' => true,
            'params' => [
                [
                    "name" => "file_id",
                    'regular_expression' => '[0-9]+'
                ],
                [
                    "name" => "organization_id",
                    'regular_expression' => '[0-9]+'
                ],
                [
                    "name" => "access_token",
                    'regular_expression' => '^.+*$' // match jwt
                ]
            ],
            'classes' => [
                [
                    "name" => "for_tmp_plots_boundary_overlaps",
                    "border_color" => '255 102 102',
                    "color" => '255 102 102',
                    "opacity" => '50',
                    "tags" => false
                ]
            ]
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_upload_plots_boundaries.map", 'a');
        fwrite($fp, $layerStr);
        fclose($fp);
    }

    private function generateExistingPlotsBoundaryLayerFile()
    {
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

        $query = Plot::select('su_satellite_plots.gid', 'su_satellite_plots.geom', 'su_satellite_plots.name')
            ->join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->join(
                DB::raw("dblink('" . $mainDBConnectionString . "', $$
                select su.id, su.old_id 
                from su_users su
                join oauth_access_tokens as oat on su.id = oat.user_id 
                where oat.id = (select (payload::json)->>'jti' as global_user_id from verify('%access_token%', 'secret'))
            $$) as global_user(id int, old_id int)"), 'global_user.old_id', '=', 'su.id')
            ->join('su_farms AS f', 'f.id', '=', 'sfu.farm_id')
            ->where("f.organization_id", '=', '%organization_id%')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => 'layer_existing_plots_boundary',
            'utf' => false,
            'maxextent' => false,
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$query}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . "",
            'gml_include_items' => ["geom", "name"],
            'transparency' => "100",
            'tag_label' => "name",
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'validation' => true,
            'params' => [
                [
                    "name" => "file_id",
                    'regular_expression' => '[0-9]+'
                ],
                [
                    "name" => "access_token",
                    'regular_expression' => '^.+*$' // match jwt
                ],
                [
                    "name" => "organization_id",
                    'regular_expression' => '[0-9]+'
                ]
            ],
            'classes' => [
                [
                    "name" => "for_existing_plots_boundary",
                    "border_color" => '218 232 252',
                    "color" => '218 232 252',
                    "opacity" => '70',
                    "tags" => false
                ]
            ]
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_upload_plots_boundaries.map", 'a');
        fwrite($fp, $layerStr);
        fclose($fp);
    }

    private function generateLayerEditPlotsBoundaries()
    {
        $data = [
            'srid' => Config::get('globals.DEFAULT_DB_CRS'),
        ];

        $mapStr = View::make('maps.static.layer_edit_plots_boundaries', $data)->render();

        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'layer_edit_plots_boundaries.map';
        @unlink($mapFilePath);
        $fp = fopen($mapFilePath, 'w');
        fwrite($fp, $mapStr);
        fclose($fp);

        $this->generateLayerEditableNotEditablePlotsBoundary('layer_editable_plots_boundary', 'true', '218 232 252');
        $this->generateLayerEditableNotEditablePlotsBoundary('layer_not_editable_plots_boundary', 'false', '248 206 204');
    }

    private function generateLayerEditableNotEditablePlotsBoundary(string $layerName, string $isEditablePlot, $styleColor)
    {
        $mainDB = Config::get('database.connections.main');
        $mainDBConnectionString = "host={$mainDB['host']} port={$mainDB['port']} user={$mainDB['username']} dbname={$mainDB['database']} password={$mainDB['password']}";

        $query = Plot::select("su_satellite_plots.gid", "su_satellite_plots.geom")
            ->join('su_farms_users AS sfu', 'sfu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_users AS su', 'su.id', '=', 'sfu.user_id')
            ->join(
                DB::raw("dblink('" . $mainDBConnectionString . "', $$
                select su.id, su.old_id
                from su_users su
                join oauth_access_tokens as oat on su.id = oat.user_id
                where oat.id = (select (payload::json)->>'jti' as global_user_id from verify('%access_token%', 'secret'))
            $$) as global_user(id int, old_id int)"), 'global_user.old_id', '=', 'su.id')
            ->join('su_farms AS sf', 'sf.id', '=', 'su_satellite_plots.farm_id')
            ->where('sf.organization_id', '=', '%organization_id%')
            ->where('su_satellite_plots.is_editable', '=', $isEditablePlot)
            ->whereBetween(DB::raw("su_satellite_plots.upload_date::date"), ["'%start_date%'", "'%end_date%'"])
            ->where(function ($query) {
                $query->whereNull(DB::raw('COALESCE(%farm_ids%)'));
                $query->orWhereIn('su_satellite_plots.farm_id', ['%farm_ids%']);
            })
            ->groupBy('su_satellite_plots.gid')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');

        $data = [
            'layername' => $layerName,
            'utf' => false,
            'maxextent' => false,
            'host' => Config::get('database.connections.' . $defaultdb . '.host'),
            'dbname' => Config::get('database.connections.' . $defaultdb . '.database'),
            'username' => Config::get('database.connections.' . $defaultdb . '.username'),
            'password' => Config::get('database.connections.' . $defaultdb . '.password'),
            'port' => Config::get('database.connections.' . $defaultdb . '.port'),
            'query' => "({$query}) AS subquery USING UNIQUE gid USING srid=" . Config::get('globals.DEFAULT_DB_CRS') . "",
            'gml_include_items' => ["geom"],
            'transparency' => "100",
            'tag_label' => "",
            'utm_zone' => Config::get('globals.DEFAULT_UTM_ZONE'),
            'geo_pole' => Config::get('globals.GEO_POLE'),
            'validation' => true,
            'params' => [
                [
                    "name" => "access_token",
                    'regular_expression' => '^.+*$' // match jwt
                ],
                [
                    "name" => "organization_id",
                    'regular_expression' => '[0-9]+'
                ],
                [
                    "name" => "start_date",
                    'regular_expression' => '[0-9-]+'
                ],
                [
                    "name" => "end_date",
                    'regular_expression' => '[0-9-]+'
                ],
                [
                    "name" => "farm_ids",
                    'regular_expression' => '^[0-9]+((,|,\s)[0-9]+)*|null$' // match comma separated numbers
                ],
                [
                    "name" => "default_farm_ids",
                    'regular_expression' => 'null'
                ],
            ],
            'classes' => [
                [
                    "name" => "for_" . $layerName,
                    "border_color" => $styleColor,
                    "color" => $styleColor,
                    "opacity" => '70',
                    "tags" => false
                ]
            ]
        ];

        $layerStr = View::make('maps.layer', $data)->render();

        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_edit_plots_boundaries.map", 'a');
        fwrite($fp, $layerStr);
        fclose($fp);
    }
}
