<?php

namespace App\Events;

use App\Events\Event;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class PushNotificationEvent extends Event
{
    use SerializesModels;

    public $notyId;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($notyId)
    {
        $this->notyId = $notyId;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }
}
