<?php

namespace App\Events;

use Config;
use DB;
use View;
use App\Events\Event;
use App\Models\StaticModels\FarmingYear;
use App\Models\Order;
use App\Models\Plot;

use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class GenerateWorkLayerMapFileEvent implements IGeneratable
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {
        $areaCoef = Config::get('globals.M2_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $sql = Plot::selectRaw("su_satellite_plots.gid, su_satellite_plots.geom, round((ST_Area(su_satellite_plots.geom)*{$areaCoef})::numeric, 3) || ' {$areaLabel}' AS area, su_satellite_plots.name")
            ->leftJoin('su_satellite_orders_plots_rel as sopr', 'sopr.plot_id', '=', 'su_satellite_plots.gid')
            ->leftJoin('su_satellite_orders AS so', function ($join) {
                $join->on('so.id', '=', 'sopr.order_id')
                    ->where('so.status', '<>', DB::raw("'canceled'"));
            })
            ->join('su_farms_users as fu', 'fu.farm_id', '=', 'su_satellite_plots.farm_id')
            ->join('su_farms as f', 'f.id', '=', 'fu.farm_id')
            ->join('su_organizations as o', 'o.id', '=', 'f.organization_id')
            ->where('fu.user_id', '%user_id%')
            ->where('o.id', '%organization_id%')
            ->where('fu.is_visible', 'TRUE')
            ->groupBy('gid')
            ->havingRaw('MAX(so.id) IS NULL')
            ->toSqlWithBindings();

        $defaultdb = Config::get('database.default');        

        $data = [];
        $data['layername'] = 'layer_satellite_work';
        $data['utf'] = false;
        $data['maxextent'] = false;
        $data['host'] = Config::get('database.connections.' . $defaultdb . '.host');
        $data['dbname'] = Config::get('database.connections.' . $defaultdb . '.database');
        $data['username'] = Config::get('database.connections.' . $defaultdb . '.username');
        $data['password'] = Config::get('database.connections.' . $defaultdb . '.password');
        $data['port'] = Config::get('database.connections.' . $defaultdb . '.port');
        $data['gml_include_items'] = ["gid"];
        $data["transparency"] = 100;
        $data['classes'][0]["name"] = 'layer_satellite_work';
        $data['query'] = "({$sql}) AS subquery USING UNIQUE gid USING srid=".Config::get('globals.DEFAULT_DB_CRS')."";
        $data["tag_label"] = 'area';
        $data['classes'][0]["tags"] = true;
        $data['classes'][0]["size"] = 10;
        $data['classes'][0]["border_color"] = '73 10 61';
        $data['classes'][0]["border_only"] = true;
        $data['classes'][0]["color"] = '';
        $data['epsg_code'] = Config::get('globals.DEFAULT_DB_CRS');
        $data['utm_zone'] = Config::get('globals.DEFAULT_UTM_ZONE');
        $data['geo_pole'] = Config::get('globals.GEO_POLE');
        $data['validation'] = true;
        $data['params'][0]["name"] = 'user_id';
        $data['params'][0]["regular_expression"] = '[0-9]+';
        $data['params'][1]["name"] = 'organization_id';
        $data['params'][1]["regular_expression"] = '[0-9]+';

        $layerStr = View::make('maps.layer', $data)->render();

        @unlink(Config::get('globals.WMS_MAP_PATH') . "layer_satellite_work.map");
        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_satellite_work.map", 'w');
        fwrite($fp, $layerStr);
        fclose($fp);
    }
}
