<?php

namespace App\Events;

use Config;
use DB;
use View;
use App\Events\Event;
use App\Models\StaticModels\FarmingYear;
use App\Models\Order;
use App\Models\Plot;

use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class GenerateAllPlotsLayerMapFileEvent implements IGeneratable
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {

        $mapStr = "MAP\r\n";
        
        $mapStr .= $this->getMapHead();
        $mapStr .= "\r\n";

        $areaCoef = Config::get('globals.M2_' . Config::get('globals.AREA_UNIT'));
        $areaLabel = Config::get('globals.AREA_UNIT_LABEL');

        $sql = "select su_satellite_plots.gid, su_satellite_plots.geom, round((ST_Area(su_satellite_plots.geom)*%m2_coef%)::numeric, 3) || ' ' || '%area_unit_label%'  AS area, su_satellite_plots.name from su_satellite_plots 
        join su_farms_users on su_farms_users.farm_id = su_satellite_plots.farm_id
        join su_farms on su_farms.id = su_farms_users.farm_id
        join su_organizations on su_organizations.id = su_farms.organization_id
                where su_farms_users.user_id = %user_id% AND su_organizations.id = %organization_id% AND su_farms_users.is_visible = TRUE AND su_satellite_plots.upload_date::DATE >= '%from_date%' AND su_satellite_plots.upload_date::DATE <= '%to_date%'";

        $defaultdb = Config::get('database.default');        

        $data = [];
        $data['layername'] = 'layer_all_plots';
        $data['utf'] = false;
        $data['maxextent'] = false;
        $data['host'] = Config::get('database.connections.' . $defaultdb . '.host');
        $data['dbname'] = Config::get('database.connections.' . $defaultdb . '.database');
        $data['username'] = Config::get('database.connections.' . $defaultdb . '.username');
        $data['password'] = Config::get('database.connections.' . $defaultdb . '.password');
        $data['port'] = Config::get('database.connections.' . $defaultdb . '.port');
        $data['gml_include_items'] = ["gid"];
        $data["transparency"] = 100;
        $data['classes'][0]["name"] = 'layer_all_plots';
        $data['query'] = "({$sql}) AS subquery USING UNIQUE gid USING srid=".Config::get('globals.DEFAULT_DB_CRS')."";
        $data["tag_label"] = 'area';

        $data['validation'] = true;            
        $data['params'][0]["name"] = 'user_id';
        $data['params'][0]["regular_expression"] = '[0-9]+';
        $data['params'][1]["name"] = 'from_date';
        $data['params'][1]["regular_expression"] = '[0-9\-]+';
        $data['params'][2]["name"] = 'to_date';
        $data['params'][2]["regular_expression"] = '[0-9\-]+';
        $data['params'][3]["name"] = 'm2_coef';
        $data['params'][3]["regular_expression"] = '[0-9]+';
        $data['params'][4]["name"] = 'area_unit_label';
        $data['params'][4]["regular_expression"] = '[а-Яa-zA-Z]+';
        $data['params'][5]["name"] = 'organization_id';
        $data['params'][5]["regular_expression"] = '[0-9]+';

        $data['classes'][0]["tags"] = true;
        $data['classes'][0]["size"] = 10;
        $data['classes'][0]["border_color"] = '244 67 54';
        $data['classes'][0]["border_only"] = true;
        $data['classes'][0]["color"] = '';
        $data['epsg_code'] = Config::get('globals.DEFAULT_DB_CRS');
        $data['utm_zone'] = Config::get('globals.DEFAULT_UTM_ZONE');
        $data['geo_pole'] = Config::get('globals.GEO_POLE');

        $mapStr .= View::make('maps.layer', $data)->render();

        $mapStr .= "END";

        @unlink(Config::get('globals.WMS_MAP_PATH') . "layer_all_plots.map");
        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "layer_all_plots.map", 'w');
        $res = fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function getMapHead() {
        
        $data['wmsserver'] = Config::get('globals.WMS_SERVER');
        $data['wmssrs'] = Config::get('globals.WMS_SRS');
        $data['wfsserver'] = Config::get('globals.WMS_SERVER');
        $data['wfssrs'] = Config::get('globals.WMS_SRS');
        $data['epsg_code'] = Config::get('globals.DEFAULT_DB_CRS');
        $data['utm_zone'] = Config::get('globals.DEFAULT_UTM_ZONE');
        $data['geo_pole'] = Config::get('globals.GEO_POLE');
        
        return View::make('maps.head', $data)->render();
    }
}
