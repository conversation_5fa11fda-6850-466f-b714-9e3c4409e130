<?php

namespace App\Events;

use App\Helpers\Helper;
use App\Models\Organization;
use App\Models\PlotFile;
use Config;
use DB;
use View;
use App\Models\Plot;

use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class GeneratePlotImgPreviewEvent implements IGeneratable
{
    use SerializesModels;

    static $width       = 93;
    static $height      = 70;
    static $coverWidth  = 320;
    static $coverHeight = 240;

    /**
     * GenerateFieldImgPreviewEvent constructor.
     * @param $farmId
     * @param array $gids Fields ids to generate preview for.
     */
    public function __construct(Organization $organization, Array $gids = null)
    {
        $this->organization = $organization;
        $this->gids = $gids;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {
        $farmIds = [];
        foreach ($this->organization->farms as $farm) {
            $farmIds[] = $farm->id;
        }


        $q = Plot::select('gid', 'farm_id',
            DB::raw('ST_Extent(ST_Transform(ST_Buffer(geom, 75), 900913)) AS extent')
        )
            ->whereIn('farm_id', $farmIds)
            ->groupBy('gid');

        if ($this->gids !== null) {
            $q->whereIn('gid', $this->gids);
        }

        $plots = $q->get();

        $plots->each(function ($plot) {
            $this->makePlotPng($plot);
        });
    }

    /**
     * Creates an RGB Png file from the shp2img.map file.
     * @param  object $plot
     */
    private function makePlotPng($plot)
    {
        $extent = Helper::toOpenLayerFormat($plot->extent);
        $outDir = storage_path('plots' . DIRECTORY_SEPARATOR . Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . $plot->farm_id . DIRECTORY_SEPARATOR);
        if (!is_dir($outDir)) {
            mkdir($outDir, 0755, true);
        }
        $outName = $outDir . "plot_" . $plot->gid . ".png";
        $coverOutName = $outDir . "plot_cover_" . $plot->gid . ".png";

        $width = self::$width;
        $height = self::$height;
        $coverWidth = self::$coverWidth;
        $coverHeight = self::$coverHeight;

        $parcelsQuery = "\"geom FROM (SELECT gid, ST_Transform(geom, 900913) AS geom FROM su_satellite_plots WHERE gid={$plot->gid}) AS subquery USING UNIQUE gid USING srid=900913\"";
        $mapFilePath = Config::get('globals.WMS_MAP_PATH') . 'plot_preview.map';
        $parcelsLayer = 'plot_preview';

        $shp2img = Config::get('globals.SHP2IMG');
        $command = "{$shp2img} -m {$mapFilePath} -l \"{$parcelsLayer}\" -s {$width} {$height} -e {$extent} -d \"{$parcelsLayer}\" {$parcelsQuery}";
        $command .= " -o \"{$outName}\"";
        ;
        $output = array();
        exec($command . " 2>&1", $output);

        $coverCommand = "{$shp2img} -m {$mapFilePath} -l \"{$parcelsLayer}\" -s {$coverWidth} {$coverHeight} -e {$extent} -d \"{$parcelsLayer}\" {$parcelsQuery}";
        $coverCommand .= " -o \"{$coverOutName}\"";

        $coverOutput = array();
        exec($coverCommand . " 2>&1", $coverOutput);

        //insert the $outName at DB
        $plotFile = new PlotFile();
        $plotFile->plot()->associate($plot);
        $plotFile->type = PlotFile::TYPE_PLOT;
        $plotFile->path = $outName;
        $plotFile->web_path = Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . $plot->farm_id . DIRECTORY_SEPARATOR . "plot_" . $plot->gid . ".png";
        $plotFile->save();

        $plotCoverFile = new PlotFile();
        $plotCoverFile->plot()->associate($plot);
        $plotCoverFile->type = PlotFile::TYPE_COVER;
        $plotCoverFile->path = $coverOutName;
        $plotCoverFile->web_path = Config::get('globals.MACHINE') . DIRECTORY_SEPARATOR . $plot->farm_id . DIRECTORY_SEPARATOR . "plot_cover_" . $plot->gid . ".png";
        $plotCoverFile->save();
    }

}
