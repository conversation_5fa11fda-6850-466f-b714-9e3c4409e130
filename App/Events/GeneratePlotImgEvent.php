<?php

namespace App\Events;

use Config;
use DB;
use View;
use File;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class GeneratePlotImgEvent implements IGeneratable
{
    use SerializesModels;

    /**
     * GeneratePlotImgEvent constructor.
     * @param string    $mapFile    Path to the mapfile source
     * @param array     $layers     Layers to be displayed
     * @param array     $extent     Coordinates for the image extent
     * @param string    $outImage   Path for the output image
     * @param integer   $width      Width of the image in px
     * @param integer   $height     Height of the image in px
     */
    public function __construct($mapFile, array $layers, array $extent, $outImage, $width, $height)
    {
        $this->mapFile = $mapFile;
        $this->layers = $layers;
        $this->extent = $extent;
        $this->outImage = $outImage;
        $this->width = $width;
        $this->height = $height;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {
        $extent = implode(" ", $this->extent);

        $parcelsLayer = implode(" ", array_keys($this->layers));

        $shp2img = Config::get('globals.SHP2IMG');
        $command = "{$shp2img} -m {$this->mapFile} -l \"{$parcelsLayer}\"";
        if ($this->width && $this->height) {
            $command .= " -s {$this->width} {$this->height}";
        }

        foreach ($this->layers as $layerName => $layerData) {
            if ($layerData != null) {
                $command .= " -d {$layerName} \"$layerData\"";
            }
        }

        if (!File::exists(pathinfo($this->outImage, PATHINFO_DIRNAME))) {
            File::makeDirectory(pathinfo($this->outImage, PATHINFO_DIRNAME));
        }

        $command .= " -e {$extent} -o \"{$this->outImage}\"";

        $output = array();
        exec($command . " 2>&1", $output);
    }
}
