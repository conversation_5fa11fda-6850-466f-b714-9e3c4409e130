<?php

namespace App\Events;

use Config;
use View;

use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class GenerateUserMapFileEvent implements IGeneratable
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($truncateOthers = true)
    {
        $this->truncateOthers = $truncateOthers;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }

    public function generate()
    {
        $this->createOrdersWorkMap();
        $this->createUploadPlotsBoundariesMap();
        $this->createEditPlotsBoundariesMap();

        if ($this->truncateOthers) {
            //create cov_layer if not exist
            fopen(Config::get('globals.WMS_MAP_PATH') . "layer_satellite_orders.map", 'w');
            fopen(Config::get('globals.WMS_MAP_PATH') . "layer_satellite_work.map", 'w');
            fopen(Config::get('globals.WMS_MAP_PATH') . "layer_plots_by_crop.map", 'w');
            fopen(Config::get('globals.WMS_MAP_PATH') . "layer_upload_plots_boundaries.map", 'w');
            fopen(Config::get('globals.WMS_MAP_PATH') . "layer_edit_plots_boundaries.map", 'w');
        }

    }

    private function getMapHead()
    {

        $data['wmsserver'] = Config::get('globals.WMS_SERVER');
        $data['wmssrs'] = Config::get('globals.WMS_SRS');
        $data['wfsserver'] = Config::get('globals.WMS_SERVER');
        $data['wfssrs'] = Config::get('globals.WMS_SRS');
        $data['epsg_code'] = Config::get('globals.DEFAULT_DB_CRS');
        $data['utm_zone'] = Config::get('globals.DEFAULT_UTM_ZONE');
        $data['geo_pole'] = Config::get('globals.GEO_POLE');

        return View::make('maps.head', $data)->render();
    }

    private function createOrdersWorkMap()
    {
        $mapStr = "MAP\r\n";

        $mapStr .= $this->getMapHead();
        $mapStr .= "\r\n";

        $mapStr .= "INCLUDE '" . Config::get('globals.WMS_MAP_PATH') . "layer_satellite_orders.map'\r\n";
        $mapStr .= "INCLUDE '" . Config::get('globals.WMS_MAP_PATH') . "layer_satellite_orders_old.map'\r\n";
        $mapStr .= "INCLUDE '" . Config::get('globals.WMS_MAP_PATH') . "layer_satellite_work.map'\r\n";
        $mapStr .= "INCLUDE '" . Config::get('globals.WMS_MAP_PATH') . "layer_plots_by_crop.map'\r\n";
        $mapStr .= "INCLUDE '" . Config::get('globals.WMS_MAP_PATH') . "layer_index.map'\r\n";
        $mapStr .= "INCLUDE '" . Config::get('globals.WMS_MAP_PATH') . "layer_index_water.map'\r\n";
        $mapStr .= "INCLUDE '" . Config::get('globals.WMS_MAP_PATH') . "layer_tile_idx.map'\r\n";
        $mapStr .= "INCLUDE '" . Config::get('globals.WMS_MAP_PATH') . "layer_tile_idx_mask.map'\r\n";

        $mapStr .= "END";

        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "orders_work.map", 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function createUploadPlotsBoundariesMap()
    {
        $mapStr = "MAP\r\n";

        $mapStr .= $this->getMapHead();
        $mapStr .= "\r\n";

        $mapStr .= "INCLUDE '" . Config::get('globals.WMS_MAP_PATH') . "layer_upload_plots_boundaries.map'\r\n";

        $mapStr .= "END";

        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "upload_plots_boundaries.map", 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }

    private function createEditPlotsBoundariesMap()
    {
        $mapStr = "MAP\r\n";

        $mapStr .= $this->getMapHead();
        $mapStr .= "\r\n";

        $mapStr .= "INCLUDE '" . Config::get('globals.WMS_MAP_PATH') . "layer_edit_plots_boundaries.map'\r\n";

        $mapStr .= "END";

        $fp = fopen(Config::get('globals.WMS_MAP_PATH') . "edit_plots_boundaries.map", 'w');
        fwrite($fp, $mapStr);
        fclose($fp);
    }
}
